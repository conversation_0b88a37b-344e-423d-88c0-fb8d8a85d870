"use client";

import { AlertTriangle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface MintNFTErrorProps {
  error: string | Error;
  onRetry: () => void;
}

export function MintNFTError({ error, onRetry }: MintNFTErrorProps) {
  const errorMessage = error instanceof Error ? error.message : error;

  return (
    <div className="flex flex-col items-center justify-center p-8 min-h-[300px] w-full bg-gradient-to-b from-gray-50 to-white dark:from-[#0c0916] dark:to-[#0f0a19] rounded-lg border border-gray-200 dark:border-gray-800/50">
      <div className="rounded-full bg-red-100 dark:bg-red-900/30 p-3 mb-4">
        <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
      </div>
      <p className="text-gray-800 dark:text-gray-200 text-center font-medium mb-6">
        {errorMessage}
      </p>
      <Button
        onClick={onRetry}
        className="bg-pink-500 dark:bg-pink-600 hover:bg-pink-600 dark:hover:bg-pink-700 text-white"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Try Again
      </Button>
    </div>
  );
}
