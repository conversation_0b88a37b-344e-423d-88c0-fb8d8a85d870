"use client";

import { ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

interface FooterBarProps {
  itemCount: string;
  sliderValue: number[];
  maxItems: number;
  handleItemCountChange: (value: string) => void;
  handleSliderChange: (value: number[]) => void;
  handleSliderDragStart: () => void;
  handleSliderDragEnd: () => void;
  openCart: () => void;
}

export default function FooterBar({
  itemCount,
  sliderValue,
  maxItems,
  handleItemCountChange,
  handleSliderChange,
  handleSliderDragStart,
  handleSliderDragEnd,
  openCart,
}: FooterBarProps) {
  const count = parseInt(itemCount) || 0;
  return (
    <div className="flex items-center justify-between p-3 border-t">
      <div className="flex items-center gap-3">
        <div className="flex items-center border rounded-md bg-muted/30 px-2 py-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() =>
              handleItemCountChange(Math.max(count - 1, 0).toString())
            }
            disabled={count <= 0}
          >
            -
          </Button>
          <div className="w-8 text-center font-semibold select-none">
            {count}
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() =>
              handleItemCountChange(Math.min(count + 1, maxItems).toString())
            }
            disabled={count >= maxItems}
          >
            +
          </Button>
        </div>
        <Slider
          value={sliderValue}
          onValueChange={handleSliderChange}
          onValueCommit={handleSliderDragEnd}
          max={100}
          step={1}
          className="w-50 mx-4 cursor-pointer"
          onPointerDown={handleSliderDragStart}
        />
      </div>
      
      <Button className="ml-auto bg-pink-600 hover:bg-pink-700">
        Connect wallet to buy
      </Button>
      <Button variant="outline" size="icon" className="ml-2" onClick={openCart}>
        <ShoppingCart className="h-4 w-4" />
      </Button>
    </div>
  );
}
