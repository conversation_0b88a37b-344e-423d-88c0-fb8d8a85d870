"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangle, RefreshCw } from "lucide-react";

interface CollectionCarouselErrorProps {
  error: string;
  onRetry?: () => void;
}

export function CollectionCarouselError({
  error,
  onRetry,
}: CollectionCarouselErrorProps) {
  return (
    <div className="w-full py-12 px-4">
      <div className="max-w-md mx-auto text-center">
        <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
          <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>

        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Failed to load collections
        </h3>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          {error ||
            "There was an error loading the NFT collections. Please try again later."}
        </p>

        {onRetry && (
          <Button
            onClick={onRetry}
            variant="outline"
            size="sm"
            className="inline-flex items-center border-red-200 dark:border-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/10"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
}
