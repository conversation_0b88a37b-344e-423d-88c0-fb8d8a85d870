import { Collection, Stats } from "@/lib/api/graphql/generated";
import { Chain } from "@/lib/constant/chains";
import { HomeState } from "@/types/collection.type";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: HomeState = {
  collections: [],
  stats: { artworks: "0", artists: "0", collectors: "0" },
  selectedChain: null,
  isLoading: false,
  error: null,
};

const homeSlice = createSlice({
  name: "home",
  initialState,
  reducers: {
    setChain: (state, action: PayloadAction<Chain | null>) => {
      state.selectedChain = action.payload;
    },
    setData: (
      state,
      action: PayloadAction<{ collections: Collection[]; stats: Stats }>
    ) => {
      state.collections = action.payload.collections;
      state.stats = action.payload.stats;
      state.isLoading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    addCollection: (state, action: PayloadAction<Collection>) => {
      state.collections.unshift(action.payload);
    },
    updateCollection: (state, action: PayloadAction<Collection>) => {
      state.collections = state.collections.map((col) =>
        col.id === action.payload.id ? { ...col, ...action.payload } : col
      );
    },
  },
});

export const {
  setChain,
  setData,
  setLoading,
  setError,
  addCollection,
  updateCollection,
} = homeSlice.actions;
export default homeSlice.reducer;
