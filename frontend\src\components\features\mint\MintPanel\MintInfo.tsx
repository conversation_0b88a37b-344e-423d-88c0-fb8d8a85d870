"use client";

import type React from "react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ChevronDown, ChevronUp } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useSelector } from "react-redux";
import type { RootState } from "@/store/store";
import { AttributeInput } from "@/lib/api/graphql/generated";
import { NftMetadataInput } from "@/types/mint.types";

// Import các component con
import { ImageUpload } from "./components/ImageUpload";
import { MetadataForm } from "./components/MetadataForm";
import { AllowlistForm } from "./components/AllowlistForm";
import { AttributesForm } from "./components/AttributesForm";
import { AdvancedOptions } from "./components/AdvancedOptions";

interface MintInfoProps {
  isSameArtType: boolean;
  agreedToTerms: boolean;
  setAgreedToTerms: (value: boolean) => void;
  name: string;
  setName: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  attributes: AttributeInput[];
  setAttributes: (value: AttributeInput[]) => void;
  amount: number;
  setAmount: (value: number) => void;
  royalty: number;
  setRoyalty: (value: number) => void;
  gasPrice?: string;
  setGasPrice: (value: string | undefined) => void;
  gasLimit?: string;
  setGasLimit: (value: string | undefined) => void;
  signature?: string;
  setSignature: (value: string | undefined) => void;
  nonce?: string;
  setNonce: (value: string | undefined) => void;
  nameError?: string;
  descriptionError?: string;
  attributesError?: string;
  imageFile?: string | null;
  setImageFile: (value: string | null) => void;
  handleFileUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  handleBatchUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  isUploading: boolean;
  setIsUploading: (value: boolean) => void;
  batchMetadata: NftMetadataInput[];
  setBatchMetadata: (
    value:
      | NftMetadataInput[]
      | ((prev: NftMetadataInput[]) => NftMetadataInput[])
  ) => void;
  isAllowlistMint: boolean;
}

const MintInfo: React.FC<MintInfoProps> = ({
  isSameArtType,
  agreedToTerms,
  setAgreedToTerms,
  name,
  setName,
  description,
  setDescription,
  attributes,
  setAttributes,
  amount,
  setAmount,
  royalty,
  setRoyalty,
  gasPrice,
  setGasPrice,
  gasLimit,
  setGasLimit,
  signature,
  setSignature,
  nonce,
  setNonce,
  nameError,
  descriptionError,
  attributesError,
  imageFile,
  setImageFile,
  handleFileUpload,
  handleBatchUpload,
  isUploading,
  setIsUploading,
  batchMetadata,
  setBatchMetadata,
  isAllowlistMint,
}) => {
  const collection = useSelector((state: RootState) => state.collection.data);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showAttributes, setShowAttributes] = useState(false);
  const [showMetadata, setShowMetadata] = useState(!isSameArtType);

  return (
    <div className="space-y-4">
      {/* Image Upload Section */}
      <ImageUpload
        isSameArtType={isSameArtType}
        collection={collection}
        imageFile={imageFile}
        setImageFile={setImageFile}
        handleFileUpload={handleFileUpload}
        handleBatchUpload={handleBatchUpload}
        isUploading={isUploading}
        batchMetadata={batchMetadata}
        setBatchMetadata={setBatchMetadata}
      />

      {/* Allowlist Mint Inputs */}
      <AllowlistForm
        isAllowlistMint={isAllowlistMint}
        signature={signature}
        setSignature={setSignature}
        nonce={nonce}
        setNonce={setNonce}
      />

      {/* Basic Metadata */}
      <Collapsible open={showMetadata}>
        <CollapsibleTrigger asChild>
          {isSameArtType && (
            <Button
              variant="ghost"
              className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
              onClick={() => setShowMetadata(!showMetadata)}
              aria-expanded={showMetadata}
            >
              <span>Customize Metadata</span>
              {showMetadata ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          <MetadataForm
            isSameArtType={isSameArtType}
            collection={collection}
            name={name}
            setName={setName}
            description={description}
            setDescription={setDescription}
            amount={amount}
            setAmount={setAmount}
            nameError={nameError}
            descriptionError={descriptionError}
            batchMetadata={batchMetadata}
          />
        </CollapsibleContent>
      </Collapsible>

      {/* Advanced Metadata */}
      <Collapsible open={showAttributes}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
            onClick={() => setShowAttributes(!showAttributes)}
            aria-expanded={showAttributes}
          >
            <span>Attributes (Optional)</span>
            {showAttributes ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          <AttributesForm
            attributes={attributes}
            setAttributes={setAttributes}
            attributesError={attributesError}
          />
        </CollapsibleContent>
      </Collapsible>

      {/* Advanced Options */}
      <Collapsible open={showAdvanced}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            aria-expanded={showAdvanced}
          >
            <span>Advanced Options</span>
            {showAdvanced ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          <AdvancedOptions
            royalty={royalty}
            setRoyalty={setRoyalty}
            gasPrice={gasPrice}
            setGasPrice={setGasPrice}
            gasLimit={gasLimit}
            setGasLimit={setGasLimit}
          />
        </CollapsibleContent>
      </Collapsible>

      {/* Terms Agreement */}
      <div className="flex items-start space-x-3 pt-2">
        <Checkbox
          id="terms"
          checked={agreedToTerms}
          onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
          className="data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500 border-gray-300 dark:data-[state=checked]:bg-pink-600 dark:data-[state=checked]:border-pink-600 dark:border-gray-800/50"
          aria-label="Agree to terms of service"
        />
        <Label htmlFor="terms" className="text-sm text-gray-400 leading-tight">
          By clicking mint, you agree to the{" "}
          <a
            href="#"
            className="text-pink-400 hover:underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            Terms of Service
          </a>
          .
        </Label>
      </div>
    </div>
  );
};

export default MintInfo;
