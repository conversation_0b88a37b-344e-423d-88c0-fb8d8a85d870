/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import type React from "react";
import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  Trash2,
  Upload,
  ImageIcon,
  ChevronDown,
  ChevronUp,
  X,
  CheckCircle2,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useSelector } from "react-redux";
import type { RootState } from "@/store/store";
import { AttributeInput, NftMetadataInput } from "@/lib/api/graphql/generated";
import { ScrollArea } from "@/components/ui/scroll-area";

interface MintInfoProps {
  isSameArtType: boolean;
  agreedToTerms: boolean;
  setAgreedToTerms: (value: boolean) => void;
  name: string;
  setName: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  attributes: AttributeInput[];
  setAttributes: (value: AttributeInput[]) => void;
  amount: number;
  setAmount: (value: number) => void;
  royalty: number;
  setRoyalty: (value: number) => void;
  gasPrice?: string;
  setGasPrice: (value: string | undefined) => void;
  gasLimit?: string;
  setGasLimit: (value: string | undefined) => void;
  signature?: string;
  setSignature: (value: string | undefined) => void;
  nonce?: string;
  setNonce: (value: string | undefined) => void;
  nameError?: string;
  descriptionError?: string;
  attributesError?: string;
  imageFile?: string | null;
  setImageFile: (value: string | null) => void;
  handleFileUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  handleBatchUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  isUploading: boolean;
  setIsUploading: (value: boolean) => void;
  batchMetadata: NftMetadataInput[];
  setBatchMetadata: (
    value:
      | NftMetadataInput[]
      | ((prev: NftMetadataInput[]) => NftMetadataInput[])
  ) => void;
  isAllowlistMint: boolean;
}

const MintInfo: React.FC<MintInfoProps> = ({
  isSameArtType,
  agreedToTerms,
  setAgreedToTerms,
  name,
  setName,
  description,
  setDescription,
  attributes,
  setAttributes,
  amount,
  setAmount,
  royalty,
  setRoyalty,
  gasPrice,
  setGasPrice,
  gasLimit,
  setGasLimit,
  signature,
  setSignature,
  nonce,
  setNonce,
  nameError,
  descriptionError,
  attributesError,
  imageFile,
  setImageFile,
  handleFileUpload,
  handleBatchUpload,
  isUploading,
  setIsUploading,
  batchMetadata,
  setBatchMetadata,
  isAllowlistMint,
}) => {
  const collection = useSelector((state: RootState) => state.collection.data);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showAttributes, setShowAttributes] = useState(false);
  const [showMetadata, setShowMetadata] = useState(!isSameArtType);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const validateRoyalty = useCallback((value: number) => {
    if (value < 0 || value > 50) {
      toast.error("Royalty must be between 0 and 50%");
      return false;
    }
    return true;
  }, []);

  const validateSignature = useCallback((value: string) => {
    if (!/^0x[0-9a-fA-F]{130}$/.test(value)) {
      toast.error("Invalid signature format (must be 65 bytes hex)");
      return false;
    }
    return true;
  }, []);

  const validateNonce = useCallback((value: string) => {
    const num = Number(value);
    if (!Number.isInteger(num) || num < 0) {
      toast.error("Nonce must be a non-negative integer");
      return false;
    }
    return true;
  }, []);

  const handleAttributeChange = useCallback(
    (index: number, field: "trait_type" | "value", value: string) => {
      const newAttributes = [...attributes];
      newAttributes[index][field] = value.slice(0, 50);
      setAttributes(newAttributes);
    },
    [attributes, setAttributes]
  );

  const removeAttribute = useCallback(
    (index: number) => {
      setAttributes(attributes.filter((_, i) => i !== index));
    },
    [attributes, setAttributes]
  );

  const handleBatchMetadataChange = (
    index: number,
    field: keyof NftMetadataInput,
    value: string
  ) => {
    setBatchMetadata((prev: NftMetadataInput[]) =>
      prev.map((meta, i) => (i === index ? { ...meta, [field]: value } : meta))
    );
  };

  return (
    <div className="space-y-4">
      {/* Thông báo cho SAME */}
      {isSameArtType && (
        <p className="text-sm text-gray-300">
          Using collection image:{" "}
          <span className="font-medium">{collection?.image}</span>. Customize
          name, description, or attributes if needed (defaults to collection
          metadata).
        </p>
      )}

      {/* Image Upload Section */}
      {!isSameArtType && (
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Label className="text-sm text-white flex items-center gap-1">
              Image <span className="text-pink-500">*</span>
            </Label>
            <span className="text-sm text-gray-400">
              Required for Unique Art
            </span>
          </div>

          <Tabs defaultValue="single" className="w-full">
            <TabsList className="grid grid-cols-2 w-full bg-gray-50 border border-gray-200 dark:bg-[#0f0a19] dark:border-gray-800/50">
              <TabsTrigger
                value="single"
                className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-sm"
              >
                Single Upload
              </TabsTrigger>
              <TabsTrigger
                value="batch"
                className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-sm"
              >
                Batch Upload
              </TabsTrigger>
            </TabsList>

            <TabsContent value="single">
              <div className="relative mt-2">
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/png,image/jpeg,image/jpg,image/gif,image/webp"
                  onChange={handleFileUpload}
                  className="hidden"
                  aria-label="Upload single image"
                />
                <Button
                  type="button"
                  variant="outline"
                  className="w-full h-10 border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-800/50 text-sm"
                  onClick={() =>
                    document.getElementById("image-upload")?.click()
                  }
                  disabled={isUploading}
                >
                  <ImageIcon className="mr-2 h-4 w-4" />
                  Choose Image
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="batch">
              <div className="relative mt-2">
                <Input
                  id="batch-upload"
                  type="file"
                  multiple
                  accept="image/png,image/jpeg,image/jpg,image/gif,image/webp"
                  onChange={handleBatchUpload}
                  className="hidden"
                  aria-label="Upload multiple images"
                />
                <Button
                  type="button"
                  variant="outline"
                  className="w-full h-10 border-gray-800/50 text-gray-300 hover:bg-gray-800/50 text-sm"
                  onClick={() =>
                    document.getElementById("batch-upload")?.click()
                  }
                  disabled={isUploading}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Choose Images (Max 10)
                </Button>
              </div>
            </TabsContent>
          </Tabs>

          {isUploading && (
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin text-pink-500" />
              <span className="text-sm text-gray-300">Uploading...</span>
            </div>
          )}

          {imageFile && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Uploaded Image</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setImageFile(null);
                    setPreviewUrl(null);
                  }}
                  className="text-sm text-gray-400 hover:text-red-400"
                >
                  Clear
                </Button>
              </div>
              <div className="relative h-32 w-full rounded-md overflow-hidden border border-gray-800/50">
                <Image
                  src={imageFile || "/placeholder.svg"}
                  alt="NFT preview"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          )}

          {batchMetadata.length > 0 && (
            <div className="p-3 bg-purple-600/20 border border-purple-600/50 rounded-md">
              <div className="flex justify-between items-center mb-2">
                <p className="text-sm text-purple-300 font-medium">
                  {batchMetadata.length} images uploaded for batch mint
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setBatchMetadata([])}
                  className="text-sm text-gray-400 hover:text-red-400"
                >
                  Clear All
                </Button>
              </div>
              <div className="space-y-4">
                {batchMetadata.map((meta, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 bg-[#0f0a19] rounded-md border border-gray-800/50"
                  >
                    <div className="relative h-16 w-16 rounded overflow-hidden border border-purple-600/50">
                      <Image
                        src={meta.image || "/placeholder.svg"}
                        alt={meta.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-1 space-y-2">
                      <div>
                        <Label
                          htmlFor={`batch-name-${index}`}
                          className="text-sm text-white"
                        >
                          Name
                        </Label>
                        <Input
                          id={`batch-name-${index}`}
                          value={meta.name}
                          onChange={(e) =>
                            handleBatchMetadataChange(
                              index,
                              "name",
                              e.target.value.slice(0, 100)
                            )
                          }
                          placeholder="NFT Name"
                          className="h-9 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                        />
                      </div>
                      <div>
                        <Label
                          htmlFor={`batch-amount-${index}`}
                          className="text-sm text-white"
                        >
                          Amount (1-{collection?.mintLimit || 100})
                        </Label>
                        <Input
                          id={`batch-amount-${index}`}
                          type="number"
                          value={meta.amount}
                          onChange={(e) =>
                            handleBatchMetadataChange(
                              index,
                              "amount",
                              Math.max(
                                1,
                                Math.min(
                                  Number(collection?.mintLimit || 100),
                                  Number(e.target.value)
                                )
                              ).toString()
                            )
                          }
                          min={1}
                          max={collection?.mintLimit || 100}
                          className="h-9 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                        />
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        setBatchMetadata(
                          batchMetadata.filter((_, i) => i !== index)
                        )
                      }
                      className="h-9 w-9 text-gray-400 hover:text-red-400 hover:bg-red-900/20"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Allowlist Mint Inputs */}
      {isAllowlistMint && (
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Label className="text-sm text-white flex items-center gap-1">
              Allowlist Mint Credentials{" "}
              <span className="text-pink-500">*</span>
            </Label>
            <span className="text-sm text-gray-400">Required</span>
          </div>
          <div className="space-y-2">
            <div className="relative">
              <Input
                id="signature"
                value={signature || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  setSignature(value);
                  if (value && !validateSignature(value)) {
                    setSignature(undefined);
                  }
                }}
                placeholder="Signature (0x...)"
                className={cn(
                  "h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white placeholder:text-gray-600 dark:bg-[#0a0612] dark:border-gray-900/50",
                  signature &&
                    !/^0x[0-9a-fA-F]{130}$/.test(signature) &&
                    "border-red-800 focus-visible:ring-red-800"
                )}
                aria-invalid={
                  signature && !validateSignature(signature) ? "true" : "false"
                }
              />
              {signature && validateSignature(signature) && (
                <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
              )}
            </div>
            <p className="text-sm text-gray-400">
              65-byte hex signature provided by the allowlist
            </p>
          </div>
          <div className="space-y-2">
            <div className="relative">
              <Input
                id="nonce"
                value={nonce || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  setNonce(value);
                  if (value && !validateNonce(value)) {
                    setNonce(undefined);
                  }
                }}
                placeholder="Nonce"
                type="number"
                min={0}
                className={cn(
                  "h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white placeholder:text-gray-600 dark:bg-[#0a0612] dark:border-gray-900/50",
                  nonce &&
                    (!Number.isInteger(Number(nonce)) || Number(nonce) < 0) &&
                    "border-red-800 focus-visible:ring-red-800"
                )}
                aria-invalid={nonce && !validateNonce(nonce) ? "true" : "false"}
              />
              {nonce && validateNonce(nonce) && (
                <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
              )}
            </div>
            <p className="text-sm text-gray-400">
              Nonce value provided by the allowlist
            </p>
          </div>
        </div>
      )}

      {/* Basic Metadata */}
      <Collapsible open={showMetadata}>
        <CollapsibleTrigger asChild>
          {isSameArtType && (
            <Button
              variant="ghost"
              className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
              onClick={() => setShowMetadata(!showMetadata)}
              aria-expanded={showMetadata}
            >
              <span>Customize Metadata</span>
              {showMetadata ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          )}
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          {/* Name Field */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label
                htmlFor="name"
                className="text-sm text-white flex items-center gap-1"
              >
                Name{" "}
                {!isSameArtType && <span className="text-pink-500">*</span>}
              </Label>
              <span className="text-sm text-gray-400">
                {isSameArtType ? "Optional" : "Required"}
              </span>
            </div>
            <div className="relative">
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value.slice(0, 100))}
                placeholder={
                  isSameArtType ? `${collection?.name || "NFT"}` : "NFT Name"
                }
                className={cn(
                  "h-10 text-sm bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 dark:bg-[#0f0a19] dark:border-gray-800/50 dark:text-white dark:placeholder:text-gray-600",
                  nameError &&
                    "border-red-500 focus-visible:ring-red-500 dark:border-red-800 dark:focus-visible:ring-red-800"
                )}
                aria-invalid={nameError ? "true" : "false"}
              />
              {!nameError && name.trim() && (
                <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
              )}
            </div>
            {nameError ? (
              <p className="text-sm text-red-400">{nameError}</p>
            ) : (
              <p className="text-sm text-gray-400">
                Max 100 characters
                {isSameArtType &&
                  `. Defaults to "${
                    collection?.name || "NFT"
                  } #<number>" if empty.`}
              </p>
            )}
          </div>

          {/* Description Field */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label
                htmlFor="description"
                className="text-sm text-white flex items-center gap-1"
              >
                Description{" "}
                {!isSameArtType && <span className="text-pink-500">*</span>}
              </Label>
              <span className="text-sm text-gray-400">
                {isSameArtType ? "Optional" : "Required"}
              </span>
            </div>
            <div className="relative">
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value.slice(0, 1000))}
                placeholder={
                  isSameArtType
                    ? collection?.description || "Describe your NFT"
                    : "Describe your NFT"
                }
                className={cn(
                  "h-20 text-sm bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 dark:bg-[#0f0a19] dark:border-gray-800/50 dark:text-white dark:placeholder:text-gray-600",
                  descriptionError &&
                    "border-red-500 focus-visible:ring-red-500 dark:border-red-800 dark:focus-visible:ring-red-800"
                )}
                aria-invalid={descriptionError ? "true" : "false"}
              />
              {!descriptionError && description.trim() && (
                <CheckCircle2 className="absolute right-2 top-2 h-4 w-4 text-green-500" />
              )}
            </div>
            {descriptionError ? (
              <p className="text-sm text-red-400">{descriptionError}</p>
            ) : (
              <p className="text-sm text-gray-400">
                Max 1000 characters
                {isSameArtType &&
                  `. Defaults to collection description if empty.`}
              </p>
            )}
          </div>

          {/* Amount Field */}
          {(!batchMetadata.length || isSameArtType) && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="amount" className="text-sm text-white">
                  Amount (1-{collection?.mintLimit || 100})
                </Label>
                <span className="text-sm text-gray-400">
                  Number of NFTs to mint
                </span>
              </div>
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) =>
                  setAmount(
                    Math.max(
                      1,
                      Math.min(
                        Number(collection?.mintLimit || 100),
                        Number(e.target.value)
                      )
                    )
                  )
                }
                min={1}
                max={collection?.mintLimit || 100}
                className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                aria-label="Number of NFTs to mint"
              />
              <p className="text-sm text-gray-400">
                Number of NFTs to mint in this transaction.{" "}
                {isSameArtType
                  ? "All NFTs share the same metadata."
                  : "Each NFT requires unique metadata (use Batch Upload for multiple NFTs)."}
              </p>
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Advanced Metadata */}
      <Collapsible open={showAttributes}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
            onClick={() => setShowAttributes(!showAttributes)}
            aria-expanded={showAttributes}
          >
            <span>Attributes (Optional)</span>
            {showAttributes ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          <ScrollArea className="h-[150px]">
            {attributes.map((attr, index) => (
              <div key={index} className="flex gap-2 items-center">
                <Input
                  value={attr.trait_type}
                  onChange={(e) =>
                    handleAttributeChange(index, "trait_type", e.target.value)
                  }
                  placeholder="Trait Type"
                  className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                  aria-label={`Trait type ${index + 1}`}
                />
                <Input
                  value={attr.value}
                  onChange={(e) =>
                    handleAttributeChange(index, "value", e.target.value)
                  }
                  placeholder="Value"
                  className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                  aria-label={`Trait value ${index + 1}`}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeAttribute(index)}
                  className="h-10 w-10 text-gray-400 hover:text-red-400 hover:bg-red-900/20"
                  aria-label={`Remove attribute ${index + 1}`}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}

            {attributesError ? (
              <p className="text-sm text-red-400">{attributesError}</p>
            ) : (
              <p className="text-sm text-gray-400">
                Trait types must be unique. Both fields are required for each
                attribute. Max 10 attributes.
              </p>
            )}
          </ScrollArea>

          <Button
            onClick={() =>
              attributes.length < 10 &&
              setAttributes([...attributes, { trait_type: "", value: "" }])
            }
            disabled={attributes.length >= 10}
            variant="outline"
            className="w-full h-10 border-gray-800/50 text-gray-300 hover:bg-gray-800/50 text-sm"
            aria-label="Add new attribute"
          >
            Add Attribute
          </Button>
        </CollapsibleContent>
      </Collapsible>

      {/* Advanced Options */}
      <Collapsible open={showAdvanced}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full flex justify-between text-gray-300 hover:bg-gray-800/50 text-sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            aria-expanded={showAdvanced}
          >
            <span>Advanced Options</span>
            {showAdvanced ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3">
          {/* Royalty Field */}
          <div className="space-y-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Label
                    htmlFor="royalty"
                    className="text-sm text-white flex items-center gap-1"
                  >
                    Royalty (%) (0-50)
                  </Label>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">
                    Percentage of secondary sales you&apos;ll receive
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Input
              id="royalty"
              type="number"
              value={royalty}
              onChange={(e) => {
                const value = Number(e.target.value);
                if (validateRoyalty(value)) {
                  setRoyalty(value);
                }
              }}
              min={0}
              max={50}
              className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
              aria-label="Royalty percentage"
            />
            <p className="text-sm text-gray-400">
              Percentage of secondary sales you&apos;ll receive
            </p>
          </div>

          {/* Gas Settings */}
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Label htmlFor="gasPrice" className="text-sm text-white">
                      Gas Price (Gwei)
                    </Label>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">
                      Price per unit of gas. Leave as Auto for default.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Input
                id="gasPrice"
                value={gasPrice || ""}
                onChange={(e) => setGasPrice(e.target.value || undefined)}
                placeholder="Auto"
                className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                aria-label="Gas price in Gwei"
              />
            </div>

            <div className="space-y-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Label htmlFor="gasLimit" className="text-sm text-white">
                      Gas Limit
                    </Label>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">
                      Maximum gas units for transaction. Leave as Auto for
                      default.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Input
                id="gasLimit"
                value={gasLimit || ""}
                onChange={(e) => setGasLimit(e.target.value || undefined)}
                placeholder="Auto"
                className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                aria-label="Gas limit"
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Terms Agreement */}
      <div className="flex items-start space-x-3 pt-2">
        <Checkbox
          id="terms"
          checked={agreedToTerms}
          onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
          className="data-[state=checked]:bg-pink-500 data-[state=checked]:border-pink-500 border-gray-300 dark:data-[state=checked]:bg-pink-600 dark:data-[state=checked]:border-pink-600 dark:border-gray-800/50"
          aria-label="Agree to terms of service"
        />
        <Label htmlFor="terms" className="text-sm text-gray-400 leading-tight">
          By clicking mint, you agree to the{" "}
          <a
            href="#"
            className="text-pink-400 hover:underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            Terms of Service
          </a>
          .
        </Label>
      </div>
    </div>
  );
};

export default MintInfo;
