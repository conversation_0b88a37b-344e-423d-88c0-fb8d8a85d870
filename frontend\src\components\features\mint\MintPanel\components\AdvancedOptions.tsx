"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useMintValidation } from "@/hooks/useMintValidation";
import { toast } from "sonner";

interface AdvancedOptionsProps {
  royalty: number;
  setRoyalty: (royalty: number) => void;
  gasPrice?: string;
  setGasPrice: (gasPrice: string | undefined) => void;
  gasLimit?: string;
  setGasLimit: (gasLimit: string | undefined) => void;
}

export function AdvancedOptions({
  royalty,
  setRoyalty,
  gasPrice,
  setGasPrice,
  gasLimit,
  setGasLimit,
}: AdvancedOptionsProps) {
  const { validateRoyalty } = useMintValidation({
    address: "",
    collection: null,
    isSameArtType: false,
    isAllowlistMint: false,
    amount: 1,
    attributes: [],
    name: "",
    description: "",
    batchMetadata: [],
  });

  const handleRoyaltyChange = (value: number) => {
    if (validateRoyalty(value)) {
      setRoyalty(value);
    } else {
      toast.error("Royalty must be between 0 and 50%");
    }
  };

  return (
    <div className="space-y-3">
      {/* Royalty Field */}
      <div className="space-y-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Label
                htmlFor="royalty"
                className="text-sm text-white flex items-center gap-1"
              >
                Royalty (%) (0-50)
              </Label>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">
                Percentage of secondary sales you&apos;ll receive
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Input
          id="royalty"
          type="number"
          value={royalty}
          onChange={(e) => handleRoyaltyChange(Number(e.target.value))}
          min={0}
          max={50}
          className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
          aria-label="Royalty percentage"
        />
        <p className="text-sm text-gray-400">
          Percentage of secondary sales you&apos;ll receive
        </p>
      </div>

      {/* Gas Settings */}
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Label htmlFor="gasPrice" className="text-sm text-white">
                  Gas Price (Gwei)
                </Label>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">
                  Price per unit of gas. Leave as Auto for default.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Input
            id="gasPrice"
            value={gasPrice || ""}
            onChange={(e) => setGasPrice(e.target.value || undefined)}
            placeholder="Auto"
            className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
            aria-label="Gas price in Gwei"
          />
        </div>

        <div className="space-y-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Label htmlFor="gasLimit" className="text-sm text-white">
                  Gas Limit
                </Label>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">
                  Maximum gas units for transaction. Leave as Auto for
                  default.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Input
            id="gasLimit"
            value={gasLimit || ""}
            onChange={(e) => setGasLimit(e.target.value || undefined)}
            placeholder="Auto"
            className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
            aria-label="Gas limit"
          />
        </div>
      </div>
    </div>
  );
}
