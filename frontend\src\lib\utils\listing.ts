/* eslint-disable @typescript-eslint/no-explicit-any */
import { ethers } from "ethers";
import { EIP712Domain, EIP712SignData } from "@/types/eip712";
import { toast } from "sonner";

// Validation utilities
export const validateDomain = (
  domain: EIP712Domain,
  requiredFields: string[]
): void => {
  const missingFields = requiredFields.filter(
    (field) => !(domain as any)[field]
  );
  if (missingFields.length > 0) {
    throw new Error(
      `Missing required domain fields: ${missingFields.join(", ")}`
    );
  }
};

export const validateWallet = (
  expectedWallet: string,
  actualWallet: string
): void => {
  if (expectedWallet.toLowerCase() !== actualWallet.toLowerCase()) {
    throw new Error(
      `Wallet mismatch: expected ${expectedWallet}, got ${actualWallet}`
    );
  }
};

export const validateChainId = (
  expectedChainId: number,
  actualChainId: number | undefined
): void => {
  if (!actualChainId) {
    throw new Error("Chain ID is not defined");
  }
  if (expectedChainId !== actualChainId) {
    throw new Error(
      `Chain ID mismatch: expected ${expectedChainId}, got ${actualChainId}`
    );
  }
};

export const validateMessage = (message: any): void => {
  if (!ethers.isAddress(message.seller)) {
    throw new Error(`Invalid seller address: ${message.seller}`);
  }
  if (!ethers.isAddress(message.contractAddress)) {
    throw new Error(`Invalid contract address: ${message.contractAddress}`);
  }
  if (message.tokenIds.length === 0) {
    throw new Error("No token IDs provided");
  }
  if (
    !Number.isInteger(Number(message.standard)) ||
    Number(message.standard) < 0 ||
    Number(message.standard) > 255
  ) {
    throw new Error(`Invalid standard: ${message.standard}`);
  }
};

// EIP-712 utilities
export const getEIP712Types = () => ({
  OffchainListing: [
    { name: "seller", type: "address" },
    { name: "contractAddress", type: "address" },
    { name: "tokenIds", type: "uint256[]" },
    { name: "amounts", type: "uint256[]" },
    { name: "prices", type: "uint256[]" },
    { name: "standard", type: "uint8" },
    { name: "expiryTimestamp", type: "uint256" },
    { name: "nonce", type: "uint256" },
    { name: "metadataURIsHash", type: "bytes32" },
  ],
});

export const constructMessage = (signData: EIP712SignData) => ({
  seller: signData.value.seller,
  contractAddress: signData.value.contractAddress,
  tokenIds: signData.value.tokenIds.map((id: string) => id),
  amounts: signData.value.amounts.map((amount: string) => amount),
  prices: signData.value.prices.map((price: string) => price),
  standard: signData.value.standard,
  expiryTimestamp: signData.value.expiryTimestamp,
  nonce: signData.value.nonce,
  metadataURIsHash: signData.value.metadataURIsHash,
});

// Retry utility
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number,
  delayMs: number
): Promise<T> => {
  let lastError: Error | undefined;
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      console.error(`Attempt ${i + 1} failed:`, {
        message: lastError.message,
        details: (error as any).details,
      });
      if (i < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, delayMs * (i + 1)));
      }
    }
  }
  throw lastError || new Error("Unknown error during retry");
};

export const validatePrice = (price: string): boolean => {
  if (!price || price.trim() === "") return false;
  const priceNum = parseFloat(price);
  return !isNaN(priceNum) && priceNum > 0 && priceNum <= 100;
};

export const handleListingError = (error: unknown, context: string) => {
  const message = (error as Error).message || "Lỗi không xác định";
  toast.error(`${context}: ${message}`);
};
