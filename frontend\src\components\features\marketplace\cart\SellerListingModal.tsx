/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { BaseModalProps, ModalContainer } from "./CommonComponents";
import {
  useGetPriceListingQuery,
  useListNfTsMutation,
  PriceType,
  useConfirmListingNftMutation,
  Step,
  ListingError,
  TokenStandard,
  GetNonceListingDocument,
} from "@/lib/api/graphql/generated";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useExecuteTransaction } from "@/hooks/useExecuteTransaction";
import { toast } from "sonner";
import { useAccount, useWalletClient } from "wagmi";
import { BrowserProvider, ethers } from "ethers";
import { MARKETPLACE_CONTRACT } from "@/lib/abi/contracts";
import { ERC721 } from "@/lib/abi/ERC721";
import { ERC1155 } from "@/lib/abi/ERC1155";
import client from "@/lib/api/apolloClient";
import { useListingSteps } from "@/hooks/useListingSteps";
import { validatePrice } from "@/lib/utils/listing";
import { handleListingError } from "@/lib/utils/listing";
import { ListingSteps } from "@/components/features/marketplace/cart/ListingSteps";
import { PriceTypeSelector } from "@/components/features/marketplace/cart/PriceTypeSelector";
import { PriceInput } from "@/components/features/marketplace/cart/PriceInput";
import { NftItemList } from "@/components/features/marketplace/cart/NftItemList";
import { ListingSummary } from "@/components/features/marketplace/cart/ListingSummary";

export interface SellerListingModalProps extends BaseModalProps {
  onList: () => void;
  onClearAllItems?: () => void;
}

export default function SellerListingModal({
  open,
  onOpenChange,
  items,
  onRemoveItem,
  onList,
  onClearAllItems,
}: SellerListingModalProps) {
  const { collection } = useSelector((state: RootState) => state.marketplace);
  const { address, chainId } = useAccount();
  const { data: walletClient } = useWalletClient();
  const [globalPrice, setGlobalPrice] = useState("");
  const [itemPrices, setItemPrices] = useState<{ [id: string]: string }>({});
  const [activeListType, setActiveListType] = useState<PriceType | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [needsApproval, setNeedsApproval] = useState(false);
  const [contractStandard, setContractStandard] =
    useState<TokenStandard | null>(null);
  const [listNFTs] = useListNfTsMutation();
  const { executeTransaction } = useExecuteTransaction();
  const [confirmListingNft] = useConfirmListingNftMutation();

  const { setCurrentStep, showSteps, setShowSteps, steps } =
    useListingSteps(needsApproval);

  // Fetch price data
  const { data: priceData, loading: priceLoading } = useGetPriceListingQuery({
    variables: {
      input: {
        chainId: collection?.chainId || "",
        contractAddress: collection?.contractAddress || "",
        priceType:
          activeListType === PriceType.FloorPrice
            ? PriceType.FloorPrice
            : PriceType.LastedPrice,
      },
    },
    skip: !collection || !activeListType || items.length === 0,
  });

  useEffect(() => {
    if (activeListType && priceData?.getPriceListing && !priceLoading) {
      const priceInfo = priceData.getPriceListing;
      if (priceInfo?.success && priceInfo.listingPrice) {
        const price = ethers.formatEther(priceInfo.listingPrice);
        const newPrices: { [id: string]: string } = {};
        items.forEach((item) => {
          newPrices[item.id] = price;
        });
        setItemPrices(newPrices);
        setGlobalPrice(price);
      }
    }
  }, [priceData, activeListType, items, priceLoading]);

  useEffect(() => {
    if (globalPrice) {
      const newPrices: { [id: string]: string } = {};
      items.forEach((item) => {
        newPrices[item.id] = globalPrice;
      });
      setItemPrices(newPrices);
    }
  }, [globalPrice, items]);

  useEffect(() => {
    const checkContractAndApproval = async () => {
      if (
        !address ||
        !collection?.contractAddress ||
        !chainId ||
        !walletClient
      ) {
        return;
      }
      try {
        const provider = new BrowserProvider(walletClient.transport);
        const interfaceContract = new ethers.Contract(
          collection.contractAddress,
          [
            {
              constant: true,
              inputs: [{ name: "interfaceId", type: "bytes4" }],
              name: "supportsInterface",
              outputs: [{ name: "", type: "bool" }],
              payable: false,
              stateMutability: "view",
              type: "function",
            },
          ],
          provider
        );

        const isERC721 = await interfaceContract.supportsInterface(
          "0x80ac58cd"
        );
        const isERC1155 = await interfaceContract.supportsInterface(
          "0xd9b67a26"
        );

        if (isERC721 && !isERC1155) {
          setContractStandard(TokenStandard.Erc721);
          const erc721Contract = new ethers.Contract(
            collection.contractAddress,
            ERC721.abi,
            provider
          );
          const isApproved = await erc721Contract.isApprovedForAll(
            address,
            MARKETPLACE_CONTRACT.Marketplace
          );
          setNeedsApproval(!isApproved);
        } else if (isERC1155 && !isERC721) {
          setContractStandard(TokenStandard.Erc1155);
          const erc1155Contract = new ethers.Contract(
            collection.contractAddress,
            ERC1155.abi,
            provider
          );
          const isApproved = await erc1155Contract.isApprovedForAll(
            address,
            MARKETPLACE_CONTRACT.Marketplace
          );
          setNeedsApproval(!isApproved);
        } else {
          throw new Error("Contract does not support ERC721 or ERC1155");
        }
      } catch (error) {
        handleListingError(error, "Error checking contract or approval");
      }
    };
    checkContractAndApproval();
  }, [address, collection, chainId, walletClient]);

  if (!open || !contractStandard) return null;

  const validateInputs = (): boolean => {
    const newErrors: { [key: string]: string } = {};
    let isValid = true;

    if (!globalPrice || !validatePrice(globalPrice)) {
      newErrors.globalPrice = "Vui lòng nhập giá hợp lệ (0 < giá ≤ 100 ETH)";
      isValid = false;
    }

    Object.entries(itemPrices).forEach(([id, price]) => {
      if (!price || !validatePrice(price)) {
        newErrors[`item_${id}`] =
          "Vui lòng nhập giá hợp lệ (0 < giá ≤ 100 ETH)";
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleListClick = async () => {
    if (!validateInputs()) {
      toast.error("Lỗi xác thực: " + Object.values(errors).join(", "));
      return;
    }

    if (!address || !chainId || !collection || !walletClient) {
      toast.error(
        "Ví chưa được kết nối hoặc thông tin collection không đầy đủ"
      );
      return;
    }

    const listingInput = items.map((item) => {
      const price = itemPrices[item.id];
      if (!ethers.isAddress(collection.contractAddress)) {
        throw new Error(
          `Địa chỉ hợp đồng không hợp lệ: ${collection.contractAddress}`
        );
      }
      if (!/^\d+$/.test(item.tokenId)) {
        throw new Error(`Token ID không hợp lệ: ${item.tokenId}`);
      }
      if (!validatePrice(price)) {
        throw new Error(`Giá không hợp lệ cho token ${item.tokenId}: ${price}`);
      }
      return {
        tokenId: item.tokenId,
        contractAddress: collection.contractAddress,
        chainId: collection.chainId.toString(),
        listingPrice: price,
        standard: (contractStandard === TokenStandard.Erc721
          ? 0
          : 1
        ).toString(),
        amount: "1",
        metadataURI: item.tokenUri,
        listingType: "offchain",
        expiryTimestamp: (
          Math.floor(Date.now() / 1000) +
          30 * 24 * 60 * 60
        ).toString(),
      };
    });

    setIsLoading(true);
    try {
      setShowSteps(true);
      setCurrentStep(1);

      if (needsApproval) {
        toast.info("Vui lòng xác nhận phê duyệt quyền quản lý NFT trong ví");
        const provider = new BrowserProvider(walletClient.transport);
        const abi =
          contractStandard === TokenStandard.Erc721 ? ERC721.abi : ERC1155.abi;
        const nftContract = new ethers.Contract(
          collection.contractAddress,
          abi,
          provider
        );
        const data = nftContract.interface.encodeFunctionData(
          "setApprovalForAll",
          [MARKETPLACE_CONTRACT.Marketplace, true]
        );
        const step: Step = {
          id: "approve",
          params: JSON.stringify({
            to: collection.contractAddress,
            data,
            value: "0",
            chainId: Number(chainId),
          }),
        };
        await executeTransaction([step]);
        setNeedsApproval(false);
        setCurrentStep(2);
      } else {
        setCurrentStep(2);
      }

      const result = await listNFTs({
        variables: {
          input: {
            items: listingInput,
            walletAddress: address,
            chainId: collection.chainId.toString(),
          },
        },
      });

      const response = result.data?.listNFTs;
      if (!response) {
        throw new Error("Không nhận được phản hồi từ listNFTs mutation");
      }

      if (response.errors?.length) {
        response.errors.forEach((err: ListingError) => {
          const message = `${err.message} (${err.contractAddress}/${err.tokenId})`;
          toast.error(message);
          setErrors((prev) => ({
            ...prev,
            [`item_${err.tokenId}`]: message,
          }));
        });

        setTimeout(() => {
          setShowSteps(false);
          setCurrentStep(1);
          setIsLoading(false);
          onList();
          onOpenChange(false);
        }, 2000);

        return;
      }

      if (
        response.status === "full_success" ||
        response.status === "partial_success"
      ) {
        if (!response.steps.length) {
          throw new Error("Không có bước liệt kê nào trong phản hồi");
        }

        setCurrentStep(3);
        toast.info("Vui lòng xác nhận chữ ký EIP-712 trong ví");
        const { signature } = await executeTransaction(response.steps);

        setCurrentStep(4);
        const marketplaceAddress = MARKETPLACE_CONTRACT.Marketplace;
        if (!marketplaceAddress) {
          throw new Error(
            `Không tìm thấy địa chỉ hợp đồng cho chain ${chainId}`
          );
        }

        const { data: nonceData } = await client.query({
          query: GetNonceListingDocument,
          variables: {
            input: {
              wallet: address,
              chainId: collection.chainId.toString(),
              contractAddress: collection.contractAddress,
            },
          },
        });

        if (!nonceData?.getNonceListing?.nonce) {
          throw new Error("Không tìm thấy nonce");
        }

        const { data, errors } = await confirmListingNft({
          variables: {
            input: {
              listings: listingInput,
              txHash: "0x",
              signature,
              walletAddress: address,
              chainId: collection.chainId.toString(),
              nonce: nonceData.getNonceListing.nonce,
            },
          },
        });

        if (errors || !data?.confirmListingNft?.success) {
          const errorMessage =
            errors?.map((e) => e.message).join(", ") ||
            "Không thể xác nhận danh sách ở backend";
          throw new Error(errorMessage);
        }

        toast.success("Listing successfully");
        setTimeout(() => {
          setShowSteps(false);
          setCurrentStep(1);
          setIsLoading(false);
          onList();
          onOpenChange(false);
        }, 2000);
      } else {
        throw new Error("Liệt kê thất bại");
      }
    } catch (error) {
      handleListingError(error, "Lỗi khi liệt kê NFT");
    }
  };

  const handleItemPriceChange = (id: string, value: string) => {
    setItemPrices((prev) => ({
      ...prev,
      [id]: value,
    }));
    if (errors[`item_${id}`]) {
      setErrors((prev) => ({ ...prev, [`item_${id}`]: "" }));
    }
  };

  return (
    <ModalContainer
      title="List Items"
      itemCount={items.length}
      onClose={() => onOpenChange(false)}
      onClearAll={onClearAllItems}
      showClearButton={true}
    >
      {showSteps ? (
        <ListingSteps steps={steps} />
      ) : (
        <>
          <PriceTypeSelector
            activeType={activeListType}
            onTypeChange={setActiveListType}
            loading={priceLoading}
            disabled={isLoading}
          />

          <PriceInput
            value={globalPrice}
            onChange={(value) => {
              setGlobalPrice(value);
              if (errors.globalPrice) {
                setErrors((prev) => ({ ...prev, globalPrice: "" }));
              }
            }}
            error={errors.globalPrice}
            disabled={isLoading}
          />

          <NftItemList
            items={items}
            prices={itemPrices}
            globalPrice={globalPrice}
            onPriceChange={handleItemPriceChange}
            onRemoveItem={onRemoveItem}
            errors={errors}
          />

          <ListingSummary totalPrice={globalPrice || "0.0"} />

          <Button
            className="w-full bg-pink-600 hover:bg-pink-700 text-lg h-12 rounded-xl font-bold mt-2 shadow-lg transition"
            onClick={handleListClick}
            disabled={items.length === 0 || !address || isLoading}
          >
            {isLoading ? "Processing..." : "Submit Listings"}
          </Button>
          <div className="text-xs text-center text-muted-foreground mt-3">
            By clicking &quot;Submit Listings&quot;, you agree to the{" "}
            <a href="#" className="text-primary hover:underline">
              Marketplace Terms of Service
            </a>
            .
          </div>
        </>
      )}
    </ModalContainer>
  );
}
