import { NftItem } from "@/components/features/marketplace/cart/CommonComponents";
import { Nft } from "@/lib/api/graphql/generated";

export interface NftItemListProps {
  items: Nft[];
  prices: { [id: string]: string };
  globalPrice: string;
  onPriceChange: (id: string, value: string) => void;
  onRemoveItem: (id: string) => void;
  errors: { [key: string]: string };
}

export const NftItemList = ({
  items,
  prices,
  globalPrice,
  onPriceChange,
  onRemoveItem,
  errors,
}: NftItemListProps) => {
  return (
    <div className="space-y-3 max-h-44 overflow-y-auto mb-5">
      {items.length === 0 ? (
        <div className="text-center text-base text-muted-foreground py-8">
          No items to list
        </div>
      ) : (
        items.map((item) => (
          <NftItem
            key={item.id}
            item={item}
            onRemoveItem={onRemoveItem}
            price={prices[item.id] ?? globalPrice}
            onPriceChange={onPriceChange}
            error={errors[`item_${item.id}`]}
            showPriceInput={true}
          />
        ))
      )}
    </div>
  );
};
