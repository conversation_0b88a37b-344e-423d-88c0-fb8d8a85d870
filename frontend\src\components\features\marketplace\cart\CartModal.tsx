"use client";

import type { Nft } from "@/lib/api/graphql/generated";
import BuyerCartModal from "./BuyerCartModal";
import SellerListingModal from "./SellerListingModal";

interface CartModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: Nft[];
  onRemoveItem: (id: string) => void;
  onBuy: () => void;
  type: "buyer" | "seller";
  listingStep?: number;
  onClearAllItems?: () => void;
}

export default function CartModal({
  open,
  onOpenChange,
  items,
  onRemoveItem,
  onBuy,
  type,
  listingStep = 0,
  onClearAllItems,
}: CartModalProps) {
  if (type === "seller") {
    return (
      <SellerListingModal
        open={open}
        onOpenChange={onOpenChange}
        items={items}
        onRemoveItem={onRemoveItem}
        onList={onBuy}
        listingStep={listingStep}
        onClearAllItems={onClearAllItems}
      />
    );
  }

  return (
    <BuyerCartModal
      open={open}
      onOpenChange={onOpenChange}
      items={items}
      onRemoveItem={onRemoveItem}
      onBuy={onBuy}
    />
  );
}
