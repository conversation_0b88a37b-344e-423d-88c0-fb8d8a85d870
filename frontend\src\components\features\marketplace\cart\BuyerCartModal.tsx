"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  BaseModalProps,
  ModalContainer,
  NftItem,
  StepRow,
} from "./CommonComponents";

interface BuyerCartModalProps extends BaseModalProps {
  onBuy: () => void;
  onClearAllItems?: () => void;
}

export default function BuyerCartModal({
  open,
  onOpenChange,
  items,
  onRemoveItem,
  onBuy,
  onClearAllItems,
}: BuyerCartModalProps) {
  const [showSteps, setShowSteps] = useState(false);
  const [step, setStep] = useState(1);

  if (!open) return null;

  const handleBuyClick = () => {
    setShowSteps(true);
    setStep(1);
    setTimeout(() => setStep(2), 1200);
    setTimeout(() => setStep(3), 2600);
    setTimeout(() => {
      setShowSteps(false);
      setStep(1);
      onBuy();
    }, 4000);
  };

  const handleClearAll = () => {
    if (onClearAllItems) {
      onClearAllItems();
    }
  };

  return (
    <ModalContainer
      title="Cart"
      itemCount={items.length}
      onClose={() => onOpenChange(false)}
      onClearAll={handleClearAll}
      showClearButton={items.length > 0}
    >
      {showSteps ? (
        <div className="rounded-2xl p-8 bg-[#18141c] border border-neutral-700 mb-2">
          <div className="text-xl font-bold mb-8">Completing Order...</div>
          <div className="space-y-7">
            <StepRow
              step={1}
              current={step}
              label="Step 1 - Initializing Buy Transactions"
            />
            <StepRow
              step={2}
              current={step}
              label="Step 2 - Signing Buy Transactions"
              desc="Review and confirm in your wallet..."
            />
            <StepRow
              step={3}
              current={step}
              label="Step 3 - Confirming Buy Transactions"
            />
          </div>
        </div>
      ) : (
        <>
          <div className="space-y-3 max-h-44 overflow-y-auto mb-5">
            {items.length === 0 ? (
              <div className="text-center text-base text-muted-foreground py-8">
                No items in cart
              </div>
            ) : (
              items.map((item) => (
                <NftItem
                  key={item.id}
                  item={item}
                  onRemoveItem={onRemoveItem}
                />
              ))
            )}
          </div>

          <div className="space-y-2 text-base mb-3 border-t border-[#2a203a] pt-4">
            <div className="flex justify-between">
              <div className="font-semibold text-white">Total</div>
              <div className="font-bold text-pink-500">0.1 SOL</div>
            </div>
            <div className="flex justify-between text-xs">
              <div>
                Priority fee{" "}
                <span className="text-[10px] px-1 py-0.5 rounded-full bg-muted">
                  Standard
                </span>
              </div>
              <div>0 SOL</div>
            </div>
          </div>

          <Button
            className="w-full bg-pink-600 hover:bg-pink-700 text-lg h-12 rounded-xl font-bold mt-2 shadow-lg transition"
            onClick={handleBuyClick}
            disabled={items.length === 0}
          >
            Buy Now
          </Button>
          <div className="text-xs text-center text-muted-foreground mt-3">
            By clicking &quot;Buy Now&quot;, you agree to the{" "}
            <a href="#" className="text-primary hover:underline">
              Magic Eden Terms of Service
            </a>
            .
          </div>
        </>
      )}
    </ModalContainer>
  );
}
