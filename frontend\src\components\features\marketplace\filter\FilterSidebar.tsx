"use client";

import type React from "react";
import { useState, useCallback } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Slider } from "@/components/ui/slider";
import { debounce } from "lodash";

interface FilterSidebarProps {
  onClose: () => void;
  priceRange: [number, number];
  onPriceRangeChange: (range: [number, number]) => void;
  onStatusChange: (status: string) => void;
  onSortChange: (sort: string) => void;
}

export default function FilterSidebar({
  onClose,
  priceRange,
  onPriceRangeChange,
  onStatusChange,
  onSortChange,
}: FilterSidebarProps) {
  const [status, setStatus] = useState("all");
  const [minPrice, setMinPrice] = useState(priceRange[0].toString());
  const [maxPrice, setMaxPrice] = useState(priceRange[1].toString());

  const debouncedPriceRangeChange = debounce((range: [number, number]) => {
    onPriceRangeChange(range);
  }, 300);

  const handleMinPriceChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setMinPrice(value);
      const parsedValue = Number.parseFloat(value);
      if (!isNaN(parsedValue) && parsedValue >= 0) {
        debouncedPriceRangeChange([parsedValue, priceRange[1]]);
      }
    },
    [priceRange, debouncedPriceRangeChange]
  );

  const handleMaxPriceChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setMaxPrice(value);
      const parsedValue = Number.parseFloat(value);
      if (
        !isNaN(parsedValue) &&
        parsedValue >= 0 &&
        parsedValue >= priceRange[0]
      ) {
        debouncedPriceRangeChange([priceRange[0], parsedValue]);
      }
    },
    [priceRange, debouncedPriceRangeChange]
  );

  const handleSliderChange = useCallback(
    (values: number[]) => {
      const [min, max] = values;
      const newMin = min / 100;
      const newMax = max / 100;
      setMinPrice(newMin.toFixed(4));
      setMaxPrice(newMax.toFixed(4));
      debouncedPriceRangeChange([newMin, Math.max(newMin, newMax)]);
    },
    [debouncedPriceRangeChange]
  );

  const handleStatusChange = useCallback(
    (value: string) => {
      setStatus(value);
      onStatusChange(value);
    },
    [onStatusChange]
  );

  const handleSortChange = useCallback(
    (value: string) => {
      onSortChange(value);
    },
    [onSortChange]
  );

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">Filters</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1 overflow-auto">
        <div className="p-4 border-b">
          <h3 className="text-sm font-medium mb-2">Status</h3>
          <RadioGroup
            value={status}
            onValueChange={handleStatusChange}
            className="space-y-2"
          >
            <div className="flex items-center justify-between">
              <Label
                htmlFor="show-all"
                className="flex items-center gap-2 cursor-pointer"
              >
                <RadioGroupItem id="show-all" value="all" />
                All Items
              </Label>
            </div>
            <div className="flex items-center justify-between">
              <Label
                htmlFor="listed"
                className="flex items-center gap-2 cursor-pointer"
              >
                <RadioGroupItem id="listed" value="listed" />
                Listed
              </Label>
            </div>
            <div className="flex items-center justify-between">
              <Label
                htmlFor="not-listed"
                className="flex items-center gap-2 cursor-pointer"
              >
                <RadioGroupItem id="not-listed" value="not-listed" />
                Not Listed
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className="p-4 border-b">
          <h3 className="text-sm font-medium mb-2">Price Range (ETH)</h3>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Input
                type="text"
                placeholder="MIN"
                value={minPrice}
                onChange={handleMinPriceChange}
                className="h-9"
                min={0}
                step={0.0001}
                pattern="[0-9]*\.?[0-9]*"
              />
              <span>to</span>
              <Input
                type="text"
                placeholder="MAX"
                value={maxPrice}
                onChange={handleMaxPriceChange}
                className="h-9"
                min={0}
                step={0.0001}
                pattern="[0-9]*\.?[0-9]*"
              />
            </div>
            <Slider
              value={[
                Math.min(Number.parseFloat(minPrice) * 100, 1000),
                Math.min(Number.parseFloat(maxPrice) * 100, 1000),
              ]}
              min={0}
              max={1000}
              step={0.01}
              onValueChange={handleSliderChange}
              className="mt-6"
            />
          </div>
        </div>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="sort" className="border-b">
            <AccordionTrigger className="px-4 py-3 text-sm font-medium hover:no-underline">
              Sort By
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-3">
              <RadioGroup
                defaultValue="recent"
                className="space-y-2"
                onValueChange={handleSortChange}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="recent" id="recent" />
                  <Label htmlFor="recent">Recently Listed</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="price-low" id="price-low" />
                  <Label htmlFor="price-low">Price: Low to High</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="price-high" id="price-high" />
                  <Label htmlFor="price-high">Price: High to Low</Label>
                </div>
              </RadioGroup>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
