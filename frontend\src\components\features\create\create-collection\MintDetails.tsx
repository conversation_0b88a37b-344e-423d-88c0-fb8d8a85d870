"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { HelpCircle, MoreVertical, Plus } from "lucide-react";
import { AllowlistStageDialog } from "./AllowlistStageDialog";
import { PublicStageDialog } from "./PublicStageDialog";
import type {
  AllowlistStageInput,
  PublicMintInput,
} from "@/lib/api/graphql/generated";
import { calculateEndDate } from "@/lib/utils/date";
import { DateTimePicker24h } from "@/components/ui/DateTimePicker24h";
import { toast } from "sonner";

interface MintDetailsProps {
  isLoading: boolean;
  allowlistStages: AllowlistStageInput[];
  setAllowlistStages: (stages: AllowlistStageInput[]) => void;
  publicMint: PublicMintInput;
  setPublicMint: (mint: PublicMintInput) => void;
  onMintPriceChange?: (price: string) => void;
  onRoyaltyFeeChange?: (fee: string) => void;
  onMaxSupplyChange?: (supply: string) => void;
  onMintLimitChange?: (limit: string) => void;
  onMintStartDateChange?: (date: Date) => void;
}

export function MintDetails({
  isLoading,
  allowlistStages,
  setAllowlistStages,
  publicMint,
  setPublicMint,
  onMintPriceChange,
  onRoyaltyFeeChange,
  onMaxSupplyChange,
  onMintLimitChange,
  onMintStartDateChange,
}: MintDetailsProps) {
  const [date, setDate] = useState<Date>(
    new Date(new Date().getTime() + 5 * 60 * 1000) // Mặc định 5 phút sau
  );
  const [isAllowlistDialogOpen, setIsAllowlistDialogOpen] = useState(false);
  const [isPublicMintDialogOpen, setIsPublicMintDialogOpen] = useState(false);
  const [currentStageIndex, setCurrentStageIndex] = useState<number | null>(
    null
  );
  const [publicMintEndDate, setPublicMintEndDate] = useState<Date | null>(null);

  const [collectionMintPrice, setCollectionMintPrice] = useState("0.012");
  const [royaltyFee, setRoyaltyFee] = useState("1");
  const [maxSupply, setMaxSupply] = useState("11");
  const [mintLimit, setMintLimit] = useState("1");

  // Tính publicMintEndDate
  useEffect(() => {
    if (publicMint.startDate) {
      try {
        const endDate = calculateEndDate(
          publicMint.startDate,
          publicMint.durationDays,
          publicMint.durationHours
        );
        setPublicMintEndDate(endDate);
      } catch (error) {
        console.error(error);
        setPublicMintEndDate(null);
        toast.error("Failed to calculate public mint end date");
      }
    } else {
      setPublicMintEndDate(null);
    }
  }, [publicMint]);

  useEffect(() => {
    if (onMintStartDateChange) onMintStartDateChange(date);

    const now = new Date();
    const defaultPublicMintDate = new Date(
      Math.max(date.getTime(), now.getTime() + 5 * 60 * 1000)
    );
    const currentPublicMintDate = publicMint.startDate
      ? new Date(publicMint.startDate)
      : null;

    if (
      !currentPublicMintDate ||
      currentPublicMintDate < date ||
      currentPublicMintDate < now
    ) {
      setPublicMint({
        ...publicMint,
        startDate: defaultPublicMintDate.toISOString(),
      });
    }
  }, [date, onMintStartDateChange, setPublicMint, publicMint]);

  useEffect(() => {
    if (onMintPriceChange) onMintPriceChange(collectionMintPrice);
  }, [collectionMintPrice, onMintPriceChange]);

  useEffect(() => {
    if (onRoyaltyFeeChange) onRoyaltyFeeChange(royaltyFee);
  }, [royaltyFee, onRoyaltyFeeChange]);

  useEffect(() => {
    if (onMaxSupplyChange) onMaxSupplyChange(maxSupply);
  }, [maxSupply, onMaxSupplyChange]);

  useEffect(() => {
    if (onMintLimitChange) onMintLimitChange(mintLimit);
  }, [mintLimit, onMintLimitChange]);

  const handleDeleteStage = (index: number) => {
    setAllowlistStages(allowlistStages.filter((_, i) => i !== index));
  };

  const openEditStageDialog = (index: number) => {
    setCurrentStageIndex(index);
    setIsAllowlistDialogOpen(true);
  };

  const handleAddStage = () => {
    if (allowlistStages.length >= 1) return;
    const newStage: AllowlistStageInput = {
      mintPrice: "",
      durationDays: "1",
      durationHours: "0",
      wallets: [],
      startDate: publicMintEndDate
        ? publicMintEndDate.toISOString()
        : date.toISOString(),
    };
    setAllowlistStages([...allowlistStages, newStage]);
    setCurrentStageIndex(allowlistStages.length);
    setIsAllowlistDialogOpen(true);
  };

  const handleSaveStage = (stage: AllowlistStageInput) => {
    // Kiểm tra chồng lấn với publicMint
    const stageStart = new Date(stage.startDate).getTime();
    const publicMintEnd = publicMintEndDate
      ? publicMintEndDate.getTime()
      : Number.MIN_SAFE_INTEGER;
    if (stageStart <= publicMintEnd) {
      toast.error(
        `Allowlist stage must start after public mint ends (${format(
          publicMintEndDate!,
          "MM/dd/yyyy h:mm a"
        )})`
      );
      return;
    }

    if (currentStageIndex !== null) {
      const updatedStages = allowlistStages.map((s, i) =>
        i === currentStageIndex ? stage : s
      );
      setAllowlistStages(updatedStages);
    } else {
      setAllowlistStages([...allowlistStages, stage]);
    }
    setIsAllowlistDialogOpen(false);
    setCurrentStageIndex(null);
  };

  const handleSavePublicMint = (updatedPublicMint: PublicMintInput) => {
    // Kiểm tra chồng lấn với allowlist stages
    const publicMintEnd = calculateEndDate(
      updatedPublicMint.startDate,
      updatedPublicMint.durationDays,
      updatedPublicMint.durationHours
    ).getTime();
    for (const stage of allowlistStages) {
      const stageStart = new Date(stage.startDate).getTime();
      if (stageStart <= publicMintEnd) {
        toast.error(
          `Public mint must end before allowlist stage starts (${format(
            new Date(stage.startDate),
            "MM/dd/yyyy h:mm a"
          )})`
        );
        return;
      }
    }

    setPublicMint(updatedPublicMint);
    setIsPublicMintDialogOpen(false);
  };

  return (
    <div className="space-y-6 bg-white dark:bg-[#0e0a1a] p-6 rounded-lg border border-gray-200 dark:border-[#3a3450]">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="flex items-center gap-1">
            <Label
              htmlFor="mintPrice"
              className="text-gray-900 dark:text-white"
            >
              Mint Price
            </Label>
          </div>
          {isLoading ? (
            <Skeleton className="h-10 w-full mt-1" />
          ) : (
            <div className="flex mt-2">
              <Input
                id="mintPrice"
                value={collectionMintPrice}
                onChange={(e) => setCollectionMintPrice(e.target.value)}
                className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
              <div className="bg-gray-100 dark:bg-[#2a2535] border border-gray-200 dark:border-[#3a3450] rounded-r-md px-4 flex items-center text-gray-900 dark:text-white">
                ETH
              </div>
            </div>
          )}
        </div>
        <div>
          <div className="flex items-center gap-1">
            <Label
              htmlFor="royaltyFee"
              className="text-gray-900 dark:text-white"
            >
              Royalty Fee
            </Label>
            <HelpCircle className="h-4 w-4 text-gray-500 dark:text-gray-500" />
          </div>
          {isLoading ? (
            <Skeleton className="h-10 w-full mt-1" />
          ) : (
            <div className="flex mt-2">
              <Input
                id="royaltyFee"
                value={royaltyFee}
                onChange={(e) => setRoyaltyFee(e.target.value)}
                className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
              <div className="bg-gray-100 dark:bg-[#2a2535] border border-gray-200 dark:border-[#3a3450] rounded-r-md px-4 flex items-center text-gray-900 dark:text-white">
                %
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="flex items-center gap-1">
            <Label
              htmlFor="maxSupply"
              className="text-gray-900 dark:text-white"
            >
              Max Supply
            </Label>
            <HelpCircle className="h-4 w-4 text-gray-500 dark:text-gray-500" />
          </div>
          {isLoading ? (
            <Skeleton className="h-10 w-full mt-1" />
          ) : (
            <Input
              id="maxSupply"
              value={maxSupply}
              onChange={(e) => setMaxSupply(e.target.value)}
              className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white mt-2 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          )}
        </div>
        <div>
          <div className="flex items-center gap-1">
            <Label
              htmlFor="mintLimit"
              className="text-gray-900 dark:text-white"
            >
              Mint Limit per Wallet
            </Label>
            <HelpCircle className="h-4 w-4 text-gray-500 dark:text-gray-500" />
          </div>
          {isLoading ? (
            <Skeleton className="h-10 w-full mt-1" />
          ) : (
            <Input
              id="mintLimit"
              value={mintLimit}
              onChange={(e) => setMintLimit(e.target.value)}
              className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white mt-2 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          )}
        </div>
      </div>
      <div>
        <Label htmlFor="mintDate" className="text-gray-900 dark:text-white">
          Mint Start Date & Time
        </Label>
        {isLoading ? (
          <Skeleton className="h-10 w-full mt-1" />
        ) : (
          <DateTimePicker24h
            value={date}
            onChange={(newDate) => {
              if (newDate) {
                const now = new Date();
                const minDate = new Date(now.getTime() + 5 * 60 * 1000);
                const adjustedDate = new Date(newDate);

                if (adjustedDate < minDate) {
                  adjustedDate.setHours(minDate.getHours());
                  adjustedDate.setMinutes(minDate.getMinutes());
                  adjustedDate.setSeconds(0);
                }

              
                setDate(adjustedDate);
              }
            }}
            disabledDates={(checkDate: Date) => {
              const now = new Date();
              const minDate = new Date(now.getTime() + 5 * 60 * 1000);
              // Chuẩn hóa ngày để tránh lỗi múi giờ
              const checkDateStr = format(checkDate, "yyyy-MM-dd");
              const minDateStr = format(minDate, "yyyy-MM-dd");
              const isDisabled = checkDateStr < minDateStr;
             
              return isDisabled;
            }}
            disabledHours={(date: Date) => (hour: number) => {
              const now = new Date();
              if (date.toDateString() === now.toDateString()) {
                return hour < now.getHours();
              }
              return false;
            }}
            disabledMinutes={(date: Date, hour: number) => (minute: number) => {
              const now = new Date();
              if (
                date.toDateString() === now.toDateString() &&
                hour === now.getHours()
              ) {
                return minute < now.getMinutes();
              }
              return false;
            }}
            className="mt-2 [&>div>button]:bg-gray-50 [&>div>button]:dark:bg-[#1a1525] [&>div>button]:border-gray-200 dark:border-[#3a3450] [&>div>button]:text-gray-900 [&>div>button]:dark:text-white [&>div>button]:focus-visible:ring-0 [&>div>button]:focus-visible:ring-offset-0 [&>div>div>div]:bg-white [&>div>div>div]:dark:bg-[#1a1525] [&>div>div>div]:border-gray-200 dark:border-[#3a3450] [&>div>div>div]:text-gray-900 [&>div>div>div]:dark:text-white"
          />
        )}
      </div>
      <div>
        <Label className="text-gray-900 dark:text-white">Mint Stages</Label>
        {isLoading ? (
          <Skeleton className="h-40 w-full mt-1" />
        ) : (
          <div className="space-y-3 mt-2">
            {allowlistStages.map((stage, index) => (
              <div
                key={index} // Dùng index thay vì stageId
                className="border border-gray-200 dark:border-[#3a3450] rounded-md p-4 bg-gray-50 dark:bg-[#1a1525] relative cursor-pointer"
                onClick={() => openEditStageDialog(index)}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      Allowlist Mint - {index + 1} {/* Hiển thị số thứ tự */}
                    </h3>
                    <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 text-xs px-2 py-0.5 rounded">
                      {stage.mintPrice} ETH
                    </span>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="bg-white dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450]"
                    >
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          openEditStageDialog(index);
                        }}
                        className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#2a2535] cursor-pointer"
                      >
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteStage(index);
                        }}
                        className="text-red-500 hover:bg-gray-100 dark:hover:bg-[#2a2535] cursor-pointer"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <span>
                    {format(new Date(stage.startDate), "MMM dd yyyy, h:mm a")}
                  </span>
                  <span>
                    Ends:{" "}
                    {format(
                      calculateEndDate(
                        stage.startDate,
                        stage.durationDays,
                        stage.durationHours
                      ),
                      "MMM dd yyyy, h:mm a"
                    )}
                  </span>
                </div>
              </div>
            ))}

            <div
              className="border border-gray-200 dark:border-[#3a3450] rounded-md p-4 bg-gray-50 dark:bg-[#1a1525] relative cursor-pointer"
              onClick={() => setIsPublicMintDialogOpen(true)}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Public Mint
                  </h3>
                  <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 text-xs px-2 py-0.5 rounded">
                    {publicMint.mintPrice} ETH
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-400"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="bg-white dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450]"
                  >
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsPublicMintDialogOpen(true);
                      }}
                      className="text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#2a2535] cursor-pointer"
                    >
                      Edit
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400">
                <span>
                  {publicMint.startDate
                    ? format(
                        new Date(publicMint.startDate),
                        "MMM dd yyyy, h:mm a"
                      )
                    : "Not set"}
                </span>
                <span>
                  Ends:{" "}
                  {publicMint.startDate
                    ? format(
                        calculateEndDate(
                          publicMint.startDate,
                          publicMint.durationDays,
                          publicMint.durationHours
                        ),
                        "MMM dd yyyy, h:mm a"
                      )
                    : "Not set"}
                </span>
              </div>
            </div>

            {allowlistStages.length < 1 && (
              <div className="border border-gray-200 dark:border-[#1a1525] rounded-md p-3 bg-gray-50 dark:bg-[#0e0a1a] flex justify-center">
                <Button
                  type="button"
                  variant="ghost"
                  className="cursor-pointer w-full text-gray-500 dark:text-gray-400 flex items-center justify-center gap-2 hover:bg-transparent hover:text-gray-600 dark:hover:text-gray-400"
                  onClick={handleAddStage}
                  style={{ backgroundColor: "transparent" }}
                >
                  <Plus className="h-4 w-4" /> Add Allowlist Stage
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      <AllowlistStageDialog
        isOpen={isAllowlistDialogOpen}
        onOpenChange={setIsAllowlistDialogOpen}
        stage={
          currentStageIndex !== null ? allowlistStages[currentStageIndex] : null
        }
        onSave={handleSaveStage}
        mintStartDate={date}
        publicMintEndDate={publicMintEndDate!} // Truyền publicMintEndDate
      />
      <PublicStageDialog
        isOpen={isPublicMintDialogOpen}
        onOpenChange={setIsPublicMintDialogOpen}
        publicMint={publicMint}
        onSave={handleSavePublicMint}
        mintStartDate={date}
      />
    </div>
  );
}
