"use client";

import { useState, useEffect } from "react";
import { Lock } from "lucide-react";
import { cn } from "@/lib/utils";
import CountdownTimer from "@/components/features/mint/MintPanel/CountdownTimer";
import { useSelector } from "react-redux";
import type { RootState } from "@/store/store";
import {
  FilterOperator,
  useGetCollectionQuery,
} from "@/lib/api/graphql/generated";
import { NFTManager } from "@/lib/abi/NFTManager";
import { useAccount, useReadContract } from "wagmi";

const MintStages = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [hoveredStage, setHoveredStage] = useState<number | null>(null);
  const collection = useSelector((state: RootState) => state.collection.data);
  const { address } = useAccount();
  const { refetch } = useGetCollectionQuery({
    variables: {
      input: {
        filters:
          collection?.chainId && collection?.contractAddress
            ? [
                {
                  field: "chainId",
                  operator: FilterOperator.Eq,
                  value: collection?.chainId || "",
                },
                {
                  field: "contractAddress",
                  operator: FilterOperator.Eq,
                  value: collection?.contractAddress || "",
                },
              ]
            : [],
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress,
  });

  const { data: activeStage } = useReadContract({
    address: collection?.contractAddress as `0x${string}` | undefined,
    abi: NFTManager.abi,
    functionName: "getActiveStage",
    args: address ? [address as `0x${string}`] : undefined,
    query: {
      enabled: !!address && !!collection?.contractAddress,
      retry: 3,
      retryDelay: 1000,
      refetchInterval: 5000, // Poll every 5 seconds
    },
  }) as { data: [bigint, boolean, bigint] | undefined };

  useEffect(() => {
    const interval = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(interval);
  }, []);

  const getStageStatus = (
    startDate: string,
    durationDays: string
  ): "upcoming" | "live" | "ended" => {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setDate(end.getDate() + Number.parseInt(durationDays));
    if (currentTime < start) return "upcoming";
    if (currentTime > end) return "ended";
    return "live";
  };

  const getEndTime = (startDate: string, durationDays: string): string => {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setDate(end.getDate() + Number.parseInt(durationDays));
    return end.toISOString();
  };

  const getStartTime = (startDate: string): string =>
    new Date(startDate).toISOString();

  if (!collection) return null;

  const { allowlistStages = [], publicMint } = collection;
  const isPublicStage = activeStage ? activeStage[1] : false;
  const activeStageId = activeStage ? Number(activeStage[0]) : 0;

  const renderStageCard = (
    title: string,
    index: number,
    startDate: string,
    durationDays: string,
    mintPrice: string,
    isActive: boolean,
    isPublic: boolean,
    wallets?: string[]
  ) => {
    const status = getStageStatus(startDate, durationDays);
    const isEligible = wallets?.includes(address?.toLowerCase() || "");

    return (
      <div
        key={`stage-${index}`}
        className={cn(
          "rounded-lg py-4 px-5 transition-all duration-200",
          isActive
            ? "border border-pink-400 dark:border-pink-500"
            : "border border-gray-200/50 dark:border-gray-800/30",
          hoveredStage === index
            ? isActive
              ? "bg-pink-50 dark:bg-pink-600/20"
              : "bg-gray-100 dark:bg-gray-800/20"
            : isActive
            ? "bg-pink-50/50 dark:bg-pink-600/10"
            : ""
        )}
        onMouseEnter={() => setHoveredStage(index)}
        onMouseLeave={() => setHoveredStage(null)}
      >
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-gray-900 dark:text-white text-sm font-semibold">
              {title}
            </span>
          </div>
          {status === "live" && (
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 text-sm rounded-full bg-pink-500 dark:bg-pink-600 text-white">
                LIVE
              </span>
              <CountdownTimer
                endTime={getEndTime(startDate, durationDays)}
                onEnd={() => refetch()}
                isLive={true}
              />
            </div>
          )}
          {status === "ended" && (
            <span className="text-gray-500 dark:text-gray-400 text-sm">
              ENDED
            </span>
          )}
          {status === "upcoming" && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500 dark:text-gray-400 text-sm">
                STARTS IN
              </span>
              <CountdownTimer
                endTime={getStartTime(startDate)}
                onEnd={() => refetch()}
                isLive={false}
              />
            </div>
          )}
        </div>
        <div className="text-gray-700 dark:text-gray-300 text-sm">
          Price: {mintPrice} ETH
          {!isPublic && (
            <span className="ml-2">• Wallets: {wallets?.length || 0}</span>
          )}
          {isActive && !isPublic && wallets && (
            <span
              className={cn(
                "ml-2",
                isEligible
                  ? "text-green-600 dark:text-green-500"
                  : "text-red-600 dark:text-red-500"
              )}
            >
              {isEligible ? " (Eligible)" : " (Not Eligible)"}
            </span>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {allowlistStages?.map((stage, index) =>
        renderStageCard(
          `Allowlist Stage ${index + 1}`,
          index,
          stage.startDate,
          stage.durationDays,
          stage.mintPrice,
          activeStageId === Number(stage.stageId),
          false,
          stage.wallets
        )
      )}
      {publicMint &&
        renderStageCard(
          "Public",
          allowlistStages?.length || 0,
          publicMint.startDate,
          publicMint.durationDays,
          publicMint.mintPrice,
          isPublicStage,
          true
        )}
    </div>
  );
};

export default MintStages;
