import { TabsContent } from "@/components/ui/tabs";
import { MintPanelHeader } from "./MintPanelHeader";
import MintInfo from "./MintInfo";
import MintButton from "./MintButton";
import { NftPreview } from "./NftPreview";
import MintProgress from "./MintProgress";
import { Collection } from "@/lib/api/graphql/generated";
import {
  MintCostData,
  MintCost,
  FormState,
  FormActions,
  FormErrors,
} from "./types";

interface MintPanelContentProps {
  collectionData: Collection;
  isSameArtType: boolean;
  mintCostData: MintCostData;
  lastMintCost: MintCost;
  formState: FormState;
  formActions: FormActions;
  formErrors: FormErrors;
  isAllowlistMint: boolean;
  isLoadingMintCost: boolean;
  onMintSuccess: () => void;
  onMintError: (error: string) => void;
  onConfirm: () => void;
  currentGalleryImage?: string;
}

export function MintPanelContent({
  collectionData,
  isSameArtType,
  mintCostData,
  lastMintCost,
  formState,
  formActions,
  formErrors,
  isAllowlistMint,
  isLoadingMintCost,
  onMintSuccess,
  onMintError,
  onConfirm,
  currentGalleryImage,
}: MintPanelContentProps) {
  return (
    <TabsContent value="mint" className="min-h-[800px] min-w-[700px]">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Form */}
        <div className="space-y-6">
          <MintPanelHeader
            collection={collectionData}
            isSameArtType={isSameArtType}
            mintPrice={
              mintCostData?.getMintCost?.success
                ? Number(mintCostData.getMintCost.mintPrice).toFixed(4)
                : Number(lastMintCost.mintPrice).toFixed(4)
            }
            estimatedGas={
              mintCostData?.getMintCost?.success
                ? Number(mintCostData.getMintCost.estimatedGas).toFixed(6)
                : Number(lastMintCost.estimatedGas).toFixed(6)
            }
          />

          {/* Mint Form */}
          <MintInfo
            {...formState}
            {...formActions}
            nameError={formErrors.nameError}
            descriptionError={formErrors.descriptionError}
            attributesError={formErrors.attributesError}
            isAllowlistMint={isAllowlistMint}
            isSameArtType={isSameArtType}
          />

          {/* Mint Button */}
          <div className="md:sticky md:bottom-4">
            <MintButton
              agreedToTerms={formState.agreedToTerms}
              imageFile={formState.imageFile}
              name={formState.name}
              description={formState.description}
              attributes={formState.attributes}
              amount={formState.amount}
              royalty={formState.royalty}
              gasPrice={formState.gasPrice}
              gasLimit={formState.gasLimit}
              signature={formState.signature}
              nonce={formState.nonce}
              batchMetadata={formState.batchMetadata}
              onMintSuccess={onMintSuccess}
              onMintError={onMintError}
              onConfirm={onConfirm}
              isSameArtType={isSameArtType}
              isAllowlistMint={isAllowlistMint}
              lastMintCost={lastMintCost}
              isCalculating={isLoadingMintCost}
            />
          </div>
        </div>

        {/* Right Column - Preview */}
        <div className="space-y-4">
          <NftPreview
            imageFile={formState.imageFile}
            name={formState.name}
            description={formState.description}
            attributes={formState.attributes}
            isSameArtType={isSameArtType}
            collection={collectionData}
            batchMetadata={formState.batchMetadata}
            currentGalleryImage={currentGalleryImage}
          />
          <div
            className={`bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg p-4 border dark:border-gray-800/50 shadow-sm ${
              isSameArtType
                ? "border-l-4 border-l-blue-500 dark:border-l-blue-600"
                : "border-l-4 border-l-purple-500 dark:border-l-purple-600"
            }`}
          >
            <MintProgress />
          </div>
        </div>
      </div>
    </TabsContent>
  );
}
