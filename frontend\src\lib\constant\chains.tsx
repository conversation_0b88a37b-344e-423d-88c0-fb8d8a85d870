export interface Chain {
  id: number;
  name: string;
  icon: string;
  symbol: string;
}

export const getSupportedChains = (): Chain[] => [
  {
    id: 31337,
    name: "Hardhat",
    icon: "/icons/hardhat.svg",
    symbol: "ETH",
  },
  {
    id: 11155111,
    name: "<PERSON><PERSON>",
    icon: "/icons/sepolia.svg",
    symbol: "ETH",
  },

  // {
  //   id: 80001,
  //   name: "Polygon Mumbai",
  //   icon: <Polygon />,
  //   symbol: "ETH",
  // },

  // {
  //   id: 84531,
  //   name: "<PERSON> Goerli",
  //   icon: <Base />,
  //   symbol: "ETH",
  // },
  // Add more chains dynamically (e.g., from config or DB)
];
