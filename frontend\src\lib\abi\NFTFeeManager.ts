export const NFTFeeManager = {
  "_format": "hh-sol-artifact-1",
  "contractName": "NFTFeeManager",
  "sourceName": "contracts/NFTFeeManager.sol",
  "abi": [
    {
      "inputs": [],
      "name": "EnforcedPause",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "ExpectedPause",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "InvalidInitialization",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "NotInitializing",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        }
      ],
      "name": "OwnableInvalidOwner",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "OwnableUnauthorizedAccount",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "ReentrancyGuardReentrantCall",
      "type": "error"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "CollectionFeeUpdated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "FeesAccumulated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "FeesWithdrawFailed",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "FeesWithdrawn",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "uint64",
          "name": "version",
          "type": "uint64"
        }
      ],
      "name": "Initialized",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "MarketplaceFeeUpdated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "previousOwner",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "OwnershipTransferred",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "Paused",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "RoyaltyUpdated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "Unpaused",
      "type": "event"
    },
    {
      "inputs": [],
      "name": "MAX_FEE_PERCENTAGE",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "accumulateFees",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "salePrice",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        }
      ],
      "name": "calculateMarketplaceFee",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "collectionFeePercentages",
      "outputs": [
        {
          "internalType": "uint128",
          "name": "",
          "type": "uint128"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "failedTransfers",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        }
      ],
      "name": "getPendingFees",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "_marketplaceFeeRecipient",
          "type": "address"
        },
        {
          "internalType": "address",
          "name": "_royaltyRecipient",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "_marketplaceFeePercentage",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "_royaltyPercentage",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "_marketplace",
          "type": "address"
        }
      ],
      "name": "initialize",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "marketplace",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "marketplaceFeePercentage",
      "outputs": [
        {
          "internalType": "uint128",
          "name": "",
          "type": "uint128"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "marketplaceFeeRecipient",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "owner",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "pause",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "paused",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "pendingFees",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "renounceOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "salePrice",
          "type": "uint256"
        }
      ],
      "name": "royaltyInfo",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "royaltyPercentage",
      "outputs": [
        {
          "internalType": "uint128",
          "name": "",
          "type": "uint128"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "royaltyRecipient",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "setCollectionFee",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "_marketplace",
          "type": "address"
        }
      ],
      "name": "setMarketplace",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "setMarketplaceFee",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "recipient",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "percentage",
          "type": "uint256"
        }
      ],
      "name": "setRoyalty",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "bytes4",
          "name": "interfaceId",
          "type": "bytes4"
        }
      ],
      "name": "supportsInterface",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "unpause",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "withdrawFailedTransfers",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "withdrawFees",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    }
  ],
  "bytecode": "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",
  "deployedBytecode": "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",
  "linkReferences": {},
  "deployedLinkReferences": {}
};