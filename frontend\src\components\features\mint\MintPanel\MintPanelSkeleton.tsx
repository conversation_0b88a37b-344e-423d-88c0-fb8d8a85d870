import { Skeleton } from "@/components/ui/skeleton";

export function MintPanelSkeleton() {
  return (
    <div className="bg-gradient-to-b from-gray-50 to-white dark:from-[#0c0916] dark:to-[#0f0a19] flex items-center justify-center p-6">
      <div className="w-full space-y-4 max-w-md">
        <div className="bg-white border-gray-200 dark:bg-[#0f0a19] dark:border-gray-800/50 rounded-xl overflow-hidden border shadow-xl">
          {/* Mint Stages Header */}
          <div className="p-5 space-y-4 bg-gray-50 dark:bg-[#0c0916]">
            <Skeleton className="h-5 w-32" />

            {/* Mint Stages */}
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="rounded-lg py-4 px-5 border border-gray-200/50 dark:border-gray-800/30"
                >
                  <div className="flex justify-between items-center mb-2">
                    <Skeleton className="h-4 w-32" />
                    <div className="flex items-center">
                      <Skeleton className="h-4 w-16 mr-2" />
                      <div className="flex space-x-1">
                        {[...Array(4)].map((_, i) => (
                          <Skeleton key={i} className="w-10 h-6" />
                        ))}
                      </div>
                    </div>
                  </div>
                  <Skeleton className="h-3 w-40" />
                </div>
              ))}
            </div>
          </div>

          {/* Mint Panel */}
          <div className="p-5 space-y-5 bg-white dark:bg-[#0f0a19] border-t border-gray-200/70 dark:border-gray-800/30">
            {/* Progress bar */}
            <div className="space-y-2 pt-1">
              <div className="flex items-center">
                <Skeleton className="h-3 w-16" />
                <div className="ml-auto">
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <Skeleton className="h-2 w-full rounded-full" />
            </div>

            {/* Price section */}
            <div className="space-y-4">
              <div className="space-y-1">
                <Skeleton className="h-3 w-10" />
                <Skeleton className="h-8 w-24" />
              </div>
              <div className="flex justify-between items-center">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-10" />
              </div>
              <div className="flex items-start space-x-3 mt-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>

            {/* Mint button */}
            <Skeleton className="h-10 w-full rounded-md" />
          </div>
        </div>

        {/* Explore button */}
        <Skeleton className="h-10 w-full rounded-md" />
      </div>
    </div>
  );
}
