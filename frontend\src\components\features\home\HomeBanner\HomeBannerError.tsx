"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON>w } from "lucide-react";
import { motion } from "framer-motion";

interface HomeBannerErrorProps {
  error: string;
  onRetry?: () => void;
}

export function HomeBannerError({ error, onRetry }: HomeBannerErrorProps) {
  return (
    <div className="relative w-full h-[500px] rounded-xl overflow-hidden mb-12 group bg-gray-50 dark:bg-[#1A1F2C] border border-gray-200 dark:border-white/10">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 z-10 bg-gradient-to-r from-red-500/10 to-orange-500/5">
        <div className="absolute inset-0 bg-gradient-to-r from-white/90 via-white/70 to-transparent dark:from-[#121620]/90 dark:via-[#121620]/70 dark:to-transparent"></div>
      </div>

      {/* Animated particles */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <motion.div
          className="absolute w-64 h-64 rounded-full blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, 20, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
          style={{
            top: "-20px",
            left: "-20px",
            background: "linear-gradient(45deg, #ff4d4d, #f9cb28)",
          }}
        />
        <motion.div
          className="absolute w-96 h-96 rounded-full blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, -30, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 0.5,
          }}
          style={{
            bottom: "-40px",
            right: "-20px",
            background: "linear-gradient(45deg, #f9cb28, #ff4d4d)",
          }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full p-8 md:p-12 text-center">
        <div className="mb-6 p-4 rounded-full bg-red-100 dark:bg-red-900/30">
          <AlertTriangle className="h-12 w-12 text-red-500" />
        </div>

        <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-orange-500">
            Oops! Something went wrong
          </span>
        </h1>

        <p className="text-lg md:text-xl mb-8 text-gray-700 dark:text-gray-300 max-w-lg">
          We couldn&apos;t load the NFT marketplace data.{" "}
          {error && `Error: ${error}`}
        </p>

        {onRetry && (
          <Button
            size="lg"
            onClick={onRetry}
            className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white font-medium"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
}
