import { Button } from "@/components/ui/button";
export interface PriceInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
}

export const PriceInput = ({
  value,
  onChange,
  error,
  disabled,
}: PriceInputProps) => {
  return (
    <div className="flex items-center gap-2 mb-5">
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Custom Global Price"
        className={`bg-[#231b2e] border w-full ${
          error ? "border-red-500" : "border-[#2a203a]"
        } px-4 py-2 w-36 text-base text-white focus:outline-none focus:border-pink-500 transition`}
        disabled={disabled}
      />
      {error && <span className="text-red-500 text-sm">{error}</span>}
      <div className="ml-auto flex flex-1 items-center">
        <span className="text-base text-muted-foreground">ETH</span>
        <Button
          variant="secondary"
          className="px-4 py-2 text-base font-semibold ml-2"
          disabled={disabled}
        >
          ETH
        </Button>
      </div>
    </div>
  );
};
