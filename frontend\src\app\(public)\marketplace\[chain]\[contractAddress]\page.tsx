import ShopNFTs from "@/components/features/marketplace";
import { fetchCollection } from "@/lib/services/collection";
import { validate<PERSON>hain, validateContractAddress } from "@/lib/utils/validate";

interface MarketplacePageProps {
  params: {
    chain: string;
    contractAddress: string;
  };
}

async function getCollectionData(chainName: string, contractAddress: string) {
  try {
    const chain = validateChain(chainName);
    const validatedAddress = validateContractAddress(contractAddress);
    const collection = await fetchCollection(
      String(chain?.id),
      validatedAddress
    );

    return {
      chain,
      validatedAddress,
      collection,
      error: null,
    };
  } catch (error) {
    return {
      chain: null,
      validatedAddress: null,
      collection: null,
      error:
        error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

export async function generateMetadata({ params }: MarketplacePageProps) {
  const { chain: chainName, contractAddress } =await params;
  const { chain, collection, error } = await getCollectionData(
    chainName,
    contractAddress
  );

  if (error || !collection) {
    return {
      title: "Collection Not Found | NFT Marketplace",
      description: error || "The requested collection does not exist",
    };
  }

  return {
    title: `${collection.name} | ${chain?.name} | NFT Marketplace`,
    description:
      collection.description ||
      `Discover, buy, and sell unique NFTs from the ${collection.name} collection on ${chain?.name} blockchain`,
    openGraph: {
      title: `${collection.name} Collection on ${chain?.name}`,
      description:
        collection.description ||
        `Explore the ${collection.name} NFT collection on ${chain?.name} blockchain`,
    },
  };
}

export default async function Page({ params }: MarketplacePageProps) {
  const { chain: chainName, contractAddress } =await params;
  const {  validatedAddress, collection, error } =
    await getCollectionData(chainName, contractAddress);

  if (error || !collection) {
    return (
      <div className="text-center p-4">
        Error: {error || "Collection not found"}
      </div>
    );
  }

  return (
    <ShopNFTs
      contractAddress={validatedAddress}
      initialCollection={collection}
    />
  );
}
