"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronDown, Sprout } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ActionType,
  FilterOperator,
  GetCollectionsDocument,
  SortOrder,
  useCollectionModifiedPrivateRealtimeSubscription,
  useGetCollectionsQuery,
  useMeQuery,
} from "@/lib/api/graphql/generated";
import { useAccount } from "wagmi";
import Link from "next/link";
import Image from "next/image";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function ExistingCollections() {
  const { chainId, chain, isConnected } = useAccount();
  const { data: userData } = useMeQuery({
    skip: !isConnected,
  });

  const input = {
    filters: [
      {
        field: "chainId",
        operator: FilterOperator.Eq,
        value: chainId?.toString() || "",
      },
      {
        field: "creatorId",
        operator: FilterOperator.Eq,
        value: userData?.me?.id || "",
      },
    ],
    sort: {
      field: "createdAt",
      order: SortOrder.Desc,
    },
    pagination: {
      limit: "100",
    },
  };

  const isValidInput = chainId && userData?.me?.id && isConnected;

  const { data: collectionsData, loading } = useGetCollectionsQuery({
    variables: {
      input,
    },
    skip: !isValidInput,
  });

  useCollectionModifiedPrivateRealtimeSubscription({
    variables: {
      input: {
        wallet: userData?.me?.id || "",
        chainId: chainId?.toString() || "",
      },
    },
    onData: ({ data, client }) => {
      const newData = data.data?.collectionModifiedPrivateRealtime;
      const collection = newData?.data;

      if (!collection) return;

      switch (newData?.action) {
        case ActionType.Create:
          client.writeQuery({
            query: GetCollectionsDocument,
            variables: {
              input,
            },
            data: {
              getCollections: {
                collections: [
                  collection,
                  ...(collectionsData?.getCollections.collections || []),
                ],
              },
            },
          });
          break;
        case ActionType.Update:
          client.writeQuery({
            query: GetCollectionsDocument,
            variables: {
              input,
            },
            data: {
              getCollections: {
                collections:
                  collectionsData?.getCollections.collections.map((c) =>
                    c.id === collection.id ? collection : c
                  ) || [],
              },
            },
          });
          break;
      }
    },
  });

  console.log(collectionsData?.getCollections);
  return (
    <Card className="bg-white dark:bg-[#111119] border-gray-200 dark:border-zinc-800 text-gray-900 dark:text-white">
      <CardHeader className="pb-2">
        <CardTitle className="text-xl font-medium">
          Existing Collections
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            View your deployed collections on {chain?.name || "unknown chain"}
          </p>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild className="w-full">
            <Button
              variant="outline"
              className="w-full justify-between bg-gray-50 dark:bg-[#1e1e2d] border-gray-200 dark:border-zinc-700 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-zinc-700 hover:text-gray-900 dark:hover:text-white"
            >
              View Collection
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[var(--radix-dropdown-menu-trigger-width)] max-w-full bg-white dark:bg-[#1e1e2d] border-gray-200 dark:border-zinc-700 text-gray-900 dark:text-white"
            align="start"
            sideOffset={5}
          >
            <ScrollArea className="h-30">
              {loading ? (
                <div className="flex flex-col items-center py-6">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Loading collections...
                  </p>
                </div>
              ) : collectionsData?.getCollections?.collections &&
                collectionsData.getCollections.collections.length > 0 ? (
                collectionsData.getCollections.collections.map((collection) => (
                  <DropdownMenuItem
                    key={collection.id}
                    className="hover:bg-gray-100 dark:hover:bg-zinc-700 py-3 px-4 cursor-pointer"
                  >
                    <Link
                      href={`/mint/${collection.chain}/${collection.contractAddress}`}
                      className="flex items-center gap-2 w-full"
                    >
                      <Image
                        src={
                          collection.image
                            ? `${collection.image}`
                            : "/placeholder.svg"
                        }
                        alt={collection.name}
                        width={24}
                        height={24}
                        className="w-6 h-6 rounded-xs"
                        onError={(e) => {
                          e.currentTarget.src = "/placeholder.svg"; // Fallback image
                        }}
                      />
                      <span className="font-medium">{collection.name}</span>
                    </Link>
                  </DropdownMenuItem>
                ))
              ) : (
                <div className="flex flex-col items-center py-6">
                  <Sprout className="h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    No collections found for this wallet
                  </p>
                </div>
              )}
            </ScrollArea>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardContent>
    </Card>
  );
}
