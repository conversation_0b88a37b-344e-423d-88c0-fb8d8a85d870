import { Chain, getSupportedChains } from "@/lib/constant/chains";

export const validateChain = (chainName: string): Chain | null => {
  const chains = getSupportedChains();
  const chain = chains.find(
    (c) => c.name.toLowerCase() === chainName.toLowerCase()
  );
  if (!chain) {
    return null;
  }
  return chain;
};

export const validateContractAddress = (address: string): string => {
  const isValid = /^0x[a-fA-F0-9]{40}$/.test(address);
  if (!isValid) {
    throw new Error(`Invalid contract address: ${address}`);
  }
  return address;
};
