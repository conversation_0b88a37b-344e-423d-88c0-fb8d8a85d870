import { NFT, NftStatus, TokenStandard } from "@/types/nft";

export const mockNFTs: NFT[] = [
  {
    _id: "4599",
    mintPrice: "0.001224",
    collectionId: "681770cfd0fc57e9a98f8270",
    creator: "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266",
    owner: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    txHash:
      "0x912bc870362ca82abdcf684f96b9fe8501ff1490b9ea46ddafd2da1b05e40d93",
    tokenUri: "ipfs://QmdHxUbv6NjtRo8kkaAqoDrz2ZcGmm8Bn378tQXPsZ3ffy",
    tokenId: "4599",
    chainId: "31337",
    chain: "Hardhat",
    name: "<PERSON><PERSON><PERSON>'s Resolve #4599",
    status: NftStatus.COMPLETED,
    description: "<PERSON><PERSON><PERSON>'s Resolve 123",
    image:
      "https://azure-voluntary-cheetah-817.mypinata.cloud/ipfs/QmbeCAWFrRDUVZzKD3PHeTrvQoYrRoR97WGFNpjAapcfXL",
    attributes: [
      { trait_type: "Background", value: "Olive" },
      { trait_type: "Character", value: "Mikasa" },
      { trait_type: "Rarity", value: "Rare" },
    ],
    bids: [],
    standard: TokenStandard.ERC1155,
    isListed: true,
    isFeatured: false,
    contractAddress: "0x8A791620dd6260079BF849Dc5567aDC3F2FdC318",
    createdAt: new Date("2025-05-04T17:19:22.193Z"),
    updatedAt: new Date("2025-05-04T17:19:22.193Z"),
  },
  {
    _id: "3396",
    mintPrice: "0.001224",
    collectionId: "681770cfd0fc57e9a98f8270",
    creator: "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266",
    owner: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    txHash:
      "0x912bc870362ca82abdcf684f96b9fe8501ff1490b9ea46ddafd2da1b05e40d93",
    tokenUri: "ipfs://QmdHxUbv6NjtRo8kkaAqoDrz2ZcGmm8Bn378tQXPsZ3ffy",
    tokenId: "3396",
    chainId: "31337",
    chain: "Hardhat",
    name: "Mikasa's Resolve #3396",
    status: NftStatus.COMPLETED,
    description: "Mikasa's Resolve 123",
    image:
      "https://azure-voluntary-cheetah-817.mypinata.cloud/ipfs/QmbeCAWFrRDUVZzKD3PHeTrvQoYrRoR97WGFNpjAapcfXL",
    attributes: [
      { trait_type: "Background", value: "Teal" },
      { trait_type: "Character", value: "Mikasa" },
      { trait_type: "Rarity", value: "Common" },
    ],
    bids: [],
    standard: TokenStandard.ERC1155,
    isListed: true,
    isFeatured: false,
    contractAddress: "0x8A791620dd6260079BF849Dc5567aDC3F2FdC318",
    createdAt: new Date("2025-05-04T17:19:22.193Z"),
    updatedAt: new Date("2025-05-04T17:19:22.193Z"),
  },
  {
    _id: "1996",
    mintPrice: "0.001224",
    collectionId: "681770cfd0fc57e9a98f8270",
    creator: "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266",
    owner: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    txHash:
      "0x912bc870362ca82abdcf684f96b9fe8501ff1490b9ea46ddafd2da1b05e40d93",
    tokenUri: "ipfs://QmdHxUbv6NjtRo8kkaAqoDrz2ZcGmm8Bn378tQXPsZ3ffy",
    tokenId: "1996",
    chainId: "31337",
    chain: "Hardhat",
    name: "Mikasa's Resolve #1996",
    status: NftStatus.COMPLETED,
    description: "Mikasa's Resolve 123",
    image:
      "https://azure-voluntary-cheetah-817.mypinata.cloud/ipfs/QmbeCAWFrRDUVZzKD3PHeTrvQoYrRoR97WGFNpjAapcfXL",
    attributes: [
      { trait_type: "Background", value: "Orange" },
      { trait_type: "Character", value: "Mikasa" },
      { trait_type: "Rarity", value: "Epic" },
    ],
    bids: [],
    standard: TokenStandard.ERC1155,
    isListed: true,
    isFeatured: false,
    contractAddress: "0x8A791620dd6260079BF849Dc5567aDC3F2FdC318",
    createdAt: new Date("2025-05-04T17:19:22.193Z"),
    updatedAt: new Date("2025-05-04T17:19:22.193Z"),
  },
];

// Generate more NFTs for demo
export const allNFTs: NFT[] = [
  ...mockNFTs,
  ...Array(16)
    .fill(0)
    .map((_, i) => ({
      _id: (10000 + i).toString(),
      mintPrice: "0.001224",
      collectionId: "681770cfd0fc57e9a98f8270",
      creator: "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266",
      owner: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
      txHash:
        "0x912bc870362ca82abdcf684f96b9fe8501ff1490b9ea46ddafd2da1b05e40d93",
      tokenUri: "ipfs://QmdHxUbv6NjtRo8kkaAqoDrz2ZcGmm8Bn378tQXPsZ3ffy",
      tokenId: (10000 + i).toString(),
      chainId: "31337",
      chain: "Hardhat",
      name: `Mikasa's Resolve #${10000 + i}`,
      status: NftStatus.COMPLETED,
      description: "Mikasa's Resolve 123",
      image:
        "https://azure-voluntary-cheetah-817.mypinata.cloud/ipfs/QmbeCAWFrRDUVZzKD3PHeTrvQoYrRoR97WGFNpjAapcfXL",
      attributes: [
        {
          trait_type: "Background",
          value: [
            "Olive",
            "Teal",
            "Orange",
            "Purple",
            "Beige",
            "Darkred",
            "Gold",
            "Gray",
          ][i % 8],
        },
        { trait_type: "Character", value: "Mikasa" },
        {
          trait_type: "Rarity",
          value: ["Common", "Rare", "Epic", "Legendary"][i % 4],
        },
      ],
      bids: [],
      standard: TokenStandard.ERC1155,
      isListed: i % 2 === 0,
      isFeatured: i % 4 === 0,
      contractAddress: "0x8A791620dd6260079BF849Dc5567aDC3F2FdC318",
      createdAt: new Date("2025-05-04T17:19:22.193Z"),
      updatedAt: new Date("2025-05-04T17:19:22.193Z"),
    })),
];
