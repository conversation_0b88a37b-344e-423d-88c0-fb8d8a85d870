"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  useAccount,
  useConnect,
  useDisconnect,
  useSignMessage,
  useBalance,
  useSwitchChain,
  useChainId,
} from "wagmi";
import { createSiweMessage } from "viem/siwe";
import Cookies from "js-cookie";
import { toast } from "sonner";
import client from "@/lib/api/apolloClient";
import {
  LogoutDocument,
  NonceDocument,
  VerifyDocument,
} from "@/lib/api/graphql/generated";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  connectWalletRedux,
  disconnectWalletRedux,
} from "@/store/slices/authSlice";
import { supportedChains, getChainById } from "@/lib/blockchain/walletConfig";
import { persistor, RootState } from "@/store/store";

type WalletState =
  | "disconnected"
  | "connecting"
  | "signing"
  | "authenticated"
  | "failed";

export function useWallet() {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { isAuthenticated: reduxIsAuthenticated } = useSelector(
    (state: RootState) => state.auth
  );
  const { address, isConnected } = useAccount();
  const { connect, connectors } = useConnect();
  const { disconnect } = useDisconnect();
  const { signMessageAsync } = useSignMessage();
  const chainId = useChainId();
  const { switchChain, isPending: isSwitchingChain } = useSwitchChain();
  const { data: balanceData, isLoading: isBalanceLoading } = useBalance({
    address,
    chainId,
    query: {
      enabled: !!address && !!chainId,
    },
  });

  const [walletState, setWalletState] = useState<WalletState>("disconnected");

  // Auto reconnect on page load
  useEffect(() => {
    const autoReconnect = async () => {
      const hasToken = !!Cookies.get("auth_token");
      const lastConnectedWallet = localStorage.getItem("lastConnectedWallet");

      if (hasToken && lastConnectedWallet && !isConnected) {
        try {
          const connector = connectors.find(
            (c) => c.id === lastConnectedWallet
          );
          if (connector) {
            toast.info("MetaMask requires password verification", {
              description:
                "Please enter your MetaMask password to reconnect your wallet.",
            });
            await connect({ connector });
          }
        } catch (error) {
          console.error("Auto reconnect failed:", error);
          localStorage.removeItem("lastConnectedWallet");
        }
      }
    };

    autoReconnect();
  }, [connectors, connect, isConnected]);

  // Debug connectors
  useEffect(() => {
    if (connectors.length === 0) {
      console.error("No connectors available. Check walletConfig.ts.");
    }
  }, [connectors]);

  // Đồng bộ walletState
  useEffect(() => {
    const hasToken = !!Cookies.get("auth_token");
    if (
      isConnected &&
      hasToken &&
      reduxIsAuthenticated &&
      walletState !== "authenticated"
    ) {
      setWalletState("authenticated");
    } else if (
      (!isConnected || !hasToken || !reduxIsAuthenticated) &&
      walletState !== "connecting" &&
      walletState !== "signing"
    ) {
      setWalletState("disconnected");
    }
  }, [isConnected, walletState, reduxIsAuthenticated]);

  const connectMutation = useMutation({
    mutationFn: async (walletId: string) => {
      const connector = connectors.find((c) => c.id === walletId);
      if (!connector) throw new Error("Wallet not supported");
      await connect({ connector });
      if (!address || !chainId)
        throw new Error("Connection failed: No address or chainId");
      return { address, chainId };
    },
    onMutate: () => setWalletState("connecting"),
    onSuccess: () => {
      setWalletState("signing");
      toast.success("Wallet connected", {
        description: "Please sign to verify.",
      });
      verifyMutation.mutate();
    },
    onError: (error: Error) => {
      setWalletState("failed");
      toast.error("Connection failed", { description: error.message });
    },
  });

  const verifyMutation = useMutation({
    mutationFn: async () => {
      if (!address || !chainId) throw new Error("No address or chainId");

      // Lấy nonce với no-cache
      console.log("Fetching nonce...");
      const { data } = await client.query({
        query: NonceDocument,
        fetchPolicy: "no-cache", // Ngăn cache Apollo
      });
      const nonce = data?.nonce;
      console.log("Nonce received:", nonce);
      if (!nonce) throw new Error("Failed to fetch nonce");

      const message = createSiweMessage({
        domain: window.location.host,
        address,
        statement: "Sign in with Ethereum.",
        uri: window.location.origin,
        version: "1",
        chainId,
        nonce,
      });

      const current_refreshToken = Cookies.get("refresh_token");

      const signature = await signMessageAsync({ message });
      const { data: verifyData } = await client.mutate({
        mutation: VerifyDocument,
        variables: {
          input: { message, signature, refreshToken: current_refreshToken },
        },
      });

      const { accessToken, refreshToken } = verifyData.verify;
      if (!accessToken || !refreshToken) throw new Error("Verification failed");

      Cookies.set("auth_token", accessToken, {
        expires: 2,
        secure: true,
        sameSite: "strict",
      });
      Cookies.set("refresh_token", refreshToken, {
        expires: 7,
        secure: true,
        sameSite: "strict",
      });

      dispatch(connectWalletRedux({ token: accessToken }));
      return { accessToken, refreshToken };
    },
    onMutate: () => setWalletState("signing"),
    onSuccess: () => {
      setWalletState("authenticated");
      toast.success("Wallet verified");
      queryClient.setQueryData(["walletState"], "authenticated");
    },
    onError: async (error: Error) => {
      setWalletState("failed");
      const errorMessage = error.message || "Verification failed";
      console.error("Verification error:", errorMessage);

      // Xử lý lỗi nonce
      if (errorMessage.includes("Nonce không hợp lệ")) {
        console.log("Invalid nonce detected, clearing cache and retrying...");
        await client.cache.evict({ fieldName: "nonce" });
        await client.cache.gc();
        toast.warning("Session expired, retrying with new nonce...");
        verifyMutation.mutate(); // Thử lại với nonce mới
      } else {
        toast.error("Verification failed", { description: errorMessage });
      }
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await client.mutate({
        mutation: LogoutDocument,
        variables: { wallet: address },
      });
    },
    onSuccess: () => {
      console.log("Server logout successful");
    },
    onError: (error: Error) => {
      console.warn("Server logout failed:", error.message);
    },
  });

  const connectWallet = (walletId: string) => {
    localStorage.setItem("lastConnectedWallet", walletId);
    connectMutation.mutate(walletId);
  };

  const disconnectWallet = async () => {
    try {
      // 1. Ngắt kết nối ví
      disconnect();
      if (isConnected) {
        // throw new Error("Failed to disconnect wallet provider");
      }

      // Remove last connected wallet from storage
      localStorage.removeItem("lastConnectedWallet");

      // 2. Gọi logout server-side
      try {
        await logoutMutation.mutateAsync();
      } catch (serverError) {
        console.warn(
          "Server logout failed, proceeding with local disconnect:",
          serverError
        );
        toast.warning("Server logout failed", {
          description:
            "Wallet disconnected locally, but server sync may be delayed.",
        });
      }

      // 3. Xóa token và state cục bộ
      Cookies.remove("auth_token");
      Cookies.remove("refresh_token");
      await persistor.purge();

      // 4. Xóa cache Apollo và React Query
      await client.cache.evict({ fieldName: "nonce" });
      await client.cache.gc();
      queryClient.clear(); // Hoặc invalidate cụ thể: queryClient.invalidateQueries(['nonce'])

      // 5. Cập nhật Redux và React Query
      dispatch(disconnectWalletRedux());
      queryClient.setQueryData(["walletState"], "disconnected");

      // 6. Cập nhật UI
      setWalletState("disconnected");
      toast.success("Wallet disconnected successfully");
      console.log("Disconnected wallet successfully");
    } catch (error) {
      console.error("Failed to disconnect wallet:", error);
      toast.error("Disconnect failed", {
        description: error instanceof Error ? error.message : "Unknown error",
      });
      setWalletState(isConnected ? "authenticated" : "disconnected");
    }
  };

  const switchNetwork = async (chainId: number): Promise<boolean> => {
    try {
      await switchChain({ chainId });
      return true;
    } catch (error) {
      console.error("Switch chain failed:", error);
      return false;
    }
  };

  const isAuthenticated =
    walletState === "authenticated" &&
    !!Cookies.get("auth_token") &&
    reduxIsAuthenticated;

  return {
    address,
    chainId,
    isConnected,
    walletState,
    isConnecting: connectMutation.isPending,
    isSigning: verifyMutation.isPending,
    isAuthenticated,
    connectWallet,
    disconnectWallet,
    walletBalance: balanceData?.formatted,
    walletBalanceSymbol: balanceData?.symbol,
    isBalanceLoading,
    currentChain: getChainById(chainId),
    supportedChains,
    switchNetwork,
    isSwitchingChain,
  };
}
