"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Check, Link, Upload, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { toast } from "sonner";
import { ArtType } from "@/lib/api/graphql/generated";
interface NFTArtSectionProps {
  isLoading: boolean;
  onArtTypeChange?: (type: ArtType) => void;
  onArtworkChange?: (file: File | null) => void;
  onMetadataUrlChange?: (url: string) => void;
}

export function NFTArtSection({
  isLoading,
  onArtTypeChange,
  onArtworkChange,
  onMetadataUrlChange,
}: NFTArtSectionProps) {
  const [selectedType, setSelectedType] = useState<ArtType>(ArtType.Unique);
  const [showMetadataUrl, setShowMetadataUrl] = useState(true);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [metadataUrl, setMetadataUrl] = useState("");

  useEffect(() => {
    if (onArtTypeChange) onArtTypeChange(selectedType);
  }, [selectedType, onArtTypeChange]);

  useEffect(() => {
    if (onMetadataUrlChange) onMetadataUrlChange(metadataUrl);
  }, [metadataUrl, onMetadataUrlChange]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validation
      const validTypes = ["image/jpeg", "image/png"];
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (!validTypes.includes(file.type)) {
        toast.error("Invalid file type. Only JPG and PNG are allowed.");
        return;
      }
      if (file.size > maxSize) {
        toast.error("File size exceeds 10MB limit.");
        return;
      }

      setSelectedImage(file);
      if (onArtworkChange) onArtworkChange(file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeSelectedImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedImage(null);
    setImagePreview(null);
    if (onArtworkChange) onArtworkChange(null);
    const input = document.getElementById("nft-artwork") as HTMLInputElement;
    if (input) input.value = "";
  };

  const handleMetadataUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setMetadataUrl(url);
    if (onMetadataUrlChange) onMetadataUrlChange(url);
  };

  return (
    <div className="space-y-4 bg-white dark:bg-[#0e0a1a] p-6 rounded-lg border border-gray-200 dark:border-[#3a3450]">
      <Label className="text-gray-900 dark:text-white">NFT Art Type</Label>
      {isLoading ? (
        <Skeleton className="h-80 w-full mt-1" />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div className="flex flex-col gap-4">
            {/* Same Artwork Option */}
            <div
              className={`relative flex items-start gap-4 p-4 rounded-lg border cursor-pointer transition-all ${
                selectedType === ArtType.Same
                  ? "border-gray-300 bg-gray-50 dark:border-[#3a3450] dark:bg-[#1a1525]"
                  : "border-gray-200 bg-transparent dark:border-[#1e1a2a] hover:border-gray-300 dark:hover:border-[#3a3450]"
              }`}
              onClick={() => {
                setSelectedType(ArtType.Same);
                setShowMetadataUrl(false);
                setMetadataUrl(""); // Reset metadata URL khi chuyển sang same
                if (onArtTypeChange) onArtTypeChange(ArtType.Same);
              }}
            >
              <div className="flex-shrink-0">
                <Image
                  src="https://placehold.co/60x60" // Thay bằng đường dẫn thực tế
                  alt="Same artwork character"
                  width={60}
                  height={60}
                  className="rounded-md"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Same Artwork
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  An ERC-1155 collection where everyone mints the same artwork
                </p>
              </div>
              {selectedType === ArtType.Same && (
                <div className="absolute top-3 right-3 bg-green-500 rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>

            {/* Unique Artwork Option */}
            <div
              className={`relative flex items-start gap-4 p-4 rounded-lg border cursor-pointer transition-all ${
                selectedType === ArtType.Unique
                  ? "border-gray-300 bg-gray-50 dark:border-[#3a3450] dark:bg-[#1a1525]"
                  : "border-gray-200 bg-transparent dark:border-[#1e1a2a] hover:border-gray-300 dark:hover:border-[#3a3450]"
              }`}
              onClick={() => {
                setSelectedType(ArtType.Unique);
                setShowMetadataUrl(true);
                setSelectedImage(null); // Reset file khi chuyển sang unique
                setImagePreview(null);
                if (onArtTypeChange) onArtTypeChange(ArtType.Unique);
              }}
            >
              <div className="flex-shrink-0 grid grid-cols-2 gap-1">
                <Image
                  src="https://placehold.co/28x28" // Thay bằng đường dẫn thực tế
                  alt="Unique artwork character 1"
                  width={28}
                  height={28}
                  className="rounded-sm"
                />
                <Image
                  src="https://placehold.co/28x28"
                  alt="Unique artwork character 2"
                  width={28}
                  height={28}
                  className="rounded-sm"
                />
                <Image
                  src="https://placehold.co/28x28"
                  alt="Unique artwork character 3"
                  width={28}
                  height={28}
                  className="rounded-sm"
                />
                <Image
                  src="https://placehold.co/28x28"
                  alt="Unique artwork character 4"
                  width={28}
                  height={28}
                  className="rounded-sm"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Unique Artwork
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  An ERC-721 collection where everyone mints a unique artwork
                </p>
              </div>
              {selectedType === ArtType.Unique && (
                <div className="absolute top-3 right-3 bg-green-500 rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Upload or Metadata URL */}
          <div className="flex flex-col">
            {showMetadataUrl ? (
              <div className="border border-dashed border-gray-300 dark:border-[#3a3450] rounded-lg p-6 flex flex-col items-center justify-center h-full bg-gray-50 dark:bg-transparent">
                <Link className="h-10 w-10 mb-4 text-gray-400" />
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                  Metadata URL
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">
                  Check our{" "}
                  <span className="text-blue-600 dark:text-blue-400 underline cursor-pointer">
                    step-by-step guide
                  </span>{" "}
                  on how to generate and upload your collection assets and
                  metadata.
                </p>
                <Input
                  placeholder="https://ipfs.io/ipfs/<CID>"
                  value={metadataUrl}
                  onChange={handleMetadataUrlChange}
                  className="bg-white dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
            ) : (
              <div className="border border-dashed border-gray-300 dark:border-[#3a3450] rounded-lg p-6 flex flex-col items-center justify-center h-full bg-gray-50 dark:bg-transparent">
                <input
                  type="file"
                  id="nft-artwork"
                  className="hidden"
                  accept="image/jpeg,image/png"
                  onChange={handleFileChange}
                />

                {imagePreview ? (
                  <div className="w-full flex flex-col items-center">
                    <div className="relative w-40 h-40 mb-2">
                      <Image
                        src={imagePreview || "/placeholder.svg"}
                        alt="Artwork preview"
                        fill
                        style={{ objectFit: "contain" }}
                        className="rounded-md"
                      />
                      <button
                        type="button"
                        className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1"
                        onClick={removeSelectedImage}
                      >
                        <X className="h-4 w-4 text-white" />
                      </button>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                      {selectedImage?.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {selectedImage &&
                        (selectedImage?.size / 1024 / 1024).toFixed(2)}{" "}
                      MB
                    </p>
                  </div>
                ) : (
                  <>
                    <Upload className="h-10 w-10 mb-4 text-gray-400" />
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                      Drop your artwork here to upload
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">
                      File types allowed: jpg, png. Max file size: 10MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="bg-white dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-[#2a2535]"
                      onClick={(e) => {
                        e.preventDefault();
                        document.getElementById("nft-artwork")?.click();
                      }}
                    >
                      Choose Image...
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
