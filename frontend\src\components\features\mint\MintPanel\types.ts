import { NftMetadataInput as GraphQLNftMetadataInput } from "@/lib/api/graphql/generated";

export interface MintCostData {
  getMintCost?: {
    success: boolean;
    mintPrice: string;
    estimatedGas: string;
    totalPrice: string;
  };
}

export interface MintCost {
  mintPrice: string;
  estimatedGas: string;
  totalPrice: string;
}

export type NftMetadataInput = GraphQLNftMetadataInput;

export interface FormState {
  agreedToTerms: boolean;
  imageFile: string | null;
  previewUrl: string | null;
  isUploading: boolean;
  name: string;
  description: string;
  attributes: Array<{ trait_type: string; value: string }>;
  amount: number;
  royalty: number;
  gasPrice?: string;
  gasLimit?: string;
  signature?: string;
  nonce?: string;
  batchMetadata: NftMetadataInput[];
}

export interface FormActions {
  resetForm: () => void;
  setAgreedToTerms: (agreed: boolean) => void;
  setImageFile: (file: string | null) => void;
  setName: (name: string) => void;
  setDescription: (description: string) => void;
  setAttributes: (
    attributes: Array<{ trait_type: string; value: string }>
  ) => void;
  setAmount: (amount: number) => void;
  setRoyalty: (royalty: number) => void;
  setGasPrice: (price: string | undefined) => void;
  setGasLimit: (limit: string | undefined) => void;
  setSignature: (signature: string | undefined) => void;
  setNonce: (nonce: string | undefined) => void;
  setBatchMetadata: (metadata: NftMetadataInput[]) => void;
  handleFileUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  handleBatchUpload: (
    event: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  isUploading: boolean;
  setIsUploading: (isUploading: boolean) => void;
  validateForm: () => boolean;
}

export interface FormErrors {
  nameError?: string;
  descriptionError?: string;
  attributesError?: string;
}
