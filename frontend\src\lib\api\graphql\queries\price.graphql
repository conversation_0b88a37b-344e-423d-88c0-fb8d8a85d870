# Queries
query GetPriceHistory($input: PriceHistoryInput!) {
  getPriceHistory(input: $input) {
    contractAddress
    chainId
    timestamp
    open
    high
    low
    close
    volume
    currency
    assetType
    source
  }
}

query GetPriceChangePercentage($input: PriceHistoryInput!) {
  getPriceChangePercentage(input: $input) {
    percentage
    currency
  }
}

# Subscriptions
subscription PriceHistoryUpdated($input: PriceHistoryInput!) {
  priceHistoryUpdated(input: $input) {
    contractAddress
    chainId
    timestamp
    open
    high
    low
    close
    volume
    currency
    assetType
    source
  }
}

subscription PriceChangePercentageUpdated($input: PriceHistoryInput!) {
  priceChangePercentageUpdated(input: $input) {
    percentage
    currency
  }
}
