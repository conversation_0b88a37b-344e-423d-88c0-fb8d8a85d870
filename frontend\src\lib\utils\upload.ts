export const uploadFilePinata = async (file: File) => {
  try {
    const data = new FormData();
    data.append("file", file);

    const res = await fetch("https://api.pinata.cloud/pinning/pinFileToIPFS", {
      method: "POST",
      body: data,
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_PINATA_JWT}`,
      },
    });

    const result = await res.json();
    return `ipfs://${result.IpfsHash}`;
  } catch (error) {
    throw new Error(error as string);
  }
};

// export const uploadMetadataPinata = async (metadata: any) => {
//   try {
//     const res = await fetch("https://api.pinata.cloud/pinning/pinJSONToIPFS", {
//       method: "POST",
//       body: JSON.stringify(metadata),
//       headers: {
//         "Content-Type": "application/json",
//         Authorization: `Bearer ${process.env.NEXT_PUBLIC_PINATA_JWT}`,
//       },
//     });
//     const result = await res.json();
//     return `ipfs://${result.IpfsHash}`;
//   } catch (error) {
//     throw new Error(error as string);
//   }
// };
