import Image from "next/image";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Nft } from "@/lib/api/graphql/generated";

interface MintPanelHistoryProps {
  nfts: Nft[] | undefined;
}

export function MintPanelHistory({ nfts }: MintPanelHistoryProps) {
  return (
    <div className="bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg p-4 border dark:border-gray-800/50 shadow-sm">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Your Minted NFTs
      </h2>

      <ScrollArea className="h-[700px]">
        <div className="text-center py-12">
          {!nfts || nfts.length === 0 ? (
            <>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                No minted NFTs found
              </p>
              <p className="text-gray-500 dark:text-gray-500 text-sm mt-1">
                Mint your first NFT to see it here
              </p>
            </>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {nfts.map((nft) => (
                <div
                  key={nft.id}
                  className="bg-gray-50 dark:bg-[#0f0a19] rounded-lg border border-gray-200 dark:border-gray-800/50 overflow-hidden flex flex-col"
                >
                  <div className="aspect-square relative">
                    <Image
                      src={nft.image}
                      alt={nft.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4 flex-1 flex flex-col">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {nft.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {nft.description}
                    </p>
                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-500">
                        Token ID: {nft.tokenId}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
