export interface ListingSummaryProps {
  totalPrice: string;
}
export const ListingSummary = ({ totalPrice }: ListingSummaryProps) => {
  return (
    <div className="space-y-2 text-base mb-3 border-t border-[#2a203a] pt-4">
      <div className="flex justify-between">
        <div className="font-semibold text-white">You Receive</div>
        <div className="font-bold text-pink-500">{totalPrice} ETH</div>
      </div>
      <div className="flex justify-between text-xs">
        <div>
          Priority fee{" "}
          <span className="text-[10px] px-1 py-0.5 rounded-full bg-muted">
            Standard
          </span>
        </div>
        <div>0 ETH</div>
      </div>
    </div>
  );
};
