import { Skeleton } from "@/components/ui/skeleton";

export function CollectionMediaShowcaseSkeleton() {
  return (
    <div className="bg-gradient-to-b from-gray-50 to-white dark:from-[#0c0916] dark:to-[#0f0a19] rounded-lg p-5 relative w-full max-w-full overflow-hidden xl:max-w-3xl border border-gray-200 dark:border-gray-800/50">
      {/* Main image skeleton */}
      <div className="aspect-square relative rounded-md overflow-hidden">
        <Skeleton className="absolute inset-0 w-full h-full" />
      </div>

      {/* Thumbnails skeleton */}
      <div className="flex gap-3 mt-5 overflow-x-auto pb-2">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="flex-shrink-0 w-48 h-48 relative rounded-md overflow-hidden"
          >
            <Skeleton className="absolute inset-0 w-full h-full" />
          </div>
        ))}
      </div>
    </div>
  );
}
