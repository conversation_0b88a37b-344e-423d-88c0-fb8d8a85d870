"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import type { Stats } from "@/lib/api/graphql/generated";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { HomeBannerSkeleton } from "@/components/features/home/<USER>/HomeBannerSkeleton";
import { HomeBannerError } from "@/components/features/home/<USER>/HomeBannerError";
import { ButtonsBanner } from "@/components/features/home/<USER>/ButtonsBanner";
import { SlideContentBanner } from "@/components/features/home/<USER>/SlideContentBanner";
import { BackgroundBanner } from "@/components/features/home/<USER>/BackgroundBanner";
import { NavigationDots } from "@/components/features/home/<USER>/NavigationDots";
import { SlideCollection } from "@/types/collection.type";
import { StatsDisplay } from "@/components/features/home/<USER>/StatsDisplay";

// Get chain slides function
const getChainSlides = (
  stats: Stats,
  chain: string | null
): SlideCollection[] => {
  const slides: Record<string, SlideCollection[]> = {
    all: [
      {
        title: `Explore ${stats.artworks.toLocaleString()} NFTs Across All Chains`,
        description:
          "Discover thousands of digital assets on Ethereum, Sepolia, Polygon, and more. Join the largest multi-chain NFT marketplace!",
        image:
          "https://images.unsplash.com/photo-1742435456486-3a0059c05e38?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D?500x1200",
        color1: "#00dfd8",
        color2: "#7928ca",
      },
      {
        title: "Join the NFT Revolution",
        description: `Create and trade NFTs across multiple blockchains with ${stats.artists.toLocaleString()} artists and low fees.`,
        image:
          "https://images.unsplash.com/photo-1742435456486-3a0059c05e38?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D?500x1200",
        color1: "#0070f3",
        color2: "#00dfd8",
      },
      {
        title: "Exclusive Auctions & Drops",
        description:
          "Participate in exclusive NFT auctions and get early access to limited edition drops.",
        image:
          "https://images.unsplash.com/photo-1742435456486-3a0059c05e38?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D?500x1200",
        color1: "#ff4d4d",
        color2: "#f9cb28",
      },
    ],
    "11155111": [
      {
        title: `Test ${stats.artworks.toLocaleString()} NFTs on Sepolia`,
        description:
          "Discover unique NFTs on the Sepolia testnet, perfect for testing and development.",
        image:
          "https://images.unsplash.com/photo-1618005198919-d3d4b5a92ead?q=80&w=2074&auto=format&fit=crop",
        color1: "#ff6f61",
        color2: "#6b7280",
      },
      {
        title: "Experiment with Zero Risk",
        description: `Create and trade NFTs on Sepolia with ${stats.artists.toLocaleString()} creators and low gas fees.`,
        image:
          "https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?q=80&w=2074&auto=format&fit=crop",
        color1: "#ff6f61",
        color2: "#6b7280",
      },
    ],
    "1": [
      {
        title: `Discover ${stats.artworks.toLocaleString()} Premium Ethereum NFTs`,
        description:
          "Trade high-value NFTs on Ethereum, the leading blockchain for digital assets.",
        image:
          "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?q=80&w=2064&auto=format&fit=crop",
        color1: "#3b82f6",
        color2: "#8b5cf6",
      },
      {
        title: "Join Ethereum's NFT Elite",
        description: `Own exclusive NFTs from ${stats.artists.toLocaleString()} top artists on the Ethereum blockchain.`,
        image:
          "https://images.unsplash.com/photo-1634973357973-f2ed2657db3c?q=80&w=2072&auto=format&fit=crop",
        color1: "#3b82f6",
        color2: "#8b5cf6",
      },
    ],
    "31337": [
      {
        title: `Discover ${stats.artworks.toLocaleString()} Premium Ethereum NFTs`,
        description:
          "Trade high-value NFTs on Ethereum, the leading blockchain for digital assets.",
        image:
          "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?q=80&w=2064&auto=format&fit=crop",
        color1: "#3b82f6",
        color2: "#8b5cf6",
      },
      {
        title: "Join Ethereum's NFT Elite",
        description: `Own exclusive NFTs from ${stats.artists.toLocaleString()} top artists on the Ethereum blockchain.`,
        image:
          "https://images.unsplash.com/photo-1634973357973-f2ed2657db3c?q=80&w=2072&auto=format&fit=crop",
        color1: "#3b82f6",
        color2: "#8b5cf6",
      },
    ],
  };

  return slides[chain || "all"] || slides["all"];
};

export function HomeBanner() {
  const { stats, isLoading, error, selectedChain } = useSelector(
    (state: RootState) => state.home
  );
  const [currentSlide, setCurrentSlide] = useState(0);
  const slides = getChainSlides(stats, selectedChain?.id.toString() || "all");

  // Reset currentSlide when chain changes
  useEffect(() => {
    setCurrentSlide(0); // Reset to first slide when chain changes
  }, [selectedChain]);

  // Slide interval effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  const handleDotClick = (index: number) => {
    setCurrentSlide(index);
  };
  if (isLoading) return <HomeBannerSkeleton />;
  if (error)
    return (
      <HomeBannerError error={error} onRetry={() => window.location.reload()} />
    );
  // Safeguard: Ensure slides is not empty
  if (!slides || slides.length === 0) {
    return <div>Loading slides...</div>;
  }
  return (
    <div className="relative w-full h-[500px] rounded-xl overflow-hidden mb-12 group bg-gray-50 dark:bg-[#1A1F2C] border border-gray-200 dark:border-white/10">
      {/* Background with gradient overlay */}
      {slides.map((slide, index) => (
        <BackgroundBanner
          key={index}
          slide={slide}
          currentSlide={currentSlide}
          index={index}
        />
      ))}

      {/* Animated particles */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <motion.div
          className="absolute w-64 h-64 rounded-full blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, 20, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
          style={{
            top: "-20px",
            left: "-20px",
            background: `linear-gradient(45deg, ${
              slides[currentSlide]?.color1 || "#000"
            }, ${slides[currentSlide]?.color2 || "#000"})`,
          }}
        />
        <motion.div
          className="absolute w-96 h-96 rounded-full blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, -30, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 0.5,
          }}
          style={{
            bottom: "-40px",
            right: "-20px",
            background: `linear-gradient(45deg, ${
              slides[currentSlide]?.color2 || "#000"
            }, ${slides[currentSlide]?.color1 || "#000"})`,
          }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-20 flex flex-col justify-center h-full p-8 md:p-12 max-w-2xl">
        {slides.map((slide, index) => (
          <SlideContentBanner
            key={index}
            slide={slide}
            isActive={currentSlide === index}
          />
        ))}
        <ButtonsBanner chain={selectedChain?.name || null} />
        <StatsDisplay
          stats={stats}
          currentSlide={currentSlide}
          slides={slides}
        />
        <NavigationDots
          slides={slides}
          currentSlide={currentSlide}
          onDotClick={handleDotClick}
        />
      </div>
    </div>
  );
}
