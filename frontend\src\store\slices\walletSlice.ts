import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface WalletState {
  [collectionId: string]: {
    wallet: string;
    mintCount: number;
    ownedNfts: string[]; // nftId from Nft entity
    mintPhase?: {
      isPublic: boolean;
      price: string;
      startTime: number;
      endTime: number;
      mintLimit: number;
    };
    allowlistStage?: {
      stageId: string;
      price: string;
      startTime: number;
      endTime: number;
      mintLimit: number;
      mintedInStage: string;
    };
  };
}

const initialState: WalletState = {};

export const walletSlice = createSlice({
  name: "wallet",
  initialState,
  reducers: {
    updateWallet: (
      state,
      action: PayloadAction<{
        collectionId: string | undefined;
        wallet: string | undefined;
        mintCount?: number;
        ownedNfts?: string[]; // nftId array
        mintPhase?: {
          isPublic: boolean;
          price: string;
          startTime: number;
          endTime: number;
          mintLimit: number;
        };
        allowlistStage?: {
          stageId: string;
          price: string;
          startTime: number;
          endTime: number;
          mintLimit: number;
          mintedInStage: string;
        };
      }>
    ) => {
      const {
        collectionId,
        wallet,
        mintCount,
        ownedNfts,
        mintPhase,
        allowlistStage,
      } = action.payload;
      if (!collectionId || !wallet) {
        return;
      }

      // Initialize collection if not exists
      if (!state[collectionId]) {
        state[collectionId] = {
          wallet,
          mintCount: 0,
          ownedNfts: [],
        };
      }

      // Update mintCount
      if (typeof mintCount === "number") {
        state[collectionId].mintCount = Math.max(
          0,
          state[collectionId].mintCount + mintCount
        );
      }

      // Update ownedNfts
      if (ownedNfts && ownedNfts.length > 0) {
        ownedNfts.forEach((nftId) => {
          if (!state[collectionId].ownedNfts.includes(nftId)) {
            state[collectionId].ownedNfts.push(nftId);
          }
        });
      }

      // Remove nftIds (for sales, deletions)
      if (ownedNfts && ownedNfts.length === 0) {
        state[collectionId].ownedNfts = state[collectionId].ownedNfts.filter(
          (id) => !ownedNfts.includes(id)
        );
      }

      // Update mint phase
      if (mintPhase) {
        state[collectionId].mintPhase = mintPhase;
      }

      // Update allowlist stage
      if (allowlistStage) {
        state[collectionId].allowlistStage = allowlistStage;
      }
    },
    removeOwnedNft: (
      state,
      action: PayloadAction<{
        collectionId: string;
        nftId: string;
      }>
    ) => {
      const { collectionId, nftId } = action.payload;
      if (state[collectionId]) {
        state[collectionId].ownedNfts = state[collectionId].ownedNfts.filter(
          (id) => id !== nftId
        );
      }
    },
    resetWallet: (state, action: PayloadAction<{ collectionId: string }>) => {
      delete state[action.payload.collectionId];
    },
    updateMintPhase: (
      state,
      action: PayloadAction<{
        collectionId: string;
        mintPhase: {
          isPublic: boolean;
          price: string;
          startTime: number;
          endTime: number;
          mintLimit: number;
        };
      }>
    ) => {
      const { collectionId, mintPhase } = action.payload;
      if (state[collectionId]) {
        state[collectionId].mintPhase = mintPhase;
      }
    },
    updateAllowlistStage: (
      state,
      action: PayloadAction<{
        collectionId: string;
        allowlistStage: {
          stageId: string;
          price: string;
          startTime: number;
          endTime: number;
          mintLimit: number;
          mintedInStage: string;
        };
      }>
    ) => {
      const { collectionId, allowlistStage } = action.payload;
      if (state[collectionId]) {
        state[collectionId].allowlistStage = allowlistStage;
      }
    },
  },
});

export const {
  updateWallet,
  removeOwnedNft,
  resetWallet,
  updateMintPhase,
  updateAllowlistStage,
} = walletSlice.actions;

export default walletSlice.reducer;
