"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useMintValidation } from "@/hooks/useMintValidation";

interface AllowlistFormProps {
  isAllowlistMint: boolean;
  signature?: string;
  setSignature: (signature: string | undefined) => void;
  nonce?: string;
  setNonce: (nonce: string | undefined) => void;
}

export function AllowlistForm({
  isAllowlistMint,
  signature,
  setSignature,
  nonce,
  setNonce,
}: AllowlistFormProps) {
  const { validateSignature, validateNonce } = useMintValidation({
    address: "",
    collection: null,
    isSameArtType: false,
    isAllowlistMint: false,
    amount: 1,
    attributes: [],
    name: "",
    description: "",
    batchMetadata: [],
  });

  if (!isAllowlistMint) {
    return null;
  }

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <Label className="text-sm text-white flex items-center gap-1">
          Allowlist Mint Credentials{" "}
          <span className="text-pink-500">*</span>
        </Label>
        <span className="text-sm text-gray-400">Required</span>
      </div>
      
      <div className="space-y-2">
        <div className="relative">
          <Input
            id="signature"
            value={signature || ""}
            onChange={(e) => {
              const value = e.target.value;
              setSignature(value);
              if (value && !validateSignature(value)) {
                setSignature(undefined);
              }
            }}
            placeholder="Signature (0x...)"
            className={cn(
              "h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white placeholder:text-gray-600 dark:bg-[#0a0612] dark:border-gray-900/50",
              signature &&
                !/^0x[0-9a-fA-F]{130}$/.test(signature) &&
                "border-red-800 focus-visible:ring-red-800"
            )}
            aria-invalid={
              signature && !validateSignature(signature) ? "true" : "false"
            }
          />
          {signature && validateSignature(signature) && (
            <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
        </div>
        <p className="text-sm text-gray-400">
          65-byte hex signature provided by the allowlist
        </p>
      </div>
      
      <div className="space-y-2">
        <div className="relative">
          <Input
            id="nonce"
            value={nonce || ""}
            onChange={(e) => {
              const value = e.target.value;
              setNonce(value);
              if (value && !validateNonce(value)) {
                setNonce(undefined);
              }
            }}
            placeholder="Nonce"
            type="number"
            min={0}
            className={cn(
              "h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white placeholder:text-gray-600 dark:bg-[#0a0612] dark:border-gray-900/50",
              nonce &&
                (!Number.isInteger(Number(nonce)) || Number(nonce) < 0) &&
                "border-red-800 focus-visible:ring-red-800"
            )}
            aria-invalid={nonce && !validateNonce(nonce) ? "true" : "false"}
          />
          {nonce && validateNonce(nonce) && (
            <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
        </div>
        <p className="text-sm text-gray-400">
          Nonce value provided by the allowlist
        </p>
      </div>
    </div>
  );
}
