"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RefreshCw, Share, Maximize, X, ChevronDown } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Nft } from "@/lib/api/graphql/generated";

interface SellerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  nft: Nft;
}

// Sample data for offers and activities
const offers = [
  {
    price: "0.069",
    from: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    expiresIn: "2 days",
  },
];

const getActivities = (nft: Nft) => [
  {
    type: "Mint",
    seller: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    buyer: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266",
    price: nft.mintPrice || "0",
    time: "2 days ago",
  },
];

export default function SellerModal({
  open,
  onOpenChange,
  nft,
}: SellerModalProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAttributes, setShowAttributes] = useState(false);

  if (!nft) return null;

  return (
    <>
      <Dialog
        open={open}
        onOpenChange={(newOpen) => {
          onOpenChange(newOpen);
        }}
      >
        <DialogContent
          className="[&>button]:hidden h-[80vh] sm:w-[800px] sm:max-w-[800px] p-0 gap-0 overflow-hidden bg-[#121212] text-white border-gray-800 flex flex-col"
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DialogTitle className="sr-only">NFT Details</DialogTitle>
          <div className="flex items-center justify-between p-4 border-b border-gray-800">
            <h2 className="text-xl font-bold">{nft.name}</h2>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <Share className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex items-center px-4 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-gray-400">PAWS VOUCHERS</span>
              <div className="flex items-center">
                <span className="inline-block h-4 w-4 rounded-full bg-purple-500"></span>
                <span className="inline-block h-4 w-4 rounded-full bg-pink-500 -ml-1"></span>
              </div>
            </div>
          </div>

          <Tabs
            defaultValue="overview"
            className="w-full flex-1 overflow-hidden flex flex-col"
          >
            <div className="border-b border-gray-800 px-4">
              <TabsList className="bg-transparent h-10 p-0">
                <TabsTrigger
                  value="overview"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="offers"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Offers{" "}
                  <span className="ml-1 rounded-full bg-gray-800 px-1.5 text-xs">
                    5
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Activity
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="overview"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-0 h-full p-3">
                {/* Left side - NFT Image */}
                <div className="relative aspect-square w-full h-full">
                  <Image
                    src={nft.image}
                    alt={nft.name}
                    width={1800}
                    height={1800}
                    className="object-cover w-full h-full"
                    priority
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 h-8 w-8 bg-gray-900/80 backdrop-blur-sm text-white hover:bg-gray-800"
                    onClick={() => setIsExpanded(true)}
                  >
                    <Maximize className="h-4 w-4" />
                  </Button>
                </div>

                {/* Right side - NFT Details */}
                <div className="p-4 flex flex-col bg-[#121212]">
                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-400">Total Price</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 px-2 text-gray-400 hover:text-white"
                      >
                        Details <ChevronDown className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="text-2xl font-bold">
                        {nft.mintPrice} SOL
                      </div>
                      <div className="text-gray-400">
                        ${(Number.parseFloat(nft.mintPrice) * 238).toFixed(2)}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Button
                      variant="default"
                      className="w-full bg-pink-600 hover:bg-pink-700"
                    >
                      Update Listing
                    </Button>

                    <Button
                      variant="outline"
                      className="w-full border-gray-700 text-white hover:bg-gray-800"
                    >
                      Delist
                    </Button>
                  </div>

                  <div className="mt-4">
                    <Button
                      variant="secondary"
                      className="w-full bg-gray-800 hover:bg-gray-700 text-white"
                    >
                      Review top offer for 0.069 SOL
                    </Button>
                  </div>

                  <div className="mt-4">
                    <Button
                      variant="outline"
                      className="w-full border-gray-700 text-white hover:bg-gray-800"
                    >
                      Transfer
                    </Button>
                  </div>

                  <div className="mt-6 grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-400">List Price</div>
                      <div className="flex items-center">
                        <div className="font-medium">{nft.mintPrice}</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Floor Price</div>
                      <div className="flex items-center">
                        <div className="font-medium">0.07</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Top Offer</div>
                      <div className="flex items-center">
                        <div className="font-medium">0.069</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Floor Diff.</div>
                      <div className="flex items-center">
                        <div className="font-medium">52.9%</div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Button
                      variant="ghost"
                      className="w-full justify-between border-t border-gray-800 pt-2 text-white hover:bg-gray-800"
                      onClick={() => setShowAttributes(!showAttributes)}
                    >
                      <span className="font-medium">Attributes</span>
                      <span className="flex items-center">
                        {nft.attributes?.length || 0}{" "}
                        <ChevronDown
                          className={`ml-1 h-4 w-4 transition-transform ${
                            showAttributes ? "rotate-180" : ""
                          }`}
                        />
                      </span>
                    </Button>
                    {showAttributes && (
                      <div className="mt-4 grid grid-cols-2 gap-2">
                        {nft.attributes?.map((attr, index) => (
                          <div
                            key={index}
                            className="bg-gray-800/50 p-3 rounded-lg"
                          >
                            <div className="text-xs text-gray-400">
                              {attr.trait_type}
                            </div>
                            <div className="text-sm font-medium mt-1">
                              {attr.value}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="offers"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="p-4">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-left text-gray-400 text-sm">
                        <th className="pb-2">Price</th>
                        <th className="pb-2">From</th>
                        <th className="pb-2">Expires In</th>
                        <th className="pb-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {offers.map((offer, index) => (
                        <tr key={index} className="border-t border-gray-800">
                          <td className="py-3">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="font-medium truncate max-w-[120px]">
                                    {offer.price} SOL
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{offer.price} SOL</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="text-xs text-gray-400 truncate max-w-[120px]">
                                    $
                                    {(
                                      Number.parseFloat(offer.price) * 238
                                    ).toFixed(2)}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    $
                                    {(
                                      Number.parseFloat(offer.price) * 238
                                    ).toFixed(2)}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="py-3">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-pink-500 truncate max-w-[100px] inline-block">
                                    {offer.from}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{offer.from}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="py-3">{offer.expiresIn}</td>
                          <td className="py-3">
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-gray-700 text-white hover:bg-gray-800"
                            >
                              Accept
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="activity"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="p-4">
                <div className="flex justify-end mb-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px] bg-[#1a1a1a] border-gray-800 text-white">
                      <SelectValue placeholder="Filter" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1a1a1a] border-gray-800 text-white">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="list">List</SelectItem>
                      <SelectItem value="sell">Sell</SelectItem>
                      <SelectItem value="mint">Mint</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-left text-gray-400 text-sm">
                        <th className="pb-2">Type</th>
                        <th className="pb-2">Seller</th>
                        <th className="pb-2">Buyer</th>
                        <th className="pb-2">Price</th>
                        <th className="pb-2">Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getActivities(nft).map((activity, index) => (
                        <tr key={index} className="border-t border-gray-800">
                          <td className="py-3">
                            <span
                              className={`px-2 py-1 text-xs rounded ${
                                activity.type === "List"
                                  ? "bg-red-900/30 text-red-500"
                                  : activity.type === "Sell"
                                  ? "bg-red-900/30 text-red-500"
                                  : "bg-green-900/30 text-green-500"
                              }`}
                            >
                              {activity.type}
                            </span>
                          </td>
                          <td className="py-3">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-pink-500 truncate max-w-[100px] inline-block">
                                    {activity.seller}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{activity.seller}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="py-3">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-pink-500 truncate max-w-[100px] inline-block">
                                    {activity.buyer}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{activity.buyer}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="py-3">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="font-medium truncate max-w-[120px]">
                                    {activity.price} SOL
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{activity.price} SOL</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="text-xs text-gray-400 truncate max-w-[120px]">
                                    $
                                    {(
                                      Number.parseFloat(activity.price) * 238
                                    ).toFixed(2)}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    $
                                    {(
                                      Number.parseFloat(activity.price) * 238
                                    ).toFixed(2)}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="py-3">{activity.time}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="border-t border-gray-800 p-4 flex items-center justify-between text-xs text-gray-400">
            <div className="flex items-center gap-4">
              <div>PAWS VOUCHERS</div>
              <div>
                Floor: <span className="text-white font-medium">0.07 SOL</span>{" "}
                undefined%
              </div>
              <div>
                Top Offer:{" "}
                <span className="text-white font-medium">0.07 SOL</span>
              </div>
              <div>
                24hr Vol:{" "}
                <span className="text-white font-medium">831.6 SOL</span>
              </div>
              <div>
                24hr Sales: <span className="text-white font-medium">9052</span>
              </div>
              <div>
                Listed: <span className="text-white font-medium">3673</span> 13%
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      {isExpanded && (
        <div className="fixed inset-0 z-[99999]">
          <div className="absolute inset-0 bg-black/70" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative max-w-[90vw] max-h-[90vh]">
              <Image
                src={nft.image}
                alt={`${nft.name} Expanded`}
                width={1800}
                height={1800}
                className="object-contain w-auto h-auto max-w-full max-h-[90vh]"
                priority
              />
            </div>
          </div>
          <div className="absolute top-5 right-5 z-20">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(false)}
              className="text-white hover:bg-white/10 dark:hover:bg-white/5 h-10 w-10 cursor-pointer"
              aria-label="Close expanded view"
            >
              <X className="h-7 w-7" />
            </Button>
          </div>
        </div>
      )}
    </>
  );
}
