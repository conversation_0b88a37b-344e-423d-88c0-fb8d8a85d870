// utils/date.ts
import { z } from "zod";

// Schema để validate input
const durationSchema = z
  .string()
  .regex(/^\d+$/, "Must be a non-negative integer");

export function calculateEndDate(
  startDate: string,
  durationDays: string,
  durationHours: string
): Date {
  // Validate inputs
  if (!startDate || !isValidDateString(startDate)) {
    throw new Error(`Invalid or missing startDate: ${startDate}`);
  }

  const daysResult = durationSchema.safeParse(durationDays);
  if (!daysResult.success) {
    throw new Error(`Invalid durationDays: ${durationDays}`);
  }

  const hoursResult = durationSchema.safeParse(durationHours);
  if (!hoursResult.success) {
    throw new Error(`Invalid durationHours: ${durationHours}`);
  }

  try {
    const start = new Date(startDate);
    if (isNaN(start.getTime())) {
      throw new Error("Invalid Date object created from startDate");
    }

    const days = parseInt(durationDays, 10);
    const hours = parseInt(durationHours, 10);

    const end = new Date(start);
    end.setUTCDate(start.getUTCDate() + days);
    end.setUTCHours(start.getUTCHours() + hours);

    return end;
  } catch (error) {
    console.error("Error calculating end date:", {
      error,
      startDate,
      durationDays,
      durationHours,
    });
    throw error; // Để component xử lý
  }
}

function isValidDateString(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}
