export interface EIP712Domain {
  name: string;
  version: string;
  chainId: number;
  verifyingContract: string;
}

export interface EIP712Types {
  OffchainListing: Array<{ name: string; type: string }>;
}

export interface EIP712Value {
  seller: string;
  contractAddress: string;
  tokenIds: string[];
  amounts: string[];
  prices: string[];
  standard: string;
  expiryTimestamp: string;
  nonce: string;
  metadataURI?: string;
  metadataURIsHash?: string;
}

export interface EIP712SignData {
  type: "EIP712";
  domain: EIP712Domain;
  types: EIP712Types;
  value: EIP712Value;
}
