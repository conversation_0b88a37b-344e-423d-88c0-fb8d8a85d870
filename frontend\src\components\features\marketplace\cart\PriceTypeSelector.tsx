import { PriceType } from "@/lib/api/graphql/generated";
export interface PriceTypeSelectorProps {
  activeType: PriceType | null;
  onTypeChange: (type: PriceType) => void;
  loading: boolean;
  disabled: boolean;
}
export const PriceTypeSelector = ({
  activeType,
  onTypeChange,
  loading,
  disabled,
}: PriceTypeSelectorProps) => {
  return (
    <div className="flex gap-3 mb-4 w-full">
      {[PriceType.FloorPrice, PriceType.LastedPrice].map((type) => (
        <button
          key={type}
          className={`w-full rounded-lg px-5 py-2 text-sm font-semibold transition border ${
            activeType === type
              ? "bg-pink-600 text-white border-pink-600 shadow"
              : "bg-[#231b2e] text-muted-foreground border-[#2a203a] hover:bg-[#2a203a]"
          }`}
          onClick={() => onTypeChange(type)}
          disabled={loading || disabled}
        >
          {type}
          {loading && type === activeType && " (Loading...)"}
        </button>
      ))}
    </div>
  );
};
