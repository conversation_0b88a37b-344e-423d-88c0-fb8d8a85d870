import { Loader2, <PERSON>Circle, Circle } from "lucide-react";
import Image from "next/image";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import type { Nft } from "@/lib/api/graphql/generated";

// Common interface for modal props
export interface BaseModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  items: Nft[];
  onRemoveItem: (id: string) => void;
}

// Common StepRow component
export function StepRow({
  step,
  current,
  label,
  desc,
  hidden = false,
}: {
  step: number;
  current: number;
  label: string;
  desc?: string;
  hidden?: boolean;
}) {
  if (hidden) return null;
  let icon = <Circle className="h-6 w-6 text-muted-foreground" />;
  if (current > step) icon = <CheckCircle className="h-6 w-6 text-pink-500" />;
  else if (current === step)
    icon = <Loader2 className="h-6 w-6 text-pink-500 animate-spin" />;
  return (
    <div
      className={
        "flex items-start gap-4 " +
        (current === step
          ? "font-bold text-base text-white"
          : current > step
          ? "text-pink-500 font-semibold"
          : "text-muted-foreground font-normal")
      }
    >
      <div className="pt-0.5">{icon}</div>
      <div>
        <div
          className={
            current === step ? "text-base font-bold" : "text-base font-semibold"
          }
        >
          {label}
        </div>
        {desc && (
          <div className="text-xs text-muted-foreground mt-1">{desc}</div>
        )}
      </div>
    </div>
  );
}

// Common NFT Item component
export function NftItem({
  item,
  onRemoveItem,
  price,
  onPriceChange,
  error,
  showPriceInput = false,
}: {
  item: Nft;
  onRemoveItem: (id: string) => void;
  price?: string;
  onPriceChange?: (id: string, value: string) => void;
  error?: string;
  showPriceInput?: boolean;
}) {
  return (
    <div className="flex items-center gap-3 group bg-[#231b2e] rounded-xl px-3 py-3 border border-[#2a203a] shadow-sm">
      <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 bg-[#18141c] border border-[#2a203a] flex items-center justify-center">
        <Image
          src={
            item.image ||
            "https://images.unsplash.com/photo-1742435456486-3a0059c05e38?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D?20x20"
          }
          alt={item.name || item.id || "NFT image"}
          width={40}
          height={40}
          className="w-10 h-10 object-cover"
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-base font-bold truncate text-white">
          300K PAWS Voucher
        </div>
        <div className="text-xs text-muted-foreground">Top Trait: 0.076</div>
      </div>
      {showPriceInput && onPriceChange && (
        <>
          <input
            type="text"
            value={price}
            onChange={(e) => onPriceChange(item.id, e.target.value)}
            placeholder="0.1"
            className={`bg-[#18141c] border ${
              error ? "border-red-500" : "border-[#2a203a]"
            } rounded-lg px-3 py-1 w-20 text-base text-white text-right focus:outline-none focus:border-pink-500 transition mr-2`}
          />
          {error && <span className="text-red-500 text-xs">{error}</span>}
          <span className="text-base text-white font-bold mr-2">
            {item.currency}
          </span>
        </>
      )}
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 hover:bg-pink-600 hover:text-white"
        onClick={() => onRemoveItem(item.id)}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
}

// Common modal container
export function ModalContainer({
  children,
  title,
  itemCount,
  onClose,
  onClearAll,
  showClearButton = false,
}: {
  children: React.ReactNode;
  title: string;
  itemCount: number;
  onClose: () => void;
  onClearAll?: () => void;
  showClearButton?: boolean;
}) {
  return (
    <div className="fixed right-6 bottom-8 z-50 w-[480px] max-w-[99vw] rounded-2xl border border-neutral-700 bg-[#18141c] p-8 shadow-2xl animate-in fade-in-0 zoom-in-95 slide-in-from-right-1/2">
      <div className="flex items-center justify-between pb-4 mb-4 border-b border-[#28213a]">
        <div className="text-2xl font-bold tracking-tight text-white">
          {title}{" "}
          <span className="text-base font-normal text-muted-foreground">
            ({itemCount})
          </span>
        </div>
        <div className="flex items-center gap-2">
          {showClearButton && onClearAll && (
            <button
              className="text-xs text-muted-foreground hover:text-pink-500 transition"
              onClick={onClearAll}
            >
              Clear
            </button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9 rounded-full hover:bg-[#2a203a]"
            onClick={onClose}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>
      {children}
    </div>
  );
}
