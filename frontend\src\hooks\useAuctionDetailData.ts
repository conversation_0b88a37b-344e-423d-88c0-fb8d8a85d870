"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { mockNFTs } from "@/data/mockData";

// This custom hook encapsulates data fetching and state management logic
// for the AuctionDetail component. Separating this logic improves:
// 1. Separation of Concerns: UI components focus on rendering.
// 2. Reusability: This hook could potentially be reused or adapted.
// 3. Testability: Data logic can be tested independently of the UI.
// 4. Maintainability: Easier to manage data-related code.
export function useAuctionDetailData(auctionId: string) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [auction, setAuction] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [bidAmount, setBidAmount] = useState("");
  const [timeRemaining, setTimeRemaining] = useState("");
  const [isLiked, setIsLiked] = useState(false);

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Simulate API call to get auction details
    const timer = setTimeout(() => {
      const index = Number.parseInt(auctionId.split("-")[1] || "0");
      const nft = mockNFTs[index % mockNFTs.length];

      if (nft) {
        const hoursToAdd = Math.floor(Math.random() * 72) + 1;
        const endTime = new Date();
        endTime.setHours(endTime.getHours() + hoursToAdd);
        const startingBid =
          Number.parseFloat(nft.price) * (Math.random() * 0.5 + 0.8);
        const bidCount = Math.floor(Math.random() * 20);
        const bidHistory = Array(bidCount)
          .fill(null)
          .map((_, i) => {
            const bidTime = new Date(endTime);
            bidTime.setHours(bidTime.getHours() - (i + 1) * Math.random() * 5);
            return {
              id: `bid-${i}`,
              amount: startingBid + (bidCount - i) * 0.1,
              bidder: `User${Math.floor(Math.random() * 1000)}`,
              time: bidTime,
            };
          })
          .sort((a, b) => b.amount - a.amount);

        const mockAuction = {
          ...nft,
          id: auctionId,
          endTime,
          startingBid,
          currentBid:
            bidHistory.length > 0 ? bidHistory[0].amount : startingBid,
          bidCount,
          highestBidder: bidHistory.length > 0 ? bidHistory[0].bidder : null,
          bidHistory,
        };

        setAuction(mockAuction);
        setBidAmount((mockAuction.currentBid + 0.1).toFixed(2));
        setLoading(false);
      }
    }, 1500);

    return () => clearTimeout(timer);
  }, [auctionId]);

  useEffect(() => {
    if (!auction) return;

    const updateTimer = () => {
      const now = new Date();
      const diff = auction.endTime.getTime() - now.getTime();
      if (diff <= 0) {
        setTimeRemaining("Auction ended");
        if (timerRef.current) clearInterval(timerRef.current);
        return;
      }
      const h = Math.floor(diff / (1000 * 60 * 60));
      const m = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const s = Math.floor((diff % (1000 * 60)) / 1000);
      setTimeRemaining(
        `${h.toString().padStart(2, "0")}:${m
          .toString()
          .padStart(2, "0")}:${s.toString().padStart(2, "0")}`
      );
    };
    updateTimer();
    timerRef.current = setInterval(updateTimer, 1000);
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [auction]);

  const handleLikeToggle = useCallback(() => setIsLiked((prev) => !prev), []);

  const handleBidSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (Number.parseFloat(bidAmount) <= auction.currentBid) {
        alert("Please enter a bid higher than the current bid");
        return;
      }
      alert(`Bid of ${bidAmount} ${auction.chain.symbol} placed successfully!`);
    },
    [bidAmount, auction]
  );

  return {
    auction,
    loading,
    bidAmount,
    setBidAmount,
    timeRemaining,
    isLiked,
    handleLikeToggle,
    handleBidSubmit,
  };
}
