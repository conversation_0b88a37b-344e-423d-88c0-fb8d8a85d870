import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface MintPanelTabsProps {
  address: string | undefined;
  activeTab: string;
  onTabChange: (value: string) => void;
  children: React.ReactNode;
}

export function MintPanelTabs({
  address,
  activeTab,
  onTabChange,
  children,
}: MintPanelTabsProps) {
  return (
    <Tabs
      defaultValue="mint"
      className="w-full"
      onValueChange={onTabChange}
      value={activeTab}
    >
      <TabsList
        className={`grid w-full max-w-md mx-auto mb-6 bg-white border border-gray-200 dark:bg-[#1a1625] dark:border-gray-800/50 ${
          address ? "grid-cols-2" : "grid-cols-1"
        }`}
      >
        <TabsTrigger
          value="mint"
          className="data-[state=active]:bg-pink-500 dark:data-[state=active]:bg-pink-600 data-[state=active]:text-white text-sm"
        >
          Mint Your NFT
        </TabsTrigger>
        {address && (
          <TabsTrigger
            value="history"
            className="data-[state=active]:bg-pink-500 dark:data-[state=active]:bg-pink-600 data-[state=active]:text-white text-sm"
          >
            Mint History
          </TabsTrigger>
        )}
      </TabsList>
      {children}
    </Tabs>
  );
}
