/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useState, useEffect } from "react";
import type { CarouselApi } from "@/components/ui/carousel";
import { CollectionCard } from "./CollectionCard";
import type { RootState } from "@/store/store";
import { useSelector } from "react-redux";
import Autoplay from "embla-carousel-autoplay";
import { CollectionCarouselSkeleton } from "@/components/features/home/<USER>/CollectionCarouselSkeleton";
import { CollectionCarouselError } from "@/components/features/home/<USER>/CollectionCarouselError";
import { CollectionCarouselEmpty } from "@/components/features/home/<USER>/CollectionCarouselEmpty";
export function CollectionCarousel() {
  const { collections, isLoading, error } = useSelector(
    (state: RootState) => state.home
  );
  const [hoveredId, setHoveredId] = useState<string | null>(null); // Update to string for id
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) return;
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());
    api.on("select", () => setCurrent(api.selectedScrollSnap()));
  }, [api]);
  if (isLoading) return <CollectionCarouselSkeleton />;

  if (error)
    return (
      <CollectionCarouselError
        error={error}
        onRetry={() => window.location.reload()}
      />
    );

  if (!collections.length) return <CollectionCarouselEmpty />;

  return (
    <div className="relative -mx-4 px-4">
      <Carousel
        opts={{ align: "start", loop: true }}
        setApi={setApi}
        className="w-full"
        plugins={[
          Autoplay({
            delay: 2000,
          }),
        ]}
      >
        <CarouselContent className="-ml-2">
          {collections.map((item) => (
            <CarouselItem
              key={item.id}
              className="pl-2 md:basis-1/3 lg:basis-1/5"
            >
              <CollectionCard
                item={item}
                isHovered={hoveredId === item.id}
                onMouseEnter={() => setHoveredId(item.id)}
                onMouseLeave={() => setHoveredId(null)}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute -left-4 bg-white/80 dark:bg-[#1A1F2C]/80 text-gray-900 dark:text-white backdrop-blur-sm hover:bg-white/90 dark:hover:bg-[#1A1F2C]/90" />
        <CarouselNext className="absolute -right-4 bg-white/80 dark:bg-[#1A1F2C]/80 text-gray-900 dark:text-white backdrop-blur-sm hover:bg-white/90 dark:hover:bg-[#1A1F2C]/90" />
      </Carousel>
    </div>
  );
}
