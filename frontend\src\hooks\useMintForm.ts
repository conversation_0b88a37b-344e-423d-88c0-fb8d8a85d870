"use client";

import { useState, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { NftMetadataInput, Collection } from "@/lib/api/graphql/generated";
import { FormState, FormActions, FormErrors } from "@/types/mint.types";

interface UseMintFormProps {
  collection: Collection | null;
  isSameArtType: boolean;
  isAllowlistMint: boolean;
}

// Hook xử lý metadata
function useMetadata(collection: Collection | null, isSameArtType: boolean) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [attributes, setAttributes] = useState<
    Array<{ trait_type: string; value: string }>
  >([]);
  const [nameError, setNameError] = useState<string | undefined>(undefined);
  const [descriptionError, setDescriptionError] = useState<string | undefined>(
    undefined
  );
  const [attributesError, setAttributesError] = useState<string | undefined>(
    undefined
  );

  useEffect(() => {
    if (collection) {
      setName(collection.name ? `${collection.name}` : "");
      setDescription(collection.description || "");
    }
  }, [collection]);

  const validateMetadata = useCallback(() => {
    let isValid = true;

    if (!isSameArtType && !name.trim()) {
      setNameError("Name is required");
      isValid = false;
    } else {
      setNameError(undefined);
    }

    if (!isSameArtType && !description.trim()) {
      setDescriptionError("Description is required");
      isValid = false;
    } else {
      setDescriptionError(undefined);
    }

    // Validate attributes
    const traitTypes = new Set();
    for (const attr of attributes) {
      if (!attr.trait_type.trim() || !attr.value.trim()) {
        setAttributesError(
          "All attributes must have both trait type and value"
        );
        isValid = false;
        break;
      }
      if (traitTypes.has(attr.trait_type)) {
        setAttributesError("Trait types must be unique");
        isValid = false;
        break;
      }
      traitTypes.add(attr.trait_type);
    }
    if (isValid) {
      setAttributesError(undefined);
    }

    return isValid;
  }, [name, description, attributes, isSameArtType]);

  return {
    name,
    setName,
    description,
    setDescription,
    attributes,
    setAttributes,
    nameError,
    descriptionError,
    attributesError,
    validateMetadata,
  };
}

// Hook xử lý file upload
function useFileUpload() {
  const [imageFile, setImageFile] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [batchMetadata, setBatchMetadata] = useState<NftMetadataInput[]>([]);

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      setIsUploading(true);
      try {
        if (
          ![
            "image/png",
            "image/jpeg",
            "image/jpg",
            "image/gif",
            "image/webp",
          ].includes(file.type)
        ) {
          toast.error("Only PNG, JPEG, JPG, GIF or WEBP files are allowed");
          return;
        }
        const fileSizeMB = file.size / 1024 / 1024;
        if (fileSizeMB > 10) {
          toast.error("File size must be less than 10MB");
          return;
        }
        const url = URL.createObjectURL(file);
        setImageFile(url);
        setPreviewUrl(url);
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error("Failed to upload file");
      } finally {
        setIsUploading(false);
      }
    },
    []
  );

  const handleBatchUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (!files) return;
      if (files.length > 10) {
        toast.error("Maximum 10 images allowed for batch upload");
        return;
      }

      setIsUploading(true);
      try {
        const metadata: NftMetadataInput[] = [];
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          if (
            ![
              "image/png",
              "image/jpeg",
              "image/jpg",
              "image/gif",
              "image/webp",
            ].includes(file.type)
          ) {
            toast.error(`File ${file.name} is not a valid image type`);
            continue;
          }
          const fileSizeMB = file.size / 1024 / 1024;
          if (fileSizeMB > 10) {
            toast.error(`File ${file.name} is too large (max 10MB)`);
            continue;
          }
          const url = URL.createObjectURL(file);
          metadata.push({
            name: file.name,
            description: "",
            attributes: [],
            amount: "1",
            image: url,
          });
        }
        setBatchMetadata(metadata);
      } catch (error) {
        console.error("Error uploading files:", error);
        toast.error("Failed to upload files");
      } finally {
        setIsUploading(false);
      }
    },
    []
  );

  return {
    imageFile,
    setImageFile,
    previewUrl,
    setPreviewUrl,
    isUploading,
    setIsUploading,
    batchMetadata,
    setBatchMetadata,
    handleFileUpload,
    handleBatchUpload,
  };
}

// Hook xử lý mint settings
function useMintSettings(collection: Collection | null) {
  const [amount, setAmount] = useState(1);
  const [royalty, setRoyalty] = useState(0);
  const [gasPrice, setGasPrice] = useState<string | undefined>(undefined);
  const [gasLimit, setGasLimit] = useState<string | undefined>(undefined);

  const validateSettings = useCallback(() => {
    const mintLimit = Number(collection?.mintLimit || 100);
    if (amount < 1 || amount > mintLimit) {
      toast.error(`Amount must be between 1 and ${mintLimit}`);
      return false;
    }
    if (royalty < 0 || royalty > 50) {
      toast.error("Royalty must be between 0 and 50%");
      return false;
    }
    return true;
  }, [amount, royalty, collection?.mintLimit]);

  return {
    amount,
    setAmount,
    royalty,
    setRoyalty,
    gasPrice,
    setGasPrice,
    gasLimit,
    setGasLimit,
    validateSettings,
  };
}

// Hook xử lý allowlist mint
function useAllowlistMint() {
  const [signature, setSignature] = useState<string | undefined>(undefined);
  const [nonce, setNonce] = useState<string | undefined>(undefined);

  const validateAllowlist = useCallback(() => {
    if (!signature || !/^0x[0-9a-fA-F]{130}$/.test(signature)) {
      toast.error("Invalid signature format (must be 65 bytes hex)");
      return false;
    }
    if (!nonce || !Number.isInteger(Number(nonce)) || Number(nonce) < 0) {
      toast.error("Nonce must be a non-negative integer");
      return false;
    }
    return true;
  }, [signature, nonce]);

  return {
    signature,
    setSignature,
    nonce,
    setNonce,
    validateAllowlist,
  };
}

export function useMintForm({
  collection,
  isSameArtType,
  isAllowlistMint,
}: UseMintFormProps): {
  formState: FormState;
  formActions: FormActions;
  formErrors: FormErrors;
} {
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const {
    name,
    setName,
    description,
    setDescription,
    attributes,
    setAttributes,
    nameError,
    descriptionError,
    attributesError,
    validateMetadata,
  } = useMetadata(collection, isSameArtType);

  const {
    imageFile,
    setImageFile,
    previewUrl,
    setPreviewUrl,
    isUploading,
    setIsUploading,
    batchMetadata,
    setBatchMetadata,
    handleFileUpload,
    handleBatchUpload,
  } = useFileUpload();

  const {
    amount,
    setAmount,
    royalty,
    setRoyalty,
    gasPrice,
    setGasPrice,
    gasLimit,
    setGasLimit,
    validateSettings,
  } = useMintSettings(collection);

  const { signature, setSignature, nonce, setNonce, validateAllowlist } =
    useAllowlistMint();

  const resetForm = useCallback(() => {
    setAgreedToTerms(false);
    setImageFile(null);
    setPreviewUrl(null);
    setIsUploading(false);
    setName("");
    setDescription("");
    setAttributes([]);
    setAmount(1);
    setRoyalty(0);
    setGasPrice(undefined);
    setGasLimit(undefined);
    setSignature(undefined);
    setNonce(undefined);
    setBatchMetadata([]);
  }, []);

  const validateForm = useCallback(() => {
    if (!agreedToTerms) {
      toast.error("You must agree to the terms and conditions");
      return false;
    }

    if (!isSameArtType && !imageFile && !batchMetadata.length) {
      toast.error("Please upload an image or use batch upload");
      return false;
    }

    if (!validateMetadata()) return false;
    if (!validateSettings()) return false;
    if (isAllowlistMint && !validateAllowlist()) return false;

    return true;
  }, [
    agreedToTerms,
    isSameArtType,
    imageFile,
    batchMetadata.length,
    validateMetadata,
    validateSettings,
    isAllowlistMint,
    validateAllowlist,
  ]);

  return {
    formState: {
      agreedToTerms,
      imageFile,
      previewUrl,
      isUploading,
      name,
      description,
      attributes,
      amount,
      royalty,
      gasPrice,
      gasLimit,
      signature,
      nonce,
      batchMetadata,
    },
    formActions: {
      resetForm,
      setAgreedToTerms,
      setImageFile,
      setName,
      setDescription,
      setAttributes,
      setAmount,
      setRoyalty,
      setGasPrice,
      setGasLimit,
      setSignature,
      setNonce,
      setBatchMetadata,
      handleFileUpload,
      handleBatchUpload,
      isUploading,
      setIsUploading,
      validateForm,
    },
    formErrors: {
      nameError,
      descriptionError,
      attributesError,
    },
  };
}
