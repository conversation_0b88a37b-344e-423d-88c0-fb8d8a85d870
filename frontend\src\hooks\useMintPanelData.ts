"use client";

import { useState, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { useAccount } from "wagmi";
import { toast } from "sonner";
import {
  useGetActiveStageQuery,
  useGetMintCostQuery,
  useGetNftsQuery,
  useNftMintedRealtimeSubscription,
  SortOrder,
  FilterOperator,
} from "@/lib/api/graphql/generated";
import type { RootState } from "@/store/store";

export function useMintPanelData() {
  const { address, isConnected, chain: chainObject } = useAccount();
  const collection = useSelector((state: RootState) => state.collection.data);

  const [currentAmount, setCurrentAmount] = useState(1); // Default amount
  const [isLoadingMintCost, setIsLoadingMintCost] = useState(false);
  const [lastMintCost, setLastMintCost] = useState({
    mintPrice: "0",
    estimatedGas: "0",
    totalPrice: "0",
  });

  const { data: activeStageData, error: activeStageError } = useGetActiveStageQuery({
    variables: {
      input: {
        chainId: collection?.chainId ?? "",
        contractAddress: collection?.contractAddress ?? "",
        wallet: address ?? "",
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress || !address,
  });

  const isAllowlistMint = activeStageData?.getActiveStage?.isPublicMint === false;

  const { data: mintCostData, error: mintCostError, loading: mintCostLoading } = useGetMintCostQuery({
    variables: {
      input: {
        chainId: collection?.chainId ?? "",
        contractAddress: collection?.contractAddress ?? "",
        amount: currentAmount.toString(),
        wallet: address ?? "",
        stageId: activeStageData?.getActiveStage?.stageId,
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress || isLoadingMintCost,
    onError: (error) => {
      toast.error("Failed to fetch mint cost", { description: error.message });
    },
  });

  useEffect(() => {
    if (mintCostData?.getMintCost.success) {
      setLastMintCost({
        mintPrice: mintCostData.getMintCost.mintPrice,
        estimatedGas: mintCostData.getMintCost.estimatedGas,
        totalPrice: mintCostData.getMintCost.totalPrice,
      });
    }
  }, [mintCostData]);

  useEffect(() => {
    if (mintCostError) {
      toast.error("Failed to fetch mint cost", { description: mintCostError.message });
    }
    if(activeStageError){
      toast.error("Failed to fetch active stage", { description: activeStageError.message });
    }
  }, [mintCostError, activeStageError]);

  const inputQuery = useMemo(() => ({
    filters: [
      { field: "collectionId", operator: FilterOperator.Eq, value: collection?.id || "" },
      { field: "creator", operator: FilterOperator.Eq, value: address?.toLowerCase() || "" },
    ],
    pagination: { limit: "20", skip: "0" },
    sort: { field: "createdAt", order: SortOrder.Desc },
  }), [collection?.id, address]);

  const { data: nftsData, refetch: refetchNfts } = useGetNftsQuery({
    skip: !collection || !isConnected,
    variables: { input: inputQuery },
  });

  useNftMintedRealtimeSubscription({
    variables: { input: { contractAddress: collection?.contractAddress ?? "", wallet: address ?? "" } },
    onData: ({ data }) => {
      toast.success("NFT minted successfully", { description: `Token IDs: ${data?.data?.nftMintedRealtime.tokenIds.join(", ")}` });
      refetchNfts();
    },
    onError: (error) => toast.error("Failed to fetch nft minted realtime", { description: error.message }),
    skip: !collection?.contractAddress || !isConnected || !address,
  });

  return {
    collection, address, isConnected, chainObject,
    activeStageData, isAllowlistMint,
    nftsData, refetchNfts,
    mintCostData, lastMintCost, isLoadingMintCost: mintCostLoading || isLoadingMintCost,
    currentAmount, setCurrentAmount, // Expose setCurrentAmount for form hook to update
  };
} 