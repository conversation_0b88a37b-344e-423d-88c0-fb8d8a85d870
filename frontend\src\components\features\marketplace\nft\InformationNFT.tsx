"use client";

import { Star, ChevronDown, Share2, Globe, X, ChevronUp } from "lucide-react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "sonner";
import { RootState } from "@/store/store";
import { useSelector } from "react-redux";

export default function InformationNFT() {
  const [showInfo, setShowInfo] = useState(false);
  const { collection } = useSelector((state: RootState) => state.marketplace);
  return (
    <header className="border-b">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <Image
            src={collection?.image || ""}
            alt={collection?.name || ""}
            width={40}
            height={40}
            className="w-full h-full object-cover"
          />
          <div>
            <h1 className="text-lg font-semibold flex items-center gap-2">
              {collection?.name}
              <Star className="h-4 w-4" />
            </h1>
            <div className="flex items-center gap-2 text-sm">
              <Button
                variant="outline"
                size="sm"
                className="h-7 gap-1"
                onClick={() => setShowInfo(!showInfo)}
              >
                Info
                {showInfo ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-7 gap-1"
                onClick={() => {
                  const url = window.location.href;
                  navigator.clipboard.writeText(url);
                  toast.success("Link copied to clipboard!");
                }}
              >
                Share Stats <Share2 className="h-3 w-3" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Globe className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Image
                  src="https://images.unsplash.com/photo-1742435456486-3a0059c05e38?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D?20x20"
                  alt="Discord"
                  width={16}
                  height={16}
                />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      {showInfo && (
        <div className="transition-all duration-300 w-full bg-background/30 rounded-xs p-4 h-32">
          {collection?.description}
        </div>
      )}
      <div className="grid grid-cols-2 md:grid-cols-7 px-4 py-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-1">
          <span>Floor Price</span>
          <div className="font-medium text-foreground">0.0276 BTC</div>
        </div>
        <div className="flex items-center gap-1">
          <span>All Vol</span>
          <div className="font-medium text-foreground">98.4207 BTC</div>
        </div>
        <div className="flex items-center gap-1">
          <span>Owners</span>
          <div className="font-medium text-foreground">2.7K</div>
        </div>
        <div className="flex items-center gap-1">
          <span>Listed</span>
          <div className="font-medium text-foreground">425</div>
        </div>
        <div className="flex items-center gap-1">
          <span>Total Supply</span>
          <div className="font-medium text-foreground">10K</div>
        </div>
        <div className="flex items-center gap-1">
          <span>Range</span>
          <div className="font-medium text-foreground">-265015 to -234984</div>
        </div>
        <div className="flex items-center gap-1">
          <span>Pending Transactions</span>
          <div className="font-medium text-foreground">0</div>
        </div>
      </div>
    </header>
  );
}
