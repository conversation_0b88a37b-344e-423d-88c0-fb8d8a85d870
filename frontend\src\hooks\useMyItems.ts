import { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/store";
import {
  setNFTs,
  addNFT,
  removeNFT,
  updateNFT,
  setLoading,
  setError,
} from "@/store/slices/myItemsSlice";
import {
  SortOrder,
  FilterOperator,
  useGetNftsQuery,
  useNftModifiedPrivateRealtimeSubscription,
  ActionType,
  Nft,
  NftModifiedRealtimeResponse,
} from "@/lib/api/graphql/generated";
import { toast } from "sonner";

export const useMyItems = ({
  contractAddress,
  address,
  isConnected,
}: {
  contractAddress: string;
  address: string;
  isConnected: boolean;
}) => {
  const dispatch = useDispatch();
  const { nfts, selectedNFTs, isLoading, error } = useSelector(
    (state: RootState) => state.myItemsNft
  );

  const inputQuery = {
    filters: [
      {
        field: "contractAddress",
        operator: FilterOperator.Eq,
        value: contractAddress || "",
      },
      {
        field: "creator",
        operator: FilterOperator.Eq,
        value: address?.toLowerCase() || "",
      },
    ],
    pagination: {
      limit: "200",
      skip: "0",
    },
    sort: {
      field: "createdAt",
      order: SortOrder.Desc,
    },
  };

  const {
    data: nftsData,
    loading: queryLoading,
    error: queryError,
  } = useGetNftsQuery({
    skip: !contractAddress || !address || !isConnected,
    variables: {
      input: inputQuery,
    },
  });

  useEffect(() => {
    try {
      if (nftsData?.getNfts?.nfts) {
        // Validate dữ liệu trước khi dispatch
        const validNFTs = nftsData.getNfts.nfts.filter(
          (nft): nft is Nft =>
            nft != null &&
            typeof nft === "object" &&
            typeof nft.id === "string" &&
            (nft.name == null || typeof nft.name === "string") &&
            (nft.image == null || typeof nft.image === "string") &&
            (nft.mintPrice == null || typeof nft.mintPrice === "string") &&
            (nft.owner == null || typeof nft.owner === "string")
        );
        dispatch(setNFTs(validNFTs));
      } else if (queryError) {
        dispatch(setError(queryError.message));
        toast.error("Failed to load NFTs");
      }
    } catch (error) {
      dispatch(setError(error as string));
      toast.error("An error occurred while loading NFTs");
    }
  }, [nftsData, queryError, dispatch]);

  useEffect(() => {
    dispatch(setLoading(queryLoading));
  }, [queryLoading, dispatch]);

  const handleRealtimeUpdate = useCallback(
    (newData: NftModifiedRealtimeResponse) => {
      try {
        if (!newData || !newData.data) return;

        switch (newData.action) {
          case ActionType.Create:
            newData.data.forEach((nft: Nft) => {
              console.log("realtime update", nft);
              if (
                nft != null &&
                typeof nft === "object" &&
                typeof nft.id === "string" &&
                (nft.name == null || typeof nft.name === "string") &&
                (nft.image == null || typeof nft.image === "string") &&
                (nft.mintPrice == null || typeof nft.mintPrice === "string") &&
                (nft.owner == null || typeof nft.owner === "string")
              ) {
                dispatch(addNFT(nft));
                toast.success("NFT created");
              }
            });
            break;
          case ActionType.Update:
            newData.data.forEach((nft: Nft) => {
              if (
                nft != null &&
                typeof nft === "object" &&
                typeof nft.id === "string" &&
                (nft.name == null || typeof nft.name === "string") &&
                (nft.image == null || typeof nft.image === "string") &&
                (nft.mintPrice == null || typeof nft.mintPrice === "string") &&
                (nft.owner == null || typeof nft.owner === "string")
              ) {
                dispatch(updateNFT(nft));
                toast.success("NFT updated");
              }
            });
            break;
          case ActionType.Delete:
            newData.data.forEach((nft: Nft) => {
              if (nft?.id) {
                dispatch(removeNFT(nft.id));
                toast.success("NFT deleted");
              }
            });
            break;
          default:
            throw new Error("Invalid action type");
        }
      } catch (error) {
        dispatch(setError(error as string));
        toast.error("An error occurred during realtime update");
      }
    },
    [dispatch]
  );

  useNftModifiedPrivateRealtimeSubscription({
    variables: {
      input: {
        contractAddress: contractAddress ?? "",
        wallet: address ?? "",
      },
    },
    skip: !contractAddress || !address || !isConnected,
    onData: ({ data }) => {
      const newData = data.data?.nftModifiedPrivateRealtime;
      if (newData) {
        handleRealtimeUpdate(newData);
      }
    },
  });

  return {
    nfts,
    selectedNFTs,
    isLoading,
    error,
  };
};
