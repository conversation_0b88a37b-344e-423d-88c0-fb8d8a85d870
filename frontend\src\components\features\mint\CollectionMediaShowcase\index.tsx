"use client";

import { useState } from "react";
import CollectionGallery from "./CollectionGallery";
import CollectionImageCarousel from "./CollectionImageCarousel";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";

interface CollectionMediaShowcaseProps {
  onImageChange?: (imageUrl: string) => void;
}

export function CollectionMediaShowcase({
  onImageChange,
}: CollectionMediaShowcaseProps) {
  const [showCarousel, setShowCarousel] = useState(false);
  const [carouselIndex, setCarouselIndex] = useState(0);
  const collection = useSelector((state: RootState) => state.collection.data);

  const handleOpenCarousel = (index: number) => {
    setCarouselIndex(index);
    setShowCarousel(true);
  };
  const handleCloseCarousel = () => {
    console.log("Closing carousel, showCarousel set to false");
    setShowCarousel(false);
  };
  if (!collection) return null;
  return (
    <>
      <CollectionGallery
        onOpenCarousel={handleOpenCarousel}
        onImageChange={onImageChange}
      />
      {showCarousel && (
        <CollectionImageCarousel
          initialIndex={carouselIndex}
          onClose={handleCloseCarousel}
        />
      )}
    </>
  );
}
