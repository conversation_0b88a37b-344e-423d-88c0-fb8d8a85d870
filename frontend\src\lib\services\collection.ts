import client from "@/lib/api/apolloClient";
import {
  Collection,
  FilterOperator,
  GetCollectionDocument,
} from "@/lib/api/graphql/generated";
import { ApolloError } from "@apollo/client";

export async function fetchCollection(
  chainId: string,
  contractAddress: string
): Promise<Collection> {
  try {
    const { data } = await client.query({
      query: GetCollectionDocument,
      variables: {
        input: {
          filters: [
            {
              field: "chainId",
              operator: FilterOperator.Eq,
              value: chainId,
            },
            {
              field: "contractAddress",
              operator: FilterOperator.Eq,
              value: contractAddress,
            },
          ],
        },
      },
      fetchPolicy: "no-cache",
    });

    if (!data?.getCollection) {
      throw new Error("Collection not found");
    }

    return data.getCollection;
  } catch (error) {
    throw new ApolloError({
      errorMessage:
        error instanceof Error ? error.message : "Failed to fetch collection",
    });
  }
}
