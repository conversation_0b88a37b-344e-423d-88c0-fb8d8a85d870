import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "i.imgur.com",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
      },
      {
        protocol: "https",
        hostname: "azure-voluntary-cheetah-817.mypinata.cloud",
        port: "",
        pathname: "/ipfs/**",
      },
    ],
    dangerouslyAllowSVG: true,
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ["@radix-ui/react-*", "lucide-react"],
  },
  poweredByHeader: false,
};

export default nextConfig;
