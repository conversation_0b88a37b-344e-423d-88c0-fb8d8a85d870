import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { Collection } from "@/lib/api/graphql/generated";
interface ExploreButtonProps {
  collection: Collection;
}

export default function ExploreButton({ collection }: ExploreButtonProps) {
  return (
    <div className="w-full bg-gray-50 dark:bg-[#0c0916] p-4 rounded-xs">
      <Link
        href={`/marketplace/${collection.chain}/${collection.contractAddress}`}
        className="flex items-center justify-center gap-2 p-3 w-full rounded-xs bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300"
      >
        <p>Explore Collection</p>
        <ExternalLink className="w-4 h-4" />
      </Link>
    </div>
  );
}
