/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { RotateCcw } from "lucide-react";
import { CollectionDetails } from "./CollectionDetails";
import { NFTArtSection } from "./NFTArtSection";
import { MintDetails } from "./MintDetails";
import { PublishCollectionModal } from "./PublishCollectionModal"; // Thêm import
import { useCreateCollection } from "@/hooks/useCreateCollection";
import type {
  AllowlistStageInput,
  ArtType,
  PublicMintInput,
} from "@/lib/api/graphql/generated";

export default function CreateCollection() {
  const {
    form,
    isLoading,
    allowlistStages,
    publicMint,
    isFormValid,
    setAllowlistStages,
    setSelectedChain,
    setPublicMint,
    setSelectedArtType,
    setCollectionImageFile,
    setArtworkFile,
    setMetadataUrl,
    getButtonText,
    handleClearForm,
    onSubmit,
    // Thêm các giá trị cho modal
    isModalOpen,
    step1Status,
    step2Status,
    setIsModalOpen,
  } = useCreateCollection();

  const callbacks = {
    onChainChange: setSelectedChain,
    onImageChange: setCollectionImageFile,
    onArtTypeChange: (type: ArtType) => {
      setSelectedArtType(type);
      form.setValue("artType", type);
    },
    onArtworkChange: setArtworkFile,
    onMetadataUrlChange: (url: string) => {
      setMetadataUrl(url);
      form.setValue("uri", url);
    },
    setAllowlistStages: (stages: AllowlistStageInput[]) => {
      setAllowlistStages(stages);
      form.setValue("allowlistStages", stages);
    },
    setPublicMint: (mint: PublicMintInput) => {
      setPublicMint(mint);
      form.setValue("publicMint", mint);
    },
    onMintPriceChange: (price: string) => form.setValue("mintPrice", price),
    onRoyaltyFeeChange: (fee: string) => form.setValue("royaltyFee", fee),
    onMaxSupplyChange: (supply: string) => form.setValue("maxSupply", supply),
    onMintLimitChange: (limit: string) => form.setValue("mintLimit", limit),
    onMintStartDateChange: (date: Date) =>
      form.setValue("mintStartDate", date.toISOString()),
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex justify-end mb-2">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleClearForm}
          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
        >
          <RotateCcw className="h-4 w-4 mr-1" /> Clear Form
        </Button>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <CollectionDetails form={form} isLoading={isLoading} {...callbacks} />
          <NFTArtSection isLoading={isLoading} {...callbacks} />
          <MintDetails
            isLoading={isLoading}
            allowlistStages={allowlistStages}
            publicMint={publicMint}
            {...callbacks}
          />
          <Button
            type="submit"
            className="w-full bg-pink-600 hover:bg-pink-700 text-white"
            disabled={isLoading || !isFormValid}
          >
            {getButtonText()}
          </Button>
        </form>
      </Form>
      <PublishCollectionModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        step1Status={step1Status}
        step2Status={step2Status}
      />
    </div>
  );
}
