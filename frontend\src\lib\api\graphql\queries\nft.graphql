query GetNfts($input: GetNftsInput!) {
  getNfts(input: $input) {
    nfts {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    pagination {
      total
      limit
      skip
      cursor
      hasMore
    }
  }
}

mutation MintNft($input: MintNftInput!) {
  mintNft(input: $input) {
    success
    steps {
      id
      params
    }
    stageId
    stageType
    mintPrice
    estimatedGas
    maxSupply
    totalMinted
    errorMessage
  }
}

mutation SubmitMintTransaction($input: SubmitMintTransactionInput!) {
  submitMintTransaction(input: $input) {
    success
    collectionId
    tokenIds
    amounts
    txHash
    errorMessage
  }
}

query GetActiveStage($input: GetActiveStageInput!) {
  getActiveStage(input: $input) {
    stageId
    isPublicMint
    mintPrice
  }
}

subscription MintProgressRealtime($input: MintProgressInput!) {
  mintProgressRealtime(input: $input) {
    totalMinted
    maxSupply
  }
}

query MintProgress($input: MintProgressInput!) {
  mintProgress(input: $input) {
    totalMinted
    maxSupply
  }
}

subscription NftMintedRealtime($input: NftMintedEventInput!) {
  nftMintedRealtime(input: $input) {
    contractAddress
    tokenIds
    amounts
    tokenUris
    recipient
  }
}

query GetMintCost($input: GetMintCostInput!) {
  getMintCost(input: $input) {
    success
    errorMessage
    mintPrice
    estimatedGas
    totalPrice
    isPublicMint
    stageId
    stageInfo {
      stageId
      mintLimit
      mintedInStage
    }
  }
}

subscription NftModifiedPublicRealtime($input: NftModifiedPublicInput!) {
  nftModifiedPublicRealtime(input: $input) {
    data {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    action
  }
}

subscription NftModifiedPrivateRealtime($input: NftModifiedPrivateInput!) {
  nftModifiedPrivateRealtime(input: $input) {
    data {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    action
  }
}
