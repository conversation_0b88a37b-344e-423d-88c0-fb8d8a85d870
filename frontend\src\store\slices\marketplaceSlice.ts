import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { Collection } from "@/lib/api/graphql/generated";

interface MarketplaceState {
  collection: Collection | null;
  loading: boolean;
  error: string | null;
}

const initialState: MarketplaceState = {
  collection: null,
  loading: false,
  error: null,
};

const marketplaceSlice = createSlice({
  name: "marketplace",
  initialState,
  reducers: {
    setCollection: (state, action: PayloadAction<Collection>) => {
      state.collection = action.payload;
      state.loading = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearCollection: (state) => {
      state.collection = null;
      state.loading = false;
      state.error = null;
    },
  },
});

export const { setCollection, setLoading, setError, clearCollection } =
  marketplaceSlice.actions;
export default marketplaceSlice.reducer;
