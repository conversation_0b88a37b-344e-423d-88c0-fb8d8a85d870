import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Sparkles } from "lucide-react";
import Link from "next/link";

export const ButtonsBanner = ({ chain = null }: { chain: string | null }) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <Link href={`/collections${chain ? `/${chain}` : ""}`} className="w-fit">
        <Button
          size="lg"
          className="cursor-pointer bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white font-medium"
        >
          Explore Collections
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </Link>
      <Link href="/auctions" className="w-fit">
        <Button
          size="lg"
          variant="outline"
          className="cursor-pointer border-gray-300  hover:bg-gray-100 dark:border-white/10 text-white dark:hover:bg-white/5"
        >
          <Sparkles className="mr-2 h-4 w-4" />
          Live Auctions
        </Button>
      </Link>
    </div>
  );
};
