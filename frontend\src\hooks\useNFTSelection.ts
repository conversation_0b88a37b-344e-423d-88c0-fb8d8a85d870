import { Nft } from "@/lib/api/graphql/generated";
import { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { throttle } from "lodash";

interface UseNFTSelectionProps {
  initialNFTs: Nft[];
  onVisibleNFTsChange: (nfts: Nft[]) => void;
  onSelectedNFTsChange?: (ids: Set<string>) => void;
}

export function useNFTSelection({
  initialNFTs,
  onVisibleNFTsChange,
  onSelectedNFTsChange,
}: UseNFTSelectionProps) {
  const [itemCount, setItemCount] = useState("0");
  const [sliderValue, setSliderValue] = useState([0]);
  const [isSliding, setIsSliding] = useState(false);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  // Use refs để tránh re-render không cần thiết
  const selectedIdsRef = useRef(selectedIds);
  selectedIdsRef.current = selectedIds;
  const hasInitialized = useRef(false);

  const nftsLength = useMemo(() => initialNFTs.length, [initialNFTs]);

  // Cập nhật state và callback đồng bộ
  const updateStates = useCallback(
    (newSelectedIds: Set<string>) => {
      const newCount = newSelectedIds.size;
      const newSliderValue =
        nftsLength > 0 ? Math.round((newCount / nftsLength) * 100) : 0;

      console.log("updateStates:", {
        newCount,
        newSliderValue,
        selectedIds: Array.from(newSelectedIds),
      });

      // Batch state updates
      setSelectedIds(newSelectedIds);
      setItemCount(newCount.toString());
      setSliderValue([newSliderValue]);

      // Cập nhật visible NFTs - hiển thị tất cả NFT và đánh dấu các NFT đã chọn
      const visibleNFTs = initialNFTs.map((nft) => ({
        ...nft,
        selected: newSelectedIds.has(nft.id),
      }));

      onSelectedNFTsChange?.(newSelectedIds);
      onVisibleNFTsChange(visibleNFTs);
    },
    [initialNFTs, nftsLength, onSelectedNFTsChange, onVisibleNFTsChange]
  );

  // Khởi tạo visibleNFTs ban đầu chỉ một lần
  useEffect(() => {
    if (!hasInitialized.current && initialNFTs.length > 0) {
      hasInitialized.current = true;
      const initialVisibleNFTs = initialNFTs.map((nft) => ({
        ...nft,
        selected: false,
      }));
      console.log("Initializing visibleNFTs:", initialVisibleNFTs.length);
      onVisibleNFTsChange(initialVisibleNFTs);
    }
  }, [initialNFTs, onVisibleNFTsChange]);

  // Handle input changes
  const handleItemCountChange = useCallback(
    (value: string) => {
      const count = Number.parseInt(value) || 0;
      const normalizedCount = Math.min(Math.max(count, 0), nftsLength);

      const newSelectedIds = new Set<string>();
      for (let i = 0; i < normalizedCount && i < initialNFTs.length; i++) {
        newSelectedIds.add(initialNFTs[i].id);
      }

      console.log("handleItemCountChange:", {
        normalizedCount,
        selectedIds: Array.from(newSelectedIds),
      });
      updateStates(newSelectedIds);
    },
    [initialNFTs, nftsLength, updateStates]
  );

  // Handle slider changes với throttle
  const handleSliderChange = useMemo(
    () =>
      throttle((value: number[]) => {
        const sliderValue = value[0];
        const percentage = sliderValue / 100;
        const itemsToShow = Math.max(0, Math.round(percentage * nftsLength));

        const newSelectedIds = new Set<string>();
        for (let i = 0; i < itemsToShow && i < initialNFTs.length; i++) {
          newSelectedIds.add(initialNFTs[i].id);
        }

        console.log("handleSliderChange:", {
          sliderValue,
          percentage,
          itemsToShow,
          selectedIds: Array.from(newSelectedIds),
        });
        updateStates(newSelectedIds);
      }, 30), // Giảm throttle time để phản hồi nhanh hơn
    [initialNFTs, nftsLength, updateStates]
  );

  // Handle chọn tay (individual selection)
  const handleIndividualSelection = useCallback(
    (id: string, isSelected: boolean) => {
      const newSelectedIds = new Set(selectedIdsRef.current);
      if (isSelected) {
        newSelectedIds.add(id);
      } else {
        newSelectedIds.delete(id);
      }

      console.log("handleIndividualSelection:", {
        id,
        isSelected,
        selectedIds: Array.from(newSelectedIds),
      });
      updateStates(newSelectedIds);
    },
    [updateStates]
  );

  const handleSliderDragStart = useCallback(() => {
    setIsSliding(true);
  }, []);

  const handleSliderDragEnd = useCallback(() => {
    setIsSliding(false);
    handleSliderChange.flush();
  }, [handleSliderChange]);

  // Hủy throttle khi component unmount
  useEffect(() => {
    return () => {
      handleSliderChange.cancel();
    };
  }, [handleSliderChange]);

  return {
    itemCount,
    sliderValue,
    isSliding,
    handleItemCountChange,
    handleSliderChange,
    handleSliderDragStart,
    handleSliderDragEnd,
    handleIndividualSelection,
  };
}
