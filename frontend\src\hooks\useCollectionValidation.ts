"use client";

import { toast } from "sonner";
import { AllowlistStageInput } from "@/lib/api/graphql/generated";
import { FormData, formSchema } from "@/types/collection.type";

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

interface ValidationActions {
  validateForm: (values: FormData) => ValidationResult;
  validateAllowlistStages: (stages: AllowlistStageInput[]) => string | null;
  validateFiles: (
    collectionImageFile: File | null,
    artworkFile: File | null,
    metadataUrl: string,
    selectedArtType: string
  ) => boolean;
  validateChainConnection: (
    isConnected: boolean,
    currentChain: string | undefined,
    selectedChain: string
  ) => boolean;
}

export function useCollectionValidation(): ValidationActions {
  const validateForm = (values: FormData): ValidationResult => {
    const result = formSchema.safeParse(values);
    
    if (!result.success) {
      const errors = result.error.issues.map(
        (issue) => `${issue.path.join(".")}: ${issue.message}`
      );
      
      // Show toast for first error
      if (errors.length > 0) {
        toast.error("Validation error", {
          description: errors[0],
        });
      }
      
      return {
        isValid: false,
        errors,
      };
    }
    
    return {
      isValid: true,
      errors: [],
    };
  };

  const validateAllowlistStages = (stages: AllowlistStageInput[]): string | null => {
    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];
      const startTime = new Date(stage.startDate).getTime() / 1000;
      const duration =
        parseInt(stage.durationDays || "0") * 86400 +
        parseInt(stage.durationHours || "0") * 3600;

      // Check if stage has valid duration
      if (duration <= 0) {
        return `Stage ${i + 1} must have a duration greater than 0`;
      }

      // Check for overlaps with other stages
      for (let j = i + 1; j < stages.length; j++) {
        const other = stages[j];
        const otherStartTime = new Date(other.startDate).getTime() / 1000;
        const otherDuration =
          parseInt(other.durationDays || "0") * 86400 +
          parseInt(other.durationHours || "0") * 3600;

        if (
          startTime < otherStartTime + otherDuration &&
          startTime + duration > otherStartTime
        ) {
          return `Time overlap between stage ${i + 1} and stage ${j + 1}`;
        }
      }

      // Validate wallet addresses
      if (!stage.wallets || stage.wallets.length === 0) {
        return `Stage ${i + 1} must have at least one wallet address`;
      }

      for (const wallet of stage.wallets) {
        if (!/^0x[a-fA-F0-9]{40}$/.test(wallet)) {
          return `Invalid wallet address in stage ${i + 1}: ${wallet}`;
        }
      }

      // Validate mint price
      const mintPrice = parseFloat(stage.mintPrice);
      if (isNaN(mintPrice) || mintPrice < 0) {
        return `Invalid mint price in stage ${i + 1}: ${stage.mintPrice}`;
      }
    }

    return null;
  };

  const validateFiles = (
    collectionImageFile: File | null,
    artworkFile: File | null,
    metadataUrl: string,
    selectedArtType: string
  ): boolean => {
    if (!collectionImageFile) {
      toast.error("Collection image is required");
      return false;
    }

    // Validate collection image
    const validImageTypes = ["image/jpeg", "image/png", "image/jpg", "image/gif", "image/webp"];
    if (!validImageTypes.includes(collectionImageFile.type)) {
      toast.error("Invalid collection image format. Only JPG, PNG, GIF, and WebP are allowed.");
      return false;
    }

    const maxImageSize = 10 * 1024 * 1024; // 10MB
    if (collectionImageFile.size > maxImageSize) {
      toast.error("Collection image size exceeds 10MB limit.");
      return false;
    }

    // Validate based on art type
    if (selectedArtType === "SAME") {
      if (!artworkFile) {
        toast.error("Artwork file is required for SAME art type");
        return false;
      }

      if (!validImageTypes.includes(artworkFile.type)) {
        toast.error("Invalid artwork format. Only JPG, PNG, GIF, and WebP are allowed.");
        return false;
      }

      if (artworkFile.size > maxImageSize) {
        toast.error("Artwork size exceeds 10MB limit.");
        return false;
      }
    } else if (selectedArtType === "UNIQUE") {
      if (!metadataUrl.trim()) {
        toast.error("Metadata URL is required for UNIQUE art type");
        return false;
      }

      // Basic URL validation
      try {
        new URL(metadataUrl);
      } catch {
        toast.error("Invalid metadata URL format");
        return false;
      }

      // Check if it's IPFS or HTTPS
      if (!metadataUrl.startsWith("ipfs://") && !metadataUrl.startsWith("https://")) {
        toast.error("Metadata URL must start with 'ipfs://' or 'https://'");
        return false;
      }
    }

    return true;
  };

  const validateChainConnection = (
    isConnected: boolean,
    currentChain: string | undefined,
    selectedChain: string
  ): boolean => {
    if (!isConnected) {
      toast.error("Please connect your wallet");
      return false;
    }

    if (currentChain !== selectedChain) {
      toast.warning(`Please switch to ${selectedChain} network`);
      return false;
    }

    return true;
  };

  return {
    validateForm,
    validateAllowlistStages,
    validateFiles,
    validateChainConnection,
  };
}
