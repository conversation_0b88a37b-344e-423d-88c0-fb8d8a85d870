"use client";

import { useSelector } from "react-redux";
import type { RootState } from "@/store/store";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  MintProgressDocument,
  useMintProgressQuery,
  useMintProgressRealtimeSubscription,
} from "@/lib/api/graphql/generated";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

const POLL_INTERVAL = 30000; // Fallback polling every 30s if subscription fails
const MAX_RECONNECT_ATTEMPTS = 5; // Max retry attempts for

export default function MintProgress() {
  const collection = useSelector((state: RootState) => state.collection.data);
  const [subscriptionFailed, setSubscriptionFailed] = useState(false); // Track subscription status
  const [reconnectAttempts, setReconnectAttempts] = useState(0); //

  const hasValidInput =
    !!collection?.id && !!collection?.contractAddress && !!collection?.chainId;
  const input = {
    collectionId: collection?.id ?? "",
    contractAddress: collection?.contractAddress ?? "",
    chainId: collection?.chainId ?? "",
  };

  const {
    data: dataMintProgress,
    loading: loadingMintProgress,
    error: errorMintProgress,
    stopPolling,
    startPolling,
    refetch,
  } = useMintProgressQuery({
    variables: {
      input,
    },
    skip: !hasValidInput,
    fetchPolicy: "network-only",
  });

  useMintProgressRealtimeSubscription({
    variables: {
      input,
    },
    skip: !hasValidInput,
    onError: (error) => {
      handleSubscriptionError(error);
    },
    onData: ({ data, client }) => {
      const newData = data?.data?.mintProgressRealtime;
      console.log("newData", newData);
      if (newData) {
        refetch();

        client.writeQuery({
          query: MintProgressDocument,
          variables: {
            input,
          },
          data: {
            mintProgress: newData,
          },
        });
        setSubscriptionFailed(false);
        setReconnectAttempts(0);
        stopPolling();
      }
    },
  });
  // Handle subscription errors with retry logic
  const handleSubscriptionError = (error: Error) => {
    console.error("Subscription error:", error);
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      const delay = Math.pow(2, reconnectAttempts) * 1000; // Exponential backoff
      setTimeout(() => {
        setReconnectAttempts((prev) => prev + 1);
      }, delay);
      toast.warning(
        `Reconnecting to real-time updates... (Attempt ${
          reconnectAttempts + 1
        })`
      );
    } else {
      setSubscriptionFailed(true);
      startPolling(POLL_INTERVAL); // Fallback to polling
      toast.error(
        "Real-time updates unavailable. Switching to periodic updates."
      );
    }
  };

  // Handle query errors
  useEffect(() => {
    if (errorMintProgress) {
      toast.error("Failed to fetch mint progress", {
        description: errorMintProgress.message,
      });
    }
  }, [errorMintProgress]);

  // Clean up polling on unmount
  useEffect(() => {
    return () => stopPolling();
  }, [stopPolling]);

  // Calculate progress
  const totalMinted = useMemo(
    () => Number(dataMintProgress?.mintProgress?.totalMinted ?? 0),
    [dataMintProgress]
  );
  const maxSupply = useMemo(
    () => Number(dataMintProgress?.mintProgress?.maxSupply ?? 0),
    [dataMintProgress]
  );
  const percentage = useMemo(
    () => (maxSupply > 0 ? Math.min((totalMinted / maxSupply) * 100, 100) : 0),
    [totalMinted, maxSupply]
  );

  // Loading state
  if (loadingMintProgress || !hasValidInput) {
    return (
      <div className="w-full" aria-live="polite">
        <div className="flex justify-between mb-2">
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            Minted
          </span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            <Loader2 className="inline h-4 w-4 animate-spin" />
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-800/50 rounded-full h-2.5 animate-pulse" />
      </div>
    );
  }

  // Error or invalid data state
  if (maxSupply === 0 || errorMintProgress) {
    return (
      <div className="text-center text-sm text-gray-600 dark:text-gray-400">
        Unable to load mint progress.
      </div>
    );
  }

  // Normal state
  return (
    <div className="w-full" aria-live="polite">
      <div className="flex justify-between mb-2">
        <span className="text-sm font-semibold text-gray-900 dark:text-white">
          Minted
        </span>
        <span className="text-sm font-semibold text-gray-900 dark:text-white">
          {totalMinted}/{maxSupply}
        </span>
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-800/50 rounded-full h-2.5">
        <div
          className={cn(
            "bg-gradient-to-r from-pink-500 to-purple-500 dark:from-pink-600 dark:to-purple-600 h-2.5 rounded-full transition-all duration-300",
            percentage === 100 && "bg-green-500 dark:bg-green-600"
          )}
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={percentage}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-label={`Mint progress: ${totalMinted} of ${maxSupply} (${percentage.toFixed(
            2
          )}%)`}
        />
      </div>
    </div>
  );
}
