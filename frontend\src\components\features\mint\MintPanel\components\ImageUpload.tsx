"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Upload, ImageIcon, X, Loader2 } from "lucide-react";
import Image from "next/image";
import { NftMetadataInput } from "@/types/mint.types";
import { Collection } from "@/lib/api/graphql/generated";

interface ImageUploadProps {
  isSameArtType: boolean;
  collection?: Collection | null;
  imageFile?: string | null;
  setImageFile: (file: string | null) => void;
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  handleBatchUpload: (event: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploading: boolean;
  batchMetadata: NftMetadataInput[];
  setBatchMetadata: (metadata: NftMetadataInput[]) => void;
}

export function ImageUpload({
  isSameArtType,
  collection,
  imageFile,
  setImageFile,
  handleFileUpload,
  handleBatchUpload,
  isUploading,
  batchMetadata,
  setBatchMetadata,
}: ImageUploadProps) {
  const handleBatchMetadataChange = (
    index: number,
    field: keyof NftMetadataInput,
    value: string
  ) => {
    setBatchMetadata(
      batchMetadata.map((meta, i) => (i === index ? { ...meta, [field]: value } : meta))
    );
  };

  if (isSameArtType) {
    return (
      <p className="text-sm text-gray-300">
        Using collection image:{" "}
        <span className="font-medium">{collection?.image}</span>. Customize
        name, description, or attributes if needed (defaults to collection
        metadata).
      </p>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <Label className="text-sm text-white flex items-center gap-1">
          Image <span className="text-pink-500">*</span>
        </Label>
        <span className="text-sm text-gray-400">
          Required for Unique Art
        </span>
      </div>

      <Tabs defaultValue="single" className="w-full">
        <TabsList className="grid grid-cols-2 w-full bg-gray-50 border border-gray-200 dark:bg-[#0f0a19] dark:border-gray-800/50">
          <TabsTrigger
            value="single"
            className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-sm"
          >
            Single Upload
          </TabsTrigger>
          <TabsTrigger
            value="batch"
            className="data-[state=active]:bg-pink-500 data-[state=active]:text-white text-sm"
          >
            Batch Upload
          </TabsTrigger>
        </TabsList>

        <TabsContent value="single">
          <div className="relative mt-2">
            <Input
              id="image-upload"
              type="file"
              accept="image/png,image/jpeg,image/jpg,image/gif,image/webp"
              onChange={handleFileUpload}
              className="hidden"
              aria-label="Upload single image"
            />
            <Button
              type="button"
              variant="outline"
              className="w-full h-10 border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-800/50 text-sm"
              onClick={() =>
                document.getElementById("image-upload")?.click()
              }
              disabled={isUploading}
            >
              <ImageIcon className="mr-2 h-4 w-4" />
              Choose Image
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="batch">
          <div className="relative mt-2">
            <Input
              id="batch-upload"
              type="file"
              multiple
              accept="image/png,image/jpeg,image/jpg,image/gif,image/webp"
              onChange={handleBatchUpload}
              className="hidden"
              aria-label="Upload multiple images"
            />
            <Button
              type="button"
              variant="outline"
              className="w-full h-10 border-gray-800/50 text-gray-300 hover:bg-gray-800/50 text-sm"
              onClick={() =>
                document.getElementById("batch-upload")?.click()
              }
              disabled={isUploading}
            >
              <Upload className="mr-2 h-4 w-4" />
              Choose Images (Max 10)
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {isUploading && (
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-pink-500" />
          <span className="text-sm text-gray-300">Uploading...</span>
        </div>
      )}

      {imageFile && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Uploaded Image</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setImageFile(null)}
              className="text-sm text-gray-400 hover:text-red-400"
            >
              Clear
            </Button>
          </div>
          <div className="relative h-32 w-full rounded-md overflow-hidden border border-gray-800/50">
            <Image
              src={imageFile || "/placeholder.svg"}
              alt="NFT preview"
              fill
              className="object-cover"
            />
          </div>
        </div>
      )}

      {batchMetadata.length > 0 && (
        <div className="p-3 bg-purple-600/20 border border-purple-600/50 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm text-purple-300 font-medium">
              {batchMetadata.length} images uploaded for batch mint
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setBatchMetadata([])}
              className="text-sm text-gray-400 hover:text-red-400"
            >
              Clear All
            </Button>
          </div>
          <div className="space-y-4">
            {batchMetadata.map((meta, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 bg-[#0f0a19] rounded-md border border-gray-800/50"
              >
                <div className="relative h-16 w-16 rounded overflow-hidden border border-purple-600/50">
                  <Image
                    src={meta.image || "/placeholder.svg"}
                    alt={meta.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <div>
                    <Label
                      htmlFor={`batch-name-${index}`}
                      className="text-sm text-white"
                    >
                      Name
                    </Label>
                    <Input
                      id={`batch-name-${index}`}
                      value={meta.name}
                      onChange={(e) =>
                        handleBatchMetadataChange(
                          index,
                          "name",
                          e.target.value.slice(0, 100)
                        )
                      }
                      placeholder="NFT Name"
                      className="h-9 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor={`batch-amount-${index}`}
                      className="text-sm text-white"
                    >
                      Amount (1-{collection?.mintLimit || 100})
                    </Label>
                    <Input
                      id={`batch-amount-${index}`}
                      type="number"
                      value={meta.amount}
                      onChange={(e) =>
                        handleBatchMetadataChange(
                          index,
                          "amount",
                          Math.max(
                            1,
                            Math.min(
                              Number(collection?.mintLimit || 100),
                              Number(e.target.value)
                            )
                          ).toString()
                        )
                      }
                      min={1}
                      max={collection?.mintLimit || 100}
                      className="h-9 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
                    />
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    setBatchMetadata(
                      batchMetadata.filter((_, i) => i !== index)
                    )
                  }
                  className="h-9 w-9 text-gray-400 hover:text-red-400 hover:bg-red-900/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
