"use client";

import { useMemo } from "react";
import { useBalance } from "wagmi";
import { ethers } from "ethers";
import { Collection, AttributeInput } from "@/lib/api/graphql/generated";
import {
  ValidationState,
  MintCostData,
  NftMetadataInput,
} from "@/types/mint.types";

interface UseMintValidationProps {
  address?: string;
  collection?: Collection | null;
  isSameArtType: boolean;
  isAllowlistMint: boolean;
  imageFile?: string | null;
  amount: number;
  attributes: AttributeInput[];
  name: string;
  description: string;
  batchMetadata: NftMetadataInput[];
  signature?: string;
  nonce?: string;
  mintCostData?: MintCostData;
}

export function useMintValidation({
  address,
  collection,
  isSameArtType,
  isAllowlistMint,
  imageFile,
  amount,
  attributes,
  name,
  description,
  batchMetadata,
  signature,
  nonce,
  mintCostData,
}: UseMintValidationProps) {
  const { data: balance } = useBalance({ address });

  const validations: ValidationState = useMemo(
    () => ({
      isValidImage: isSameArtType
        ? true
        : Boolean(
            (imageFile && /^(ipfs|https):\/\//.test(imageFile)) ||
              batchMetadata.length > 0
          ),
      hasSufficientBalance: Boolean(
        balance &&
          Number(ethers.formatEther(balance.value)) >=
            Number(mintCostData?.getMintCost?.totalPrice ?? 0) +
              Number(mintCostData?.getMintCost?.estimatedGas ?? 0)
      ),
      isValidAmount: Boolean(
        amount >= 1 &&
          amount <=
            Math.min(
              collection?.mintLimit ? Number(collection.mintLimit) : 100,
              Number(collection?.maxSupply) - Number(collection?.totalMinted)
            ) &&
          (isSameArtType ||
            batchMetadata.length === 0 ||
            batchMetadata.reduce(
              (sum, meta) => sum + Number(meta.amount),
              0
            ) === amount)
      ),
      isValidAttributes: Boolean(
        attributes.length <= 10 &&
          attributes.every(
            (attr) => attr.trait_type.trim() && attr.value.trim()
          ) &&
          new Set(attributes.map((attr) => attr.trait_type.trim())).size ===
            attributes.length
      ),
      isValidBatch: Boolean(
        !isAllowlistMint &&
          batchMetadata.length <= 50 &&
          batchMetadata.every(
            (meta) =>
              Number(meta.amount) >= 1 &&
              Number(meta.amount) <=
                Math.min(
                  collection?.mintLimit ? Number(collection.mintLimit) : 100,
                  Number(collection?.maxSupply) -
                    Number(collection?.totalMinted)
                ) &&
              meta.name.trim() &&
              meta.description.trim() &&
              /^(ipfs|https):\/\//.test(meta.image || "")
          )
      ),
      isValidSignature: Boolean(
        !isAllowlistMint ||
          (signature && /^0x[0-9a-fA-F]{130}$/.test(signature))
      ),
      isValidNonce: Boolean(
        !isAllowlistMint ||
          (nonce && Number.isInteger(Number(nonce)) && Number(nonce) >= 0)
      ),
    }),
    [
      isSameArtType,
      imageFile,
      balance,
      mintCostData,
      amount,
      collection,
      attributes,
      name,
      description,
      batchMetadata,
      isAllowlistMint,
      signature,
      nonce,
    ]
  );

  const validateRoyalty = (value: number): boolean => {
    return value >= 0 && value <= 50;
  };

  const validateSignature = (value: string): boolean => {
    return /^0x[0-9a-fA-F]{130}$/.test(value);
  };

  const validateNonce = (value: string): boolean => {
    const num = Number(value);
    return Number.isInteger(num) && num >= 0;
  };

  return {
    validations,
    validateRoyalty,
    validateSignature,
    validateNonce,
  };
}
