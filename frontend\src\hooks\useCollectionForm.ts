"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
  ArtType, 
  TokenStandard, 
  AllowlistStageInput,
  PublicMintInput 
} from "@/lib/api/graphql/generated";
import { FormData, formSchema } from "@/types/collection.type";

interface FormState {
  selectedChain: string;
  selectedArtType: ArtType;
  allowlistStages: AllowlistStageInput[];
  isFormValid: boolean;
}

interface FormActions {
  setSelectedChain: (chain: string) => void;
  setSelectedArtType: (artType: ArtType) => void;
  setAllowlistStages: (stages: AllowlistStageInput[]) => void;
  setPublicMint: (mint: PublicMintInput) => void;
  resetForm: () => void;
  updateFormValidity: (hasRequiredFiles: boolean) => void;
}

const getDefaultFormValues = (): FormData => ({
  chain: "Sepolia",
  chainId: "11155111",
  name: "",
  description: "",
  artType: ArtType.Unique,
  uri: "",
  collectionImageUrl: "",
  tokenStandard: TokenStandard.Erc1155,
  mintPrice: "0.001",
  royaltyFee: "1",
  maxSupply: "11",
  mintLimit: "1",
  allowlistStages: [],
  mintStartDate: new Date(
    new Date().getTime() + 5 * 60 * 1000
  ).toISOString(),
  publicMint: {
    mintPrice: "0.0012",
    durationDays: "1",
    durationHours: "0",
    startDate: new Date(new Date().getTime() + 5 * 60 * 1000).toISOString(),
  },
});

export function useCollectionForm() {
  const [selectedChain, setSelectedChain] = useState("Sepolia");
  const [selectedArtType, setSelectedArtType] = useState<ArtType>(ArtType.Unique);
  const [allowlistStages, setAllowlistStages] = useState<AllowlistStageInput[]>([]);
  const [isFormValid, setIsFormValid] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultFormValues(),
  });

  // Update selected chain when form chain changes
  useEffect(() => {
    setSelectedChain(form.watch("chain") || "Sepolia");
  }, [form]);

  // Update form validity when form state or files change
  const updateFormValidity = (hasRequiredFiles: boolean) => {
    const basicFormValid = form.formState.isValid;
    setIsFormValid(basicFormValid && hasRequiredFiles);
  };

  const setPublicMint = (mint: PublicMintInput) => {
    form.setValue("publicMint", mint);
  };

  const resetForm = () => {
    form.reset(getDefaultFormValues());
    setSelectedChain("Sepolia");
    setSelectedArtType(ArtType.Unique);
    setAllowlistStages([]);
    setIsFormValid(false);
  };

  // Watch for art type changes and update state
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "artType" && value.artType) {
        setSelectedArtType(value.artType as ArtType);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  return {
    // Form instance
    form,
    
    // State
    selectedChain,
    selectedArtType,
    allowlistStages,
    isFormValid,
    publicMint: form.watch("publicMint"),
    
    // Actions
    setSelectedChain,
    setSelectedArtType,
    setAllowlistStages,
    setPublicMint,
    resetForm,
    updateFormValidity,
  };
}
