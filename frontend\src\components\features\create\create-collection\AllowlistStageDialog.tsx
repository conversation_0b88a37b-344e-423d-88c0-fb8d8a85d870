"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Footer,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import type { AllowlistStageInput } from "@/lib/api/graphql/generated";
import { toast } from "sonner";
import { DateTimePicker24h } from "@/components/ui/DateTimePicker24h";

interface AllowlistStageDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  stage: AllowlistStageInput | null;
  onSave: (stage: AllowlistStageInput) => void;
  mintStartDate?: Date;
  publicMintEndDate?: Date;
}

export function AllowlistStageDialog({
  isOpen,
  onOpenChange,
  stage,
  onSave,
  mintStartDate,
  publicMintEndDate,
}: AllowlistStageDialogProps) {
  const MAX_WALLET_LENGTH = 42;
  const [mintPrice, setMintPrice] = useState(stage?.mintPrice || "0.00");
  const [durationDays, setDurationDays] = useState(stage?.durationDays || "1");
  const [durationHours, setDurationHours] = useState(
    stage?.durationHours || "0"
  );
  const [wallets, setWallets] = useState(stage?.wallets || []);
  const [startDate, setStartDate] = useState<Date>(
    stage?.startDate ? new Date(stage.startDate) : new Date()
  );

  useEffect(() => {
    if (isOpen) {
      const now = new Date();
      const delayMinutes = 5;
      const defaultStartDate = publicMintEndDate
        ? new Date(
            Math.max(
              publicMintEndDate.getTime(),
              mintStartDate?.getTime() || now.getTime(),
              now.getTime() + delayMinutes * 60 * 1000
            )
          )
        : mintStartDate
        ? new Date(
            Math.max(
              mintStartDate.getTime(),
              now.getTime() + delayMinutes * 60 * 1000
            )
          )
        : new Date(now.getTime() + delayMinutes * 60 * 1000);

      if (stage) {
        setMintPrice(stage.mintPrice);
        setDurationDays(stage.durationDays);
        setDurationHours(stage.durationHours);
        setWallets(stage.wallets);
        const stageDate = new Date(stage.startDate);
        setStartDate(
          stageDate > now &&
            (!mintStartDate || stageDate >= mintStartDate) &&
            (!publicMintEndDate || stageDate >= publicMintEndDate)
            ? stageDate
            : defaultStartDate
        );
      } else {
        setMintPrice("0.00");
        setDurationDays("1");
        setDurationHours("0");
        setWallets([]);
        setStartDate(defaultStartDate);
      }
    }
  }, [isOpen, stage, mintStartDate, publicMintEndDate]);

  const handleSave = () => {
    if (!/^\d+(\.\d+)?$/.test(mintPrice)) {
      toast.error("Mint price must be a valid number");
      return;
    }
    if (!/^\d+$/.test(durationDays)) {
      toast.error("Duration days must be a valid number");
      return;
    }
    if (!/^\d+$/.test(durationHours)) {
      toast.error("Duration hours must be a valid number");
      return;
    }

    const parsedDays = parseInt(durationDays);
    const parsedHours = parseInt(durationHours);
    const maxDays = 30;
    const maxHours = 23;

    if (parsedDays > maxDays) {
      toast.error(`Duration days cannot exceed ${maxDays}`);
      return;
    }
    if (parsedHours > maxHours) {
      toast.error(`Duration hours cannot exceed ${maxHours}`);
      return;
    }
    if (!wallets.length) {
      toast.error("Wallets are required");
      return;
    }
    if (publicMintEndDate && startDate <= publicMintEndDate) {
      toast.error(
        `Allowlist stage must start after public mint ends (${format(
          publicMintEndDate,
          "MM/dd/yyyy h:mm a"
        )})`
      );
      return;
    }

    const newStage: AllowlistStageInput = {
      mintPrice,
      durationDays,
      durationHours,
      wallets,
      startDate: startDate.toISOString(),
    };

    onSave(newStage);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTitle className="bg-white dark:bg-[#0e0a1a] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white hidden">
        Allowlist Stage
      </DialogTitle>
      <DialogContent
        className="bg-white dark:bg-[#0e0a1a] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white max-w-md p-0"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <div className="p-6 pb-0 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Allowlist Stage
          </h2>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <Label className="text-gray-900 dark:text-white">Mint Price</Label>
            <div className="flex mt-2">
              <Input
                placeholder="0.00"
                value={mintPrice}
                onChange={(e) => setMintPrice(e.target.value)}
                className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
              <div className="bg-gray-100 dark:bg-[#2a2535] border border-gray-200 dark:border-[#3a3450] rounded-r-md px-4 flex items-center text-gray-900 dark:text-white">
                ETH
              </div>
            </div>
          </div>

          <div>
            <Label className="text-gray-900 dark:text-white">
              Stage Duration
            </Label>
            <div className="flex gap-2 mt-2">
              <div className="flex flex-1">
                <Input
                  placeholder="1"
                  value={durationDays}
                  onChange={(e) => setDurationDays(e.target.value)}
                  className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                <div className="bg-gray-100 dark:bg-[#2a2535] border border-gray-200 dark:border-[#3a3450] rounded-r-md px-4 flex items-center text-gray-900 dark:text-white">
                  Days
                </div>
              </div>
              <div className="flex flex-1">
                <Input
                  placeholder="0"
                  value={durationHours}
                  onChange={(e) => setDurationHours(e.target.value)}
                  className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                <div className="bg-gray-100 dark:bg-[#2a2535] border border-gray-200 dark:border-[#3a3450] rounded-r-md px-4 flex items-center text-gray-900 dark:text-white">
                  Hours
                </div>
              </div>
            </div>
          </div>

          <div>
            <Label className="text-gray-900 dark:text-white">
              Wallets (one per line)
            </Label>
            <Textarea
              value={wallets.join("\n")}
              onChange={(e) => {
                const newWallets = e.target.value
                  .split("\n")
                  .map((wallet) =>
                    wallet.length > MAX_WALLET_LENGTH
                      ? wallet.slice(0, MAX_WALLET_LENGTH)
                      : wallet
                  );
                setWallets(newWallets);
              }}
              placeholder="0x123...\n0x456..."
              className="bg-gray-50 dark:bg-[#1a1525] border-gray-200 dark:border-[#3a3450] text-gray-900 dark:text-white mt-2 min-h-[100px] max-h-[300px] overflow-y-auto focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          <div>
            <Label className="text-gray-900 dark:text-white">Start Date</Label>
            <DateTimePicker24h
              value={startDate}
              onChange={(newDate) => {
                if (newDate) {
                  const now = new Date();
                  const minDate = mintStartDate
                    ? new Date(
                        Math.max(
                          mintStartDate.getTime(),
                          now.getTime() + 5 * 60 * 1000
                        )
                      )
                    : new Date(now.getTime() + 5 * 60 * 1000);
                  const adjustedDate = new Date(newDate);

                  // If newDate is less than minDate, adjust hours/minutes
                  if (adjustedDate < minDate) {
                    adjustedDate.setHours(minDate.getHours());
                    adjustedDate.setMinutes(minDate.getMinutes());
                    adjustedDate.setSeconds(0);
                  }

                  setStartDate(adjustedDate);
                }
              }}
              disabledDates={(checkDate: Date) => {
                const now = new Date();
                const minDate = mintStartDate
                  ? new Date(
                      Math.max(
                        mintStartDate.getTime(),
                        now.getTime() + 5 * 60 * 1000
                      )
                    )
                  : new Date(now.getTime() + 5 * 60 * 1000);
                // Normalize dates to avoid timezone issues
                const checkDateStr = format(checkDate, "yyyy-MM-dd");
                const minDateStr = format(minDate, "yyyy-MM-dd");
                return checkDateStr < minDateStr;
              }}
              disabledHours={(date: Date) => (hour: number) => {
                const now = new Date();
                if (date.toDateString() === now.toDateString()) {
                  return hour < now.getHours();
                }
                return false;
              }}
              disabledMinutes={(date: Date, hour: number) =>
                (minute: number) => {
                  const now = new Date();
                  if (
                    date.toDateString() === now.toDateString() &&
                    hour === now.getHours()
                  ) {
                    return minute < now.getMinutes();
                  }
                  return false;
                }}
              className="mt-2 [&>div>button]:bg-gray-50 [&>div>button]:dark:bg-[#1a1525] [&>div>button]:border-gray-200 dark:border-[#3a3450] [&>div>button]:text-gray-900 [&>div>button]:dark:text-white [&>div>button]:focus-visible:ring-0 [&>div>button]:focus-visible:ring-offset-0 [&>div>div>div]:bg-white [&>div>div>div]:dark:bg-[#1a1525] [&>div>div>div]:border-gray-200 dark:border-[#3a3450] [&>div>div>div]:text-gray-900 [&>div>div>div]:dark:text-white"
            />
          </div>

          <DialogFooter className="px-0 pt-2">
            <Button
              type="submit"
              onClick={handleSave}
              className="w-full bg-pink-600 hover:bg-pink-700 text-white"
            >
              Done
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
