# Mutations
mutation ListNFTs($input: ListNFTsInput!) {
  listNFTs(input: $input) {
    steps {
      id
      params
    }
    listings {
      tokenId
      contractAddress
      listingPrice
      standard
      amount
      metadataURI
      listingType
      expiryTimestamp
    }
    errors {
      tokenId
      contractAddress
      message
      errorCode
    }
    status
  }
}

query GetNonceListing($input: GetNonceListingInput!) {
  getNonceListing(input: $input) {
    success
    nonce
  }
}

mutation ConfirmListingNft($input: ConfirmListingNftInput!) {
  confirmListingNft(input: $input) {
    success
    listingIds
    errorMessage
  }
}

# Queries
query GetPriceListing($input: GetPriceListingInput!) {
  getPriceListing(input: $input) {
    success
    listingPrice
    updatedAt
    errorMessage
  }
}

# Subscriptions
subscription NftListedRealTimePublic($input: NftListedFilter!) {
  nftListedRealTimePublic(input: $input) {
    nftId
    tokenId
    contractAddress
    chainId
    listingPrice
    standard
    currency
    wallet
  }
}

subscription NftListedRealTimePrivate($input: NftListedFilter!) {
  nftListedRealTimePrivate(input: $input) {
    nftId
    tokenId
    contractAddress
    chainId
    listingPrice
    standard
    currency
    wallet
  }
}
