import { SlideCollection } from "@/types/collection.type";
import { motion } from "framer-motion";

export const BackgroundBanner = ({
  slide,
  currentSlide,
  index,
}: {
  slide: SlideCollection;
  currentSlide: number;
  index: number;
}) => {
  return (
    <motion.div
      key={index}
      className="absolute inset-0 z-10"
      initial={{ opacity: 0 }}
      animate={{ opacity: currentSlide === index ? 1 : 0 }}
      transition={{ duration: 0.7 }}
      style={{
        backgroundImage: `linear-gradient(to right, ${slide.color1}20, ${slide.color2}10), url(${slide.image})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-white/90 via-white/70 to-transparent dark:from-[#121620]/90 dark:via-[#121620]/70 dark:to-transparent"></div>
    </motion.div>
  );
};
