import { <PERSON>ader2, <PERSON>rk<PERSON> } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface MintPanelConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentAmount: number;
  collectionName: string;
  totalPrice: string;
  estimatedGas: string;
  isBatchMint: boolean;
  batchMetadataLength: number;
  isMintingNft: boolean;
  isLoadingMintCost: boolean;
  onConfirm: () => void;
}

export function MintPanelConfirmDialog({
  open,
  onOpenChange,
  currentAmount,
  collectionName,
  totalPrice,
  estimatedGas,
  isBatchMint,
  batchMetadataLength,
  isMintingNft,
  isLoadingMintCost,
  onConfirm,
}: MintPanelConfirmDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Mint</DialogTitle>
          <DialogDescription>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              You are about to mint{" "}
              <span className="font-medium">
                {currentAmount} NFT{Number(currentAmount) > 1 ? "s" : ""}
              </span>{" "}
              from <span className="font-medium">{collectionName}</span>.
            </p>
            <div className="bg-gray-50 dark:bg-[#0f0a19] p-4 rounded-md border border-gray-200 dark:border-gray-800/50">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Total Cost:</span> {totalPrice}{" "}
                ETH
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">Gas Fee:</span> {estimatedGas} ETH
              </p>
              {isBatchMint && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Batch Mint:</span>{" "}
                  {batchMetadataLength} unique NFTs
                </p>
              )}
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-200 dark:border-gray-800"
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isMintingNft || isLoadingMintCost}
            className="bg-pink-500 hover:bg-pink-600 text-white dark:bg-pink-600 dark:hover:bg-pink-700"
          >
            {isMintingNft || isLoadingMintCost ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="mr-2 h-4 w-4" />
            )}
            {isMintingNft || isLoadingMintCost
              ? "Calculating..."
              : "Confirm Mint"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
