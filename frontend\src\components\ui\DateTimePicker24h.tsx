"use client";

import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

interface DateTimePicker24hProps {
  value?: Date;
  onChange?: (date: Date) => void;
  disabled?: boolean;
  disabledDates?: (date: Date) => boolean;
  disabledHours?: (date: Date) => (hour: number) => boolean;
  disabledMinutes?: (date: Date, hour: number) => (minute: number) => boolean;
  placeholder?: string;
  className?: string;
}

export function DateTimePicker24h({
  value,
  onChange,
  disabled = false,
  disabledDates,
  disabledHours,
  disabledMinutes,
  placeholder = "MM/DD/YYYY HH:mm",
  className,
}: DateTimePicker24hProps) {
  const handleDateSelect = (date: Date | undefined) => {
    if (date && onChange) {
      const newDate = new Date(date);
      // Preserve existing time if value exists
      if (value) {
        newDate.setHours(value.getHours(), value.getMinutes());
      }
      onChange(newDate);
    }
  };

  const handleTimeChange = (type: "hour" | "minute", val: string) => {
    if (!onChange) return;

    try {
      const currentDate = value || new Date();
      const newDate = new Date(currentDate);

      if (type === "hour") {
        const hour = parseInt(val, 10);
        if (isNaN(hour) || hour < 0 || hour > 23) return;
        newDate.setHours(hour);
      } else if (type === "minute") {
        const minute = parseInt(val, 10);
        if (isNaN(minute) || minute < 0 || minute > 59) return;
        newDate.setMinutes(minute);
      }

      onChange(newDate);
    } catch (error) {
      console.error("Error updating time:", error);
    }
  };

  return (
    <div className={cn("flex flex-col", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full pl-3 text-left font-normal hover:bg-inherit hover:text-inherit cursor-pointer bg-inherit",
              !value && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            {value ? format(value, "MM/dd/yyyy HH:mm") : placeholder}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 ">
          <div className="sm:flex">
            <Calendar
              mode="single"
              selected={value}
              onSelect={handleDateSelect}
              initialFocus
              disabled={disabledDates || disabled}
            />
            <div className="flex flex-col sm:flex-row sm:h-[300px] divide-y sm:divide-y-0 sm:divide-x">
              <ScrollArea className="w-64 sm:w-auto">
                <div className="flex sm:flex-col p-2">
                  {Array.from({ length: 24 }, (_, i) => i)
                    .reverse()
                    .map((hour) => (
                      <Button
                        key={hour}
                        size="icon"
                        variant={
                          value && value.getHours() === hour
                            ? "default"
                            : "ghost"
                        }
                        className="sm:w-full shrink-0 aspect-square"
                        onClick={() =>
                          handleTimeChange("hour", hour.toString())
                        }
                        disabled={
                          disabled ||
                          (disabledHours &&
                            disabledHours(value || new Date())(hour))
                        }
                      >
                        {hour}
                      </Button>
                    ))}
                </div>
                <ScrollBar orientation="horizontal" className="sm:hidden" />
              </ScrollArea>
              <ScrollArea className="w-64 sm:w-auto">
                <div className="flex sm:flex-col p-2">
                  {Array.from({ length: 12 }, (_, i) => i * 5).map((minute) => (
                    <Button
                      key={minute}
                      size="icon"
                      variant={
                        value && value.getMinutes() === minute
                          ? "default"
                          : "ghost"
                      }
                      className="sm:w-full shrink-0 aspect-square"
                      onClick={() =>
                        handleTimeChange("minute", minute.toString())
                      }
                      disabled={
                        disabled ||
                        (disabledMinutes &&
                          disabledMinutes(
                            value || new Date(),
                            value?.getHours() || 0
                          )(minute))
                      }
                    >
                      {minute.toString().padStart(2, "0")}
                    </Button>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" className="sm:hidden" />
              </ScrollArea>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
