import { configureStore, combineReducers } from "@reduxjs/toolkit";
import chainsReducer from "./slices/chainsSlice";
import collectionReducer from "./slices/collectionSlice";
import nftsReducer from "./slices/nftsSlice";
import userReducer from "./slices/userSlice";
import { persistStore, persistReducer } from "redux-persist";
import authReducer from "./slices/authSlice";
import homeReducer from "./slices/homeSlice";
import storageEngine from "@/store/storageEngine";
import walletSlice from "./slices/walletSlice";
import myItemsNftReducer from "./slices/myItemsSlice";
import marketplaceReducer from "./slices/marketplaceSlice";
const persistConfig = {
  key: "root",
  storage: storageEngine,
  whitelist: ["auth"],
};
const rootReducer = combineReducers({
  auth: authReducer,
  home: homeReducer,
  collection: collectionReducer,
  wallet: walletSlice,
  myItemsNft: myItemsNftReducer,
  marketplace: marketplaceReducer,

  // deleted
  chains: chainsReducer,
  nfts: nftsReducer,
  user: userReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "persist/PERSIST",
          "persist/REHYDRATE",
          "persist/PURGE",
        ],
      },
    }),
  devTools: process.env.NODE_ENV !== "production",
});

// Create persistor
export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
