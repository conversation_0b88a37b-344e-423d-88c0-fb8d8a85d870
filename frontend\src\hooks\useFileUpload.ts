"use client";

import { useState } from "react";
import { toast } from "sonner";
import { uploadFilePinata } from "@/lib/utils/upload";
import { ArtType } from "@/lib/api/graphql/generated";
import { FormData } from "@/types/collection.type";
import { v4 as uuidv4 } from "uuid";

interface UseFileUploadProps {
  selectedArtType: ArtType;
}

interface FileUploadState {
  collectionImageFile: File | null;
  artworkFile: File | null;
  metadataUrl: string;
  isUploading: boolean;
}

interface FileUploadActions {
  setCollectionImageFile: (file: File | null) => void;
  setArtworkFile: (file: File | null) => void;
  setMetadataUrl: (url: string) => void;
  uploadCollectionImage: () => Promise<string>;
  uploadMetadata: (values: FormData) => Promise<string>;
  validateFiles: () => boolean;
  resetFiles: () => void;
}

export function useFileUpload({ selectedArtType }: UseFileUploadProps): FileUploadState & FileUploadActions {
  const [collectionImageFile, setCollectionImageFile] = useState<File | null>(null);
  const [artworkFile, setArtworkFile] = useState<File | null>(null);
  const [metadataUrl, setMetadataUrl] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  const validateFiles = (): boolean => {
    if (!collectionImageFile) {
      toast.error("Collection image is required");
      return false;
    }

    if (selectedArtType === ArtType.Same && !artworkFile) {
      toast.error("Artwork file is required for SAME art type");
      return false;
    }

    if (selectedArtType === ArtType.Unique && !metadataUrl.trim()) {
      toast.error("Metadata URL is required for UNIQUE art type");
      return false;
    }

    return true;
  };

  const uploadCollectionImage = async (): Promise<string> => {
    if (!collectionImageFile) {
      throw new Error("No collection image file selected");
    }

    setIsUploading(true);
    try {
      const fileName = collectionImageFile.name;
      const extension = fileName.split(".").pop();
      const baseName = fileName.slice(0, -(extension?.length || 0) - 1);
      
      const renamedFile = new File(
        [collectionImageFile],
        `${baseName}-${uuidv4()}.${extension}`,
        { type: collectionImageFile.type }
      );

      const url = await uploadFilePinata(renamedFile);
      if (!url) {
        throw new Error("Failed to upload collection image");
      }

      return url;
    } finally {
      setIsUploading(false);
    }
  };

  const uploadMetadata = async (values: FormData): Promise<string> => {
    if (selectedArtType === ArtType.Unique) {
      return metadataUrl;
    }

    if (!artworkFile) {
      throw new Error("No artwork file selected");
    }

    setIsUploading(true);
    try {
      // Upload artwork first
      const fileName = artworkFile.name;
      const extension = fileName.split(".").pop();
      const baseName = fileName.slice(0, -(extension?.length || 0) - 1);
      
      const artworkUrl = await uploadFilePinata(
        new File([artworkFile], `${baseName}-${uuidv4()}.${extension}`, {
          type: artworkFile.type,
        })
      );

      if (!artworkUrl) {
        throw new Error("Failed to upload artwork");
      }

      // Create and upload metadata
      const metadata = {
        name: values.name,
        description: values.description,
        image: artworkUrl,
        attributes: [],
      };

      const metadataFile = new File(
        [JSON.stringify(metadata)],
        `${values.name}-metadata-${uuidv4()}.json`,
        { type: "application/json" }
      );

      const metadataUrl = await uploadFilePinata(metadataFile);
      if (!metadataUrl) {
        throw new Error("Failed to upload metadata");
      }

      return metadataUrl;
    } finally {
      setIsUploading(false);
    }
  };

  const resetFiles = () => {
    setCollectionImageFile(null);
    setArtworkFile(null);
    setMetadataUrl("");
    setIsUploading(false);
  };

  return {
    // State
    collectionImageFile,
    artworkFile,
    metadataUrl,
    isUploading,
    
    // Actions
    setCollectionImageFile,
    setArtworkFile,
    setMetadataUrl,
    uploadCollectionImage,
    uploadMetadata,
    validateFiles,
    resetFiles,
  };
}
