"use client";

import { toast } from "sonner";
import { MintPanelSkeleton } from "./MintPanelSkeleton";
import { useMintPanel } from "@/hooks/useMintPanel";
import { MintPanelTabs } from "./MintPanelTabs";
import { MintPanelContent } from "./MintPanelContent";
import { MintPanelHistory } from "./MintPanelHistory";
import { MintPanelConfirmDialog } from "./MintPanelConfirmDialog";

interface MintPanelProps {
  currentGalleryImage?: string;
}

export default function MintPanel({ currentGalleryImage }: MintPanelProps) {
  const {
    // State
    address,
    showConfirmModal,
    setShowConfirmModal,
    isBatchMint,
    activeTab,
    setActiveTab,
    collectionData,
    isAllowlistMint,
    nftsData,
    mintCostData,
    lastMintCost,
    isLoadingMintCost,
    currentAmount,
    formState,
    formActions,
    formErrors,
    isSameArtType,

    // Actions
    handleMintConfirm,
    refetchNfts,
  } = useMintPanel();

  if (!collectionData) {
    return <MintPanelSkeleton />;
  }

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <MintPanelTabs
        address={address}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      >
        <MintPanelContent
          collectionData={collectionData}
          isSameArtType={isSameArtType}
          mintCostData={mintCostData}
          lastMintCost={lastMintCost}
          formState={formState}
          formActions={formActions}
          formErrors={formErrors}
          isAllowlistMint={isAllowlistMint}
          isLoadingMintCost={isLoadingMintCost}
          onMintSuccess={() => {
            formActions.resetForm();
            refetchNfts();
          }}
          onMintError={(error: string) =>
            toast.error("Mint failed", { description: error })
          }
          onConfirm={() => handleMintConfirm(false)}
          currentGalleryImage={currentGalleryImage}
        />

        {address && <MintPanelHistory nfts={nftsData?.getNfts?.nfts} />}
      </MintPanelTabs>

      <MintPanelConfirmDialog
        open={showConfirmModal}
        onOpenChange={setShowConfirmModal}
        currentAmount={currentAmount}
        collectionName={collectionData.name}
        totalPrice={
          mintCostData?.getMintCost?.success
            ? mintCostData.getMintCost.totalPrice
            : lastMintCost.totalPrice
        }
        estimatedGas={
          mintCostData?.getMintCost?.success
            ? mintCostData.getMintCost.estimatedGas
            : lastMintCost.estimatedGas
        }
        isBatchMint={isBatchMint}
        batchMetadataLength={formState.batchMetadata.length}
        isMintingNft={false}
        isLoadingMintCost={isLoadingMintCost}
        onConfirm={() => handleMintConfirm(isBatchMint)}
      />
    </div>
  );
}
