import { Collection } from "@/lib/api/graphql/generated";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface CollectionState {
  data: Collection | null;
  loading: boolean;
  error: string | null;
}

const initialState: CollectionState = {
  data: null,
  loading: false,
  error: null,
};

const collectionSlice = createSlice({
  name: "collection",
  initialState,
  reducers: {
    setCollection(state, action: PayloadAction<Collection>) {
      state.data = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
  },
});

export const { setCollection, setLoading, setError } = collectionSlice.actions;
export default collectionSlice.reducer;
