import Image from "next/image";
import { Collection } from "@/lib/api/graphql/generated";

interface MintPanelHeaderProps {
  collection: Collection;
  isSameArtType: boolean;
  mintPrice: string;
  estimatedGas: string;
}

export function MintPanelHeader({
  collection,
  isSameArtType,
  mintPrice,
  estimatedGas,
}: MintPanelHeaderProps) {
  return (
    <div className="bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg p-4 border dark:border-gray-800/50 shadow-sm">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          <Image
            src={collection?.image || ""}
            alt={collection?.name || ""}
            width={48}
            height={48}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {collection?.name}
          </h3>
          <div className="flex items-center gap-2 mt-1">
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                isSameArtType
                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                  : "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400"
              }`}
            >
              {isSameArtType ? "Same Art" : "Unique Art"}
            </span>
          </div>
        </div>
      </div>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-600 dark:text-gray-400 text-xs">
            Mint Price
          </span>
          <span className="text-gray-900 dark:text-white text-xl font-bold">
            {mintPrice} ETH
          </span>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1.5">
            <span className="text-gray-600 dark:text-gray-400 text-xs">
              Estimated Gas
            </span>
          </div>
          <span className="text-gray-600 dark:text-gray-400 text-xs">
            {estimatedGas} ETH
          </span>
        </div>
      </div>
    </div>
  );
}
