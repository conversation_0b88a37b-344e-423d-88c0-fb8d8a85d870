"use client";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import {
  setCollection,
  setError,
  setLoading,
} from "@/store/slices/collectionSlice";
import {
  FilterOperator,
  useGetCollectionQuery,
} from "@/lib/api/graphql/generated";
import { toast } from "sonner";

export const useDetailCollection = (
  chainId: string,
  contractAddress: string
) => {
  const dispatch = useDispatch<AppDispatch>();
  const collection = useSelector((state: RootState) => state.collection.data);

  const {
    data: collectionData,
    loading,
    error,
    refetch,
  } = useGetCollectionQuery({
    variables: {
      input: {
        filters:
          chainId && contractAddress
            ? [
                {
                  field: "chainId",
                  operator: FilterOperator.Eq,
                  value: chainId,
                },
                {
                  field: "contractAddress",
                  operator: FilterOperator.Eq,
                  value: contractAddress,
                },
              ]
            : [],
        includeStats: false,
      },
    },
    fetchPolicy: "cache-first",
    onError: (err) => {
      dispatch(setError(err.message));
      toast.error("Failed to fetch collection", { description: err.message });
    },
    onCompleted: (data) => {
      if (data?.getCollection) {
        dispatch(setCollection(data.getCollection));
        dispatch(setLoading(false));
      }
    },
  });


  return {
    collection: collectionData?.getCollection || collection,
    loading,
    error: error?.message,
    refetch,
  };
};
