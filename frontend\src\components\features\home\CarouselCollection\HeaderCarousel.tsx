import { Button } from "@/components/ui/button";
import { RootState } from "@/store/store";
import { redirect } from "next/navigation";
import { useSelector } from "react-redux";

export function HeaderCarousel() {
  const { selectedChain } = useSelector((state: RootState) => state.home);
  return (
    <div className="flex items-center justify-between mb-8">
      <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
        NFT Drops Calendar
      </h2>
      <Button
        variant="outline"
        size="sm"
        className="border-gray-200 dark:border-white/10 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-white/5"
        onClick={() => {
          redirect(`/collections${selectedChain ? `/${selectedChain}` : ""}`);
        }}
      >
        See all
      </Button>
    </div>
  );
}
