import { Stats } from "@/lib/api/graphql/generated";
import { SlideCollection } from "@/types/collection.type";

export const StatsDisplay = ({
  stats,
  currentSlide,
  slides,
}: {
  stats: Stats;
  currentSlide: number;
  slides: SlideCollection[];
}) => {
  const currentColors = slides[currentSlide] || slides[0];
  return (
    <div className="flex gap-8 mt-12">
      {[
        { value: stats.artworks, label: "Artworks" },
        { value: stats.artists, label: "Artists" },
        { value: stats.collectors, label: "Collectors" },
      ].map((stat, index) => (
        <div key={index}>
          <p
            className="text-3xl font-bold text-transparent bg-clip-text"
            style={{
              backgroundImage: `linear-gradient(to right, ${currentColors.color1}, ${currentColors.color2})`,
            }}
          >
            {stat.value !== undefined
              ? `${stat.value.toLocaleString()}+`
              : "N/A"}
          </p>
          <p className="text-gray-600 dark:text-gray-400">{stat.label}</p>
        </div>
      ))}
    </div>
  );
};
