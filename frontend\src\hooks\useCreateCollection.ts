/**
 * @deprecated This hook has been refactored into smaller, more manageable hooks.
 * Use useCreateCollectionOrchestrator instead.
 *
 * This file is kept for backward compatibility and will be removed in a future version.
 */

import { useCreateCollectionOrchestrator } from "./useCreateCollectionOrchestrator";

/**
 * @deprecated Use useCreateCollectionOrchestrator instead
 */
export function useCreateCollection() {
  return useCreateCollectionOrchestrator();
}

async function withRetry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 5000
): Promise<T> {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      console.warn(
        `Retry ${i + 1}/${retries} after ${delay}ms: ${
          (error as Error).message
        }`
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
  throw new Error("Retry limit reached");
}

async function getRevertReason(
  provider: <PERSON><PERSON>er<PERSON>rovider,
  txHash: string
): Promise<string> {
  try {
    const tx = await provider.getTransaction(txHash);
    if (!tx) return "Transaction not found";
    // Gọi lại giao dịch để lấy lý do revert
    try {
      await provider.call({
        to: tx.to,
        from: tx.from,
        data: tx.data,
        gasLimit: tx.gasLimit,
        gasPrice: tx.gasPrice,
        blockTag: tx.blockNumber ?? "latest",
      });
      return "No revert reason (call succeeded)";
    } catch (callError: any) {
      if (callError.data) {
        return `Revert reason: ${callError.data}`;
      }
      return `Revert detected: ${callError.message}`;
    }
  } catch (error) {
    return `Failed to get revert reason: ${(error as Error).message}`;
  }
}
async function estimateGasWithBuffer(signer: any, tx: any): Promise<bigint> {
  try {
    const estimatedGas = await signer.estimateGas(tx);
    const bufferedGas = (estimatedGas * BigInt(150)) / BigInt(100); // +50% buffer
    const maxGasLimit = BigInt(15_000_000);
    const minGasLimit = BigInt(500_000); // Gas tối thiểu cho deploy_proxy
    console.log(
      `Estimated gas: ${estimatedGas.toString()}, Buffered gas: ${bufferedGas.toString()}`
    );
    return bufferedGas < minGasLimit
      ? minGasLimit
      : bufferedGas > maxGasLimit
      ? maxGasLimit
      : bufferedGas;
  } catch (error) {
    console.error(`Gas estimation failed: ${(error as Error).message}`);
    const fallbackGas = tx.data.includes("deploy_implementation")
      ? BigInt(8_000_000)
      : BigInt(1_000_000); // Tăng fallback cho proxy
    console.warn(`Using fallback gas limit: ${fallbackGas.toString()}`);
    return fallbackGas;
  }
}
async function waitForBytecode(
  provider: BrowserProvider,
  address: string,
  txHash: string,
  maxAttempts = 15, // Tăng số lần thử
  delay = 10000 // Tăng độ trễ
): Promise<string | null> {
  console.log(`Kiểm tra giao dịch ${txHash} cho địa chỉ ${address}`);
  try {
    const transaction = await provider.getTransaction(txHash);
    if (!transaction) {
      console.error(`Không tìm thấy giao dịch: ${txHash}`);
      return null;
    }
    const receipt = await transaction.wait(2); // Chờ 2 xác nhận
    if (!receipt || receipt.status !== 1) {
      console.error(
        `Giao dịch thất bại: status=${receipt?.status}, contractAddress=${receipt?.contractAddress}`
      );
      return null;
    }
    console.log(`Receipt hợp lệ: contractAddress=${receipt.contractAddress}`);
  } catch (error) {
    console.error(`Xác minh giao dịch thất bại cho ${txHash}: ${error}`);
    return null;
  }

  for (let i = 0; i < maxAttempts; i++) {
    try {
      console.log(
        `Thử lấy bytecode tại ${address}, lần ${i + 1}/${maxAttempts}`
      );
      const bytecode = await Promise.race([
        provider.getCode(address),
        new Promise(
          (_, reject) =>
            setTimeout(() => reject(new Error("Timeout RPC")), 20000) // Tăng timeout lên 20s
        ),
      ]);

      console.log(
        `Bytecode tại ${address}: ${(bytecode as string).slice(0, 20)}...`
      );
      if (bytecode !== "0x") return bytecode as string;
      console.log(`Bytecode là 0x, thử lại sau ${delay}ms...`);
    } catch (error) {
      console.warn(`Lấy bytecode thất bại: ${(error as Error).message}`);
    }
    await new Promise((resolve) => setTimeout(resolve, delay));
  }
  console.error(`Không có bytecode tại ${address} sau ${maxAttempts} lần thử`);
  return null;
}
async function verifyProxyContract(
  provider: BrowserProvider,
  address: string,
  signer: any,
  txHash: string
): Promise<boolean> {
  console.log(`Bắt đầu xác minh proxy cho ${address}`);
  try {
    const bytecode = await waitForBytecode(provider, address, txHash);
    if (!bytecode) {
      console.warn(
        `Không tìm thấy bytecode hợp lệ tại ${address}. Bỏ qua kiểm tra tên.`
      );
      return false; // Không ném lỗi, cho phép Mutation 2 tiếp tục
    }
    console.log(`Bytecode proxy: ${bytecode.slice(0, 20)}...`);

    const nftManager = new ethers.Contract(address, NFTManager.abi, signer);
    const name = await withRetry(() => nftManager.name(), 3, 4000);
    console.log(`Tên qua proxy: ${name}`);
    return true;
  } catch (error) {
    const revertReason = await getRevertReason(provider, txHash);
    const immediateBytecode = await provider.getCode(address);
    console.error(
      `Xác minh proxy thất bại: ${
        (error as Error).message
      }, Lý do hoàn tác: ${revertReason}, Bytecode tức thì: ${immediateBytecode}`
    );
    return false; // Không ném lỗi, cho phép Mutation 2 tiếp tục
  } finally {
    console.log(`Hoàn tất xác minh proxy cho ${address}`);
  }
}
export function useCreateCollection() {
  const { address, chain, isConnected, chainId: chainIdAccount } = useAccount();
  const { connectAsync } = useConnect();
  const { switchChainAsync } = useSwitchChain();
  const { data: walletClient } = useWalletClient();
  const [createCollectionMutation] = useCreateCollectionMutation();

  const [isLoading, setIsLoading] = useState(false);
  const [allowlistStages, setAllowlistStages] = useState<AllowlistStageInput[]>(
    []
  );
  const [selectedChain, setSelectedChain] = useState("Sepolia");
  const [selectedArtType, setSelectedArtType] = useState<ArtType>(
    ArtType.Unique
  );
  const [collectionImageFile, setCollectionImageFile] = useState<File | null>(
    null
  );
  const [artworkFile, setArtworkFile] = useState<File | null>(null);
  const [metadataUrl, setMetadataUrl] = useState("");
  const [isFormValid, setIsFormValid] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [step1Status, setStep1Status] = useState<StepStatus>("pending");
  const [step2Status, setStep2Status] = useState<StepStatus>("pending");
  const [deployedContractAddress, setDeployedContractAddress] = useState<
    string | null
  >(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      chain: "Sepolia",
      chainId: "11155111",
      name: "",
      description: "",
      artType: ArtType.Unique,
      uri: "",
      collectionImageUrl: "",
      tokenStandard: TokenStandard.Erc1155,
      mintPrice: "0.001",
      royaltyFee: "1",
      maxSupply: "11",
      mintLimit: "1",
      allowlistStages: [],
      mintStartDate: new Date(
        new Date().getTime() + 5 * 60 * 1000
      ).toISOString(),
      publicMint: {
        mintPrice: "0.0012",
        durationDays: "1",
        durationHours: "0",
        startDate: new Date(new Date().getTime() + 5 * 60 * 1000).toISOString(),
      },
    },
  });

  const { data: collectionCreatedData, error: subscriptionError } =
    useCollectionModifiedPublicRealtimeSubscription({
      variables: {
        input: {
          chainId: chainIdAccount?.toString() || "",
        },
      },
      onData: (data) => {
        console.log("data", data);
      },
      onError: (error) => {
        console.error("Subscription error:", error);
        toast.error("Failed to subscribe to collection creation", {
          description: error.message,
        });
      },
      skip: !deployedContractAddress || !isAddress(deployedContractAddress),
    });
  useEffect(() => {
    setSelectedChain(form.watch("chain") || "Sepolia");
  }, [form]);

  useEffect(() => {
    const basicFormValid = form.formState.isValid;
    const hasRequiredFiles =
      collectionImageFile !== null &&
      (selectedArtType === ArtType.Same
        ? artworkFile !== null
        : metadataUrl.trim() !== "");
    setIsFormValid(basicFormValid && hasRequiredFiles);
  }, [
    form.formState.isValid,
    collectionImageFile,
    selectedArtType,
    artworkFile,
    metadataUrl,
  ]);

  useEffect(() => {
    if (
      collectionCreatedData?.collectionModifiedPublicRealtime.action ===
      ActionType.Create
    ) {
      const { contractAddress, name, id } =
        collectionCreatedData.collectionModifiedPublicRealtime.data;
      setStep2Status("completed");
      setIsModalOpen(false);
      toast.success("Collection created", {
        description: `${name} - Collection ID: ${id} - Contract Address: ${contractAddress}`,
      });
      handleClearForm();
    }
  }, [collectionCreatedData]);

  const getButtonText = () =>
    !isConnected
      ? "Connect Wallet"
      : isLoading
      ? "Processing..."
      : chain?.name === selectedChain
      ? `Create Collection on ${chain.name}`
      : `Switch to ${selectedChain}`;

  const handleClearForm = () => {
    form.reset();
    setSelectedChain("Sepolia");
    setAllowlistStages([]);
    setSelectedArtType(ArtType.Unique);
    setMetadataUrl("");
    setArtworkFile(null);
    setCollectionImageFile(null);
    setIsLoading(false);
    setIsModalOpen(false);
    setStep1Status("pending");
    setStep2Status("pending");
    setDeployedContractAddress(null);
    toast.success("Form cleared successfully");
  };

  const uploadMetadata = async (values: FormData, file: File) => {
    const fileName = file.name;
    const extension = fileName.split(".").pop();
    const baseName = fileName.slice(0, -(extension?.length || 0) - 1);
    const artworkUrl = await uploadFilePinata(
      new File([file], `${baseName}-${uuidv4()}.${extension}`, {
        type: file.type,
      })
    );
    if (!artworkUrl) throw new Error("Failed to upload artwork");
    const metadata = {
      name: values.name,
      description: values.description,
      image: artworkUrl,
      attributes: [],
    };
    const metadataFile = new File(
      [JSON.stringify(metadata)],
      `${values.name}-metadata-${uuidv4()}.json`,
      {
        type: "application/json",
      }
    );
    return await uploadFilePinata(metadataFile);
  };

  const createBaseInput = (
    values: FormData,
    tokenUri: string,
    collectionImageUrl: string
  ): CreateCollectionInput => ({
    chain: values.chain,
    chainId: values.chainId,
    name: values.name,
    description: values.description,
    artType: values.artType as ArtType,
    uri: tokenUri,
    image: collectionImageUrl,
    tokenStandard: values.tokenStandard as TokenStandard,
    mintPrice: String(values.mintPrice),
    royaltyFee: String(values.royaltyFee),
    maxSupply: String(values.maxSupply),
    mintLimit: String(values.mintLimit),
    mintStartDate: String(values.mintStartDate),
    currency: chain?.nativeCurrency.symbol as Currency,
    allowlistStages: allowlistStages.map((stage) => ({
      mintPrice: stage.mintPrice,
      startDate: stage.startDate,
      durationDays: stage.durationDays,
      durationHours: stage.durationHours,
      wallets: stage.wallets,
    })),
    publicMint: {
      mintPrice: values.publicMint.mintPrice,
      startDate: values.publicMint.startDate,
      durationDays: values.publicMint.durationDays,
      durationHours: values.publicMint.durationHours,
    },
  });

  const validateAllowlistStages = (stages: AllowlistStageInput[]) => {
    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];
      const startTime = new Date(stage.startDate).getTime() / 1000;
      const duration =
        parseInt(stage.durationDays || "0") * 86400 +
        parseInt(stage.durationHours || "0") * 3600;

      for (let j = i + 1; j < stages.length; j++) {
        const other = stages[j];
        const otherStartTime = new Date(other.startDate).getTime() / 1000;
        const otherDuration =
          parseInt(other.durationDays || "0") * 86400 +
          parseInt(other.durationHours || "0") * 3600;

        if (
          startTime < otherStartTime + otherDuration &&
          startTime + duration > otherStartTime
        ) {
          return `Time overlap between stage ${i + 1} and stage ${j + 1}`;
        }
      }
    }
    return null;
  };

  const onSubmit = async (values: FormData) => {
    if (!isConnected) {
      try {
        await connectAsync({ connector: injected() });
        return;
      } catch (error) {
        toast.error("Failed to connect wallet", {
          description: (error as Error).message,
        });
        return;
      }
    }

    const overlapError = validateAllowlistStages(allowlistStages);
    if (overlapError) {
      toast.error("Validation error", { description: overlapError });
      return;
    }

    const result = formSchema.safeParse(values);
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        toast.error("Validation error", {
          description: `${issue.path.join(".")}: ${issue.message}`,
        });
      });
      return;
    }

    if (chain?.name !== selectedChain) {
      const chainId = getSupportedChains().find(
        (c) => c.name === selectedChain
      )?.id;
      if (chainId) {
        await switchChainAsync({ chainId }).catch((error) => {
          toast.error("Failed to switch chain", { description: error.message });
        });
        return;
      }
    }

    if (
      !collectionImageFile ||
      (selectedArtType === ArtType.Same && !artworkFile) ||
      (selectedArtType === ArtType.Unique && !metadataUrl.trim())
    ) {
      toast.error("Missing required files or metadata URL");
      return;
    }

    setIsLoading(true);
    setIsModalOpen(true);
    setStep1Status("processing");

    try {
      await client.cache.reset();
      const collectionImageFileName = collectionImageFile.name;
      const collectionImageExtension = collectionImageFileName.split(".").pop();
      const collectionImageBaseName = collectionImageFileName.slice(
        0,
        -(collectionImageExtension?.length || 0) - 1
      );
      const collectionImageUrl = await uploadFilePinata(
        new File(
          [collectionImageFile],
          `${collectionImageBaseName}-${uuidv4()}.${collectionImageExtension}`,
          { type: collectionImageFile.type }
        )
      );
      const tokenUri =
        selectedArtType === ArtType.Same
          ? await uploadMetadata(values, artworkFile!)
          : metadataUrl;

      const baseInput = createBaseInput(values, tokenUri, collectionImageUrl);
      console.log(
        "Mutation 1 - Input:",
        JSON.stringify(
          baseInput,
          (key, value) =>
            typeof value === "bigint" ? value.toString() : value,
          2
        )
      );

      const { data: createCollectionData } = await createCollectionMutation({
        variables: { input: baseInput },
      });

      if (!createCollectionData?.createCollection) {
        throw new Error("Failed to create collection");
      }

      const { steps, contractAddress, collectionId } =
        createCollectionData.createCollection as CreateCollectionResponse;

      if (steps?.length && walletClient) {
        const provider = new BrowserProvider(walletClient.transport);
        const signer = await provider.getSigner();
        let contractAddressFromDeploy: string | undefined;
        let implementationAddress: string | undefined;
        let lastTxHash: `0x${string}` | undefined;

        for (const step of steps) {
          console.log(`Processing step ${step.id}, params: ${step.params}`);
          let params: any;
          try {
            params = safeParseJSON(step.params, `step ${step.id}`);
            if (!params.from || !ethers.isAddress(params.from)) {
              throw new Error(
                `Invalid from address in step ${step.id}: ${params.from}`
              );
            }
          } catch (error) {
            throw new Error(
              `Invalid JSON in step ${step.id}: ${(error as Error).message}`
            );
          }

          if (step.id === "deploy_implementation") {
            if (!params.data || !ethers.isHexString(params.data)) {
              throw new Error(`Invalid transaction data for step ${step.id}`);
            }
            console.log("Deploying implementation...");
            const txParams = {
              data: params.data as `0x${string}`,
              value: params.value || "0",
              gasPrice: BigInt(10_000_000_000),
            };
            console.log(
              "txParams:",
              JSON.stringify(
                txParams,
                (k, v) => (typeof v === "bigint" ? v.toString() : v),
                2
              )
            );
            const gasLimit = await estimateGasWithBuffer(signer, txParams);
            console.log(`Gas limit for ${step.id}: ${gasLimit.toString()}`);
            const txHash = await withRetry(
              () =>
                walletClient.sendTransaction({
                  data: txParams.data,
                  value: txParams.value,
                  gasPrice: txParams.gasPrice,
                  gas: gasLimit,
                }),
              3,
              5000
            );
            const transaction = await provider.getTransaction(txHash);
            if (!transaction) {
              throw new Error(`Transaction not found: ${txHash}`);
            }
            const receipt = await transaction.wait();
            if (!receipt || receipt.status !== 1) {
              throw new Error(`Implementation deployment failed: ${txHash}`);
            }
            implementationAddress = receipt.contractAddress!;
            lastTxHash = txHash;
            console.log("Implementation address:", implementationAddress);
            console.log("Implementation tx hash:", txHash);

            const bytecode = await waitForBytecode(
              provider,
              implementationAddress,
              lastTxHash,
              10,
              5000
            );
            if (bytecode) {
              console.log(
                `Implementation bytecode: ${bytecode.slice(0, 20)}...`
              );
            } else {
              console.warn(
                `No valid bytecode for implementation at ${implementationAddress}`
              );
            }
            toast.info("Implementation deployed", {
              description: `Address: ${implementationAddress} | Tx: ${txHash}`,
            });
          } else if (step.id === "deploy_proxy") {
            if (!implementationAddress) {
              throw new Error("Implementation not deployed yet");
            }
            if (
              !params.initializeData ||
              !ethers.isHexString(params.initializeData)
            ) {
              throw new Error(`Invalid initializeData for step ${step.id}`);
            }
            console.log("Deploying proxy...");
            console.log(`initializeData: ${params.initializeData}`);
            const iface = new ethers.Interface(NFTManager.abi);
            try {
              const decoded = iface.decodeFunctionData(
                "initialize",
                params.initializeData
              );
              console.log(
                "Decoded initializeData:",
                JSON.stringify(
                  decoded,
                  (k, v) => (typeof v === "bigint" ? v.toString() : v),
                  2
                )
              );
            } catch (error) {
              throw new Error(
                `Failed to decode initializeData: ${(error as Error).message}`
              );
            }

            const ERC1967ProxyFactory = new ethers.ContractFactory(
              ERC1967Proxy.abi,
              ERC1967Proxy.bytecode,
              signer
            );
            const deployData = ERC1967ProxyFactory.interface.encodeDeploy([
              implementationAddress,
              params.initializeData,
            ]);
            let gasLimit = params.gasLimit
              ? BigInt(params.gasLimit)
              : BigInt(0);
            if (gasLimit < BigInt(500_000)) {
              gasLimit = await estimateGasWithBuffer(signer, {
                data: deployData,
              });
            }
            console.log(`Gas limit for deploy_proxy: ${gasLimit.toString()}`);

            const proxy = await withRetry(
              () =>
                ERC1967ProxyFactory.deploy(
                  implementationAddress,
                  params.initializeData,
                  {
                    gasLimit,
                    gasPrice: BigInt(10_000_000_000),
                  }
                ),
              3,
              5000
            );
            await proxy.waitForDeployment();
            contractAddressFromDeploy = await proxy.getAddress();
            const deployTx = proxy.deploymentTransaction();
            if (!deployTx) {
              throw new Error("No deployment transaction found");
            }
            lastTxHash = deployTx.hash as `0x${string}`;
            console.log("Proxy address:", contractAddressFromDeploy);
            console.log("Proxy tx hash:", lastTxHash);

            // Cập nhật trạng thái
            setDeployedContractAddress(contractAddressFromDeploy);
            setStep1Status("completed");
            toast.info("Contract deployed", {
              description: `Address: ${contractAddressFromDeploy} | Tx: ${lastTxHash}`,
            });

            // Kiểm tra proxy
            await verifyProxyContract(
              provider,
              contractAddressFromDeploy,
              signer,
              lastTxHash
            );
          }
        }

        if (contractAddressFromDeploy && lastTxHash) {
          console.log(
            `Bắt đầu Mutation 2 cho hợp đồng ${contractAddressFromDeploy}`
          );
          setStep2Status("processing");
          const inputWithContract: CreateCollectionInput = {
            ...baseInput,
            contractAddress: contractAddressFromDeploy,
            txHash: lastTxHash,
          };
          console.log(
            "Mutation 2 - Input:",
            JSON.stringify(
              inputWithContract,
              (key, value) =>
                typeof value === "bigint" ? value.toString() : value,
              2
            )
          );

          // Xác minh proxy ở chế độ nền, không chặn
          verifyProxyContract(
            provider,
            contractAddressFromDeploy,
            signer,
            lastTxHash
          ).then((verified) => {
            if (verified) {
              toast.info("Xác minh hợp đồng proxy thành công");
            } else {
              toast.warning(
                "Xác minh proxy thất bại, nhưng vẫn tiếp tục Mutation 2"
              );
            }
          });

          const mutation2 = async () => {
            try {
              console.log("Thực thi Mutation 2...");
              const { data } = await createCollectionMutation({
                variables: { input: inputWithContract },
              });
              console.log(
                "Phản hồi Mutation 2:",
                JSON.stringify(data, null, 2)
              );
              return data;
            } catch (error) {
              console.error("Mutation 2 thất bại:", error);
              throw new Error(
                `Mutation 2 thất bại: ${(error as Error).message}`
              );
            }
          };

          const saveData = await withRetry(mutation2, 5, 15000);
          if (!saveData?.createCollection) {
            throw new Error("Failed to save collection data");
          }
          const response =
            saveData.createCollection as CreateCollectionResponse;

          console.log("Mutation 2 đã xử lý, trạng thái:", response.status);
          if (response.status === "pending") {
            setStep2Status("processing");
            toast.info("Bộ sưu tập đang được xử lý", {
              description: `Địa chỉ hợp đồng: ${contractAddressFromDeploy}. Đang chờ xác nhận...`,
            });
          } else if (response.collectionId) {
            setStep2Status("completed");
            setIsModalOpen(false);
            toast.success("Bộ sưu tập đã được tạo", {
              description: `ID Bộ sưu tập: ${response.collectionId}, Địa chỉ hợp đồng: ${contractAddressFromDeploy}`,
            });
            handleClearForm();
          } else {
            throw new Error("Phản hồi không mong muốn từ Mutation 2");
          }
        }
      } else if (contractAddress && collectionId) {
        setStep1Status("completed");
        setStep2Status("completed");
        setIsModalOpen(false);
        toast.success("Collection created", {
          description: `Contract Address: ${contractAddress}`,
        });
        handleClearForm();
      } else if (collectionId) {
        setStep1Status("completed");
        setStep2Status("completed");
        setIsModalOpen(false);
        toast.info("Collection submitted for approval", {
          description: `Collection ID: ${collectionId}`,
        });
        handleClearForm();
      }
    } catch (error) {
      console.error("Error in onSubmit:", error);
      const errorMessage = (error as Error).message || "Unknown error";
      let description = errorMessage;
      if (errorMessage.includes("Invalid JSON")) {
        description =
          "Invalid data received from server. Please try again or contact support.";
      } else if (errorMessage.includes("deploy")) {
        description = "Contract deployment failed. Check gas or network.";
      } else if (errorMessage.includes("reverted")) {
        description = "Transaction reverted. Check contract logic or inputs.";
      } else if (errorMessage.includes("network")) {
        description = "Network error. Please check your connection.";
      } else if (errorMessage.includes("Mutation 2 failed")) {
        description = "Failed to save collection. Check backend service.";
      } else if (errorMessage.includes("initializeData")) {
        description = "Failed to initialize contract. Check input parameters.";
      }
      toast.error("Failed to create collection", { description });
      setStep1Status("pending");
      setStep2Status("pending");
      setIsLoading(false);
      setIsModalOpen(false);
    }
  };

  return {
    form,
    isLoading,
    allowlistStages,
    selectedChain,
    publicMint: form.watch("publicMint"),
    selectedArtType,
    collectionImageFile,
    artworkFile,
    metadataUrl,
    isFormValid,
    setAllowlistStages,
    setSelectedChain,
    setPublicMint: (mint: PublicMintInput) => form.setValue("publicMint", mint),
    setSelectedArtType,
    setCollectionImageFile,
    setArtworkFile,
    setMetadataUrl,
    getButtonText,
    handleClearForm,
    onSubmit,
    isModalOpen,
    step1Status,
    step2Status,
    setIsModalOpen,
  };
}
