import { SlideCollection } from "@/types/collection.type";

export const NavigationDots = ({
  slides,
  currentSlide,
  onDotClick,
}: {
  slides: SlideCollection[];
  currentSlide: number;
  onDotClick: (index: number) => void;
}) => {
  return (
    <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
      {slides.map((_, index) => (
        <button
          key={index}
          className={`w-2 h-2 rounded-full transition-all duration-300 ${
            currentSlide === index
              ? "w-6 bg-primary"
              : "bg-gray-400/50 hover:bg-gray-400/80 dark:bg-white/50 dark:hover:bg-white/80"
          }`}
          onClick={() => onDotClick(index)}
          aria-label={`Go to slide ${index + 1}`}
        />
      ))}
    </div>
  );
};
