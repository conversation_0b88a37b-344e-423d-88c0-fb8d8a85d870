"use client";

import { motion } from "framer-motion";

export function HomeBannerSkeleton() {
  return (
    <div className="relative w-full h-[500px] rounded-xl overflow-hidden mb-12 group bg-gray-50 dark:bg-[#1A1F2C] border border-gray-200 dark:border-white/10">
      {/* Background skeleton */}
      <div className="absolute inset-0 z-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-700 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-white/90 via-white/70 to-transparent dark:from-[#121620]/90 dark:via-[#121620]/70 dark:to-transparent"></div>
      </div>

      {/* Animated particles */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <motion.div
          className="absolute w-64 h-64 rounded-full blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, 20, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
          style={{
            top: "-20px",
            left: "-20px",
            background: "linear-gradient(45deg, #00dfd8, #7928ca)",
          }}
        />
        <motion.div
          className="absolute w-96 h-96 rounded-full blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, -30, 0],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: 0.5,
          }}
          style={{
            bottom: "-40px",
            right: "-20px",
            background: "linear-gradient(45deg, #7928ca, #00dfd8)",
          }}
        />
      </div>

      {/* Main Content Skeleton */}
      <div className="relative z-20 flex flex-col justify-center h-full p-8 md:p-12 max-w-2xl">
        {/* Title skeleton */}
        <div className="h-16 md:h-24 bg-gray-300 dark:bg-gray-700 rounded-lg w-3/4 mb-6 animate-pulse"></div>

        {/* Description skeleton */}
        <div className="h-20 bg-gray-200 dark:bg-gray-800 rounded-lg w-full max-w-lg mb-8 animate-pulse"></div>

        {/* Buttons skeleton */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="h-12 w-48 bg-gray-300 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          <div className="h-12 w-48 bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"></div>
        </div>

        {/* Stats skeleton */}
        <div className="flex gap-8 mt-12">
          {[0, 1, 2].map((index) => (
            <div key={index}>
              <div className="h-8 w-20 bg-gray-300 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
              <div className="h-6 w-16 bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"></div>
            </div>
          ))}
        </div>

        {/* Navigation dots skeleton */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className={`h-2 rounded-full bg-gray-300 dark:bg-gray-700 animate-pulse ${
                index === 0 ? "w-6" : "w-2"
              }`}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}
