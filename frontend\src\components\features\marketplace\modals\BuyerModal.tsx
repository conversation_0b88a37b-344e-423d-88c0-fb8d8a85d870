"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import Image from "next/image";
import { ChevronDown, Maximize, RefreshCw, Share, X } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import type { Nft } from "@/lib/api/graphql/generated";

// Fake activity data
const activities = [
  {
    type: "List",
    seller: "6jAnot",
    buyer: "--",
    price: "0.219",
    time: "4d8h",
  },
  { type: "List", seller: "6jAnot", buyer: "--", price: "0.227", time: "6h" },
  { type: "List", seller: "6jAnot", buyer: "--", price: "0.246", time: "1d" },
  { type: "List", seller: "6jAnot", buyer: "--", price: "0.249", time: "1d" },
  { type: "List", seller: "6jAnot", buyer: "--", price: "0.25", time: "1d" },
  { type: "List", seller: "6jAnot", buyer: "--", price: "0.258", time: "1d" },
  {
    type: "Sell",
    seller: "8FwRks",
    buyer: "6jAnot",
    price: "0.21",
    time: "1d",
  },
  { type: "Mint", seller: "--", buyer: "--", price: "0.021", time: "1d" },
];

interface BuyerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  nft: Nft;
}

export function BuyerModal({ open, onOpenChange, nft }: BuyerModalProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="[&>button]:hidden h-[80vh] sm:w-[800px] sm:max-w-[800px] p-0 gap-0 overflow-hidden bg-[#121212] text-white border-gray-800 flex flex-col">
          <DialogTitle className="sr-only">NFT Details</DialogTitle>
          <div className="flex items-center justify-between p-4 border-b border-gray-800">
            <h2 className="text-xl font-bold">{nft.name}</h2>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
              >
                <Share className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex items-center px-4 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-gray-400">PAWS VOUCHERS</span>
              <div className="flex items-center">
                <span className="inline-block h-4 w-4 rounded-full bg-purple-500"></span>
                <span className="inline-block h-4 w-4 rounded-full bg-pink-500 -ml-1"></span>
              </div>
            </div>
          </div>

          <Tabs
            defaultValue="overview"
            className="w-full flex-1 overflow-hidden flex flex-col"
          >
            <div className="border-b border-gray-800 px-4">
              <TabsList className="bg-transparent h-10 p-0">
                <TabsTrigger
                  value="overview"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="offers"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Offers{" "}
                  <span className="ml-1 rounded-full bg-gray-800 px-1.5 text-xs">
                    0
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-pink-500 data-[state=active]:bg-transparent h-10 text-gray-400 data-[state=active]:text-white"
                >
                  Activity
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="overview"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-0 h-full p-3">
                <div className="relative aspect-square w-full">
                  <Image
                    src={nft.image || "https://placehold.co/1200x1200"}
                    alt={nft.name || "NFT Image"}
                    width={1800}
                    height={1800}
                    className="object-cover w-full h-full"
                    priority
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 h-8 w-8 bg-gray-900/80 backdrop-blur-sm text-white hover:bg-gray-800"
                    onClick={() => setIsExpanded(true)}
                  >
                    <Maximize className="h-4 w-4" />
                  </Button>
                </div>

                {/* Right side - NFT Details */}
                <div className="p-4 flex flex-col bg-[#121212]">
                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-400">Total Price</div>
                      <div className="text-sm flex items-center">
                        Owned by:{" "}
                        <span className="text-pink-500 ml-1 truncate max-w-[120px]">
                          378n...vysw
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="text-2xl font-bold">
                        {nft.mintPrice} SOL
                      </div>
                      <div className="text-gray-400">$1.24</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto h-8 px-2 text-gray-400 hover:text-white"
                      >
                        Details <ChevronDown className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="relative">
                      <Button
                        variant="outline"
                        className="w-full justify-between border-gray-700 text-white hover:bg-gray-800"
                      >
                        Pay with SOL
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 text-sm">
                        0.009
                      </div>
                    </div>

                    <Button
                      variant="default"
                      className="w-full bg-red-500 hover:bg-red-600 text-white"
                      disabled
                    >
                      Insufficient funds
                    </Button>
                  </div>

                  <div className="mt-2 text-sm text-gray-400">
                    Priority fee (Standard)
                  </div>

                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-400">List Price</div>
                      <div className="flex items-center">
                        <div className="font-medium">{nft.mintPrice} SOL</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Floor Price</div>
                      <div className="flex items-center">
                        <div className="font-medium">0.0071 SOL</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Floor Diff.</div>
                      <div className="flex items-center">
                        <div className="font-medium">19.7%</div>
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Top Offer</div>
                      <div className="flex items-center">
                        <div className="font-medium">-- SOL</div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Button
                      variant="ghost"
                      className="w-full justify-between border-t border-gray-800 pt-2 text-white hover:bg-gray-800"
                    >
                      <span className="font-medium">Traits</span>
                      <span className="flex items-center">
                        1 <ChevronDown className="ml-1 h-4 w-4" />
                      </span>
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="offers"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="flex flex-col items-center justify-center py-16 h-full">
                <h3 className="text-lg font-medium">No offers yet</h3>
                <p className="text-gray-400 mt-2">
                  Be the first to make an offer on this item
                </p>
                <Button className="mt-6 bg-pink-600 hover:bg-pink-700">
                  Make an offer
                </Button>
              </div>
            </TabsContent>

            <TabsContent
              value="activity"
              className="mt-0 p-0 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-gray-600"
            >
              <div className="p-4 h-full">
                <div className="flex justify-end mb-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px] bg-[#1a1a1a] border-gray-800 text-white">
                      <SelectValue placeholder="Filter" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1a1a1a] border-gray-800 text-white">
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="list">List</SelectItem>
                      <SelectItem value="sell">Sell</SelectItem>
                      <SelectItem value="mint">Mint</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-left text-gray-400 text-sm">
                        <th className="pb-2">Type</th>
                        <th className="pb-2">Seller</th>
                        <th className="pb-2">Buyer</th>
                        <th className="pb-2">Price</th>
                        <th className="pb-2">Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {activities.map((activity, index) => (
                        <tr key={index} className="border-t border-gray-800">
                          <td className="py-3">
                            <span
                              className={`px-2 py-1 text-xs rounded ${
                                activity.type === "List"
                                  ? "bg-red-900/30 text-red-500"
                                  : activity.type === "Sell"
                                  ? "bg-red-900/30 text-red-500"
                                  : "bg-green-900/30 text-green-500"
                              }`}
                            >
                              {activity.type}
                            </span>
                          </td>
                          <td className="py-3 text-pink-500 truncate max-w-[100px]">
                            {activity.seller}
                          </td>
                          <td className="py-3 text-pink-500 truncate max-w-[100px]">
                            {activity.buyer}
                          </td>
                          <td className="py-3">
                            <div className="font-medium truncate max-w-[120px]">
                              {activity.price} SOL
                            </div>
                            <div className="text-xs text-gray-400 truncate max-w-[120px]">
                              $
                              {(
                                Number.parseFloat(activity.price) * 238
                              ).toFixed(2)}
                            </div>
                          </td>
                          <td className="py-3">{activity.time}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="border-t border-gray-800 p-4 flex items-center justify-between text-xs text-gray-400">
            <div className="flex items-center gap-4">
              <div>PAWS VOUCHERS</div>
              <div>
                Floor:{" "}
                <span className="text-white font-medium">{"<"} 0.01 SOL</span>{" "}
                0.88%
              </div>
              <div>
                Top Offer:{" "}
                <span className="text-white font-medium">-- SOL</span>
              </div>
              <div>
                24hr Vol:{" "}
                <span className="text-white font-medium">0.29 SOL</span>
              </div>
              <div>
                24hr Sales: <span className="text-white font-medium">27</span>
              </div>
              <div>
                Listed: <span className="text-white font-medium">3034</span>{" "}
                4.46%
              </div>
              <div>
                Owners: <span className="text-white font-medium">37410</span>{" "}
                55.0%
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {isExpanded && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
          <div className="absolute inset-0 bg-black/70">
            <X
              className="absolute top-4 right-4 h-6 w-6"
              onClick={() => setIsExpanded(false)}
            />
          </div>
          <div
            className="relative z-[10000] w-[60vw] h-[60vh]"
            onClick={(e) => e.stopPropagation()}
          >
            <Image
              src={nft.image || "https://placehold.co/600x600"}
              alt={nft.name || "NFT Image Expanded"}
              width={1800}
              height={1800}
              className="object-cover w-full h-full"
              priority
            />
          </div>
        </div>
      )}
    </>
  );
}
