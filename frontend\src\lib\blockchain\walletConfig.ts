import {
  sepolia,
  polygon<PERSON>umbai,
  baseSepolia,
  Chain,
  hardhat,
} from "wagmi/chains";
import { createConfig, http } from "wagmi";
import { metaMask } from "wagmi/connectors"; // <PERSON><PERSON><PERSON> b<PERSON><PERSON> import từ "wagmi/connectors", không phải SDK
import { ethers } from "ethers";

  export const supportedChains: Chain[] = [
  {
    ...sepolia,
    rpcUrls: {
      default: { http: ["https://rpc.sepolia.org"] },
      public: { http: ["https://rpc.sepolia.org"] },
    },
    blockExplorers: {
      default: { name: "Etherscan", url: "https://sepolia.etherscan.io" },
    },
  },
  {
    ...polygonMumbai,
    rpcUrls: {
      default: { http: ["https://rpc-mumbai.maticvigil.com"] },
      public: { http: ["https://rpc-mumbai.maticvigil.com"] },
    },
    blockExplorers: {
      default: { name: "PolygonScan", url: "https://mumbai.polygonscan.com" },
    },
  },
  {
    ...baseSepolia,
    rpcUrls: {
      default: { http: ["https://sepolia.base.org"] },
      public: { http: ["https://sepolia.base.org"] },
    },
    blockExplorers: {
      default: { name: "BaseScan", url: "https://sepolia.basescan.org" },
    },
  },
  {
    ...hardhat,
    rpcUrls: {
      default: { http: ["http://127.0.0.1:8545"] },
      public: { http: ["http://127.0.0.1:8545"] },
    },
    blockExplorers: {
      default: { name: "Etherscan", url: "https://sepolia.etherscan.io" },
    },
  },
];

export const getChainsConfig = () => {
  const rpcUrls = {
    "11155111": process.env.SEPOLIA_RPC_URL || "https://rpc.sepolia.org",
    "84532": process.env.BASE_SEPOLIA_RPC_URL || "https://sepolia.base.org",
    "80001":
      process.env.POLYGON_MUMBAI_RPC_URL || "https://rpc-mumbai.maticvigil.com",
    "31337": process.env.HARDHAT_RPC_URL || "http://127.0.0.1:8545",
  };

  const configs: Record<string, { provider: ethers.JsonRpcProvider }> = {};

  for (const [chainId, rpcUrl] of Object.entries(rpcUrls)) {
    if (!rpcUrl) {
      throw new Error(`RPC URL for chain ${chainId} is not defined`);
    }
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    configs[chainId] = {
      provider,
    };
  }

  return configs;
};

export const chainColors: Record<number, string> = {
  [sepolia.id]: "#CFB5F0",
  [polygonMumbai.id]: "#8247E5",
  [baseSepolia.id]: "#0052FF",
  [hardhat.id]: "#0052FF",
};

export const getChainById = (chainId?: number): Chain | undefined =>
  chainId ? supportedChains.find((chain) => chain.id === chainId) : undefined;

export const getChainName = (chainId: number): string =>
  getChainById(chainId)?.name ?? "Unknown Network";

export const wallets = [
  {
    id: "metaMaskSDK",
    name: "metaMaskSDK",
    status: "Installed",
    color: "#E2761B",
    logo: "🦊",
  },
];

export const config = createConfig({
  chains: [sepolia, polygonMumbai, baseSepolia, hardhat],
  transports: {
    [sepolia.id]: http(
      process.env.NEXT_PUBLIC_SEPOLIA_RPC_URL || "https://rpc.sepolia.org"
    ),
    [polygonMumbai.id]: http(
      process.env.NEXT_PUBLIC_POLYGON_MUMBAI_RPC_URL ||
        "https://rpc-mumbai.maticvigil.com"
    ),
    [baseSepolia.id]: http(
      process.env.NEXT_PUBLIC_BASE_SEPOLIA_RPC_URL || "https://sepolia.base.org"
    ),
    [hardhat.id]: http(
      process.env.NEXT_PUBLIC_HARDHAT_RPC_URL || "http://127.0.0.1:8545"
    ),
  },
  connectors: [metaMask()], // Đảm bảo chỉ dùng metaMask() từ "wagmi/connectors"
  syncConnectedChain: true,
});

export type WalletModalContextType = {
  modalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  pendingVerification: boolean;
  setPendingVerification: (value: boolean) => void;
};
