/* eslint-disable @typescript-eslint/no-unused-vars */
import { fetchCollection } from "@/lib/services/collection";
import MintNFT from "@/components/features/mint";
import { validateChain, validateContractAddress } from "@/lib/utils/validate";

interface MintNFTPageProps {
  params: {
    chain: string;
    contractAddress: string;
  };
}

export async function generateMetadata({ params }: MintNFTPageProps) {
  const { chain: chainName, contractAddress } = await params;

  try {
    const chain = validateChain(chainName);
    const validatedAddress = validateContractAddress(contractAddress);

    const collection = await fetchCollection(
      String(chain?.id),
      validatedAddress
    );
    if (!collection) {
      return {
        title: "Collection Not Found | NFT Marketplace",
        description: "The requested collection does not exist",
      };
    }

    return {
      title: `${collection.name} | ${chain?.name} | NFT Marketplace`,
      description:
        collection.description ||
        `Mint and explore NFTs in ${collection.name} on ${chain?.name}`,
      openGraph: {
        title: `${collection.name} on ${chain?.name}`,
        description: collection.description,
      },
    };
  } catch (error) {
    return {
      title: "Error | NFT Marketplace",
      description: "An error occurred while loading the collection",
    };
  }
}

export default async function Page({ params }: MintNFTPageProps) {
  const { chain: chainName, contractAddress } = await params;
  const chain = validateChain(chainName);
  const validatedAddress = validateContractAddress(contractAddress);
  return (
    <MintNFT chainId={String(chain?.id)} contractAddress={validatedAddress} />
  );
}
