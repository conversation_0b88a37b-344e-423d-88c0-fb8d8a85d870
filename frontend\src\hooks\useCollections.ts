/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import {
  Collection,
  FilterOperator,
  useGetCollectionsQuery,
  useCollectionModifiedPublicRealtimeSubscription,
  ActionType,
} from "@/lib/api/graphql/generated";
import { setData, setError, setLoading } from "@/store/slices/homeSlice";
import { RootState } from "@/store/store";

interface UseCollectionsProps {
  chainId: string | null;
}

export function useCollections({ chainId }: UseCollectionsProps) {
  const dispatch = useDispatch();
  const { collections: currentCollections, stats: currentStats } = useSelector(
    (state: RootState) => state.home
  );

  // Use the generated query hook
  const { data, loading, error } = useGetCollectionsQuery({
    variables: {
      input: {
        filters: chainId
          ? [
              {
                field: "chainId",
                operator: FilterOperator.Eq,
                value: chainId,
              },
            ]
          : [],
        pagination: {
          limit: "10",
          skip: "0",
        },
        includeStats: true,
      },
    },
    fetchPolicy: "no-cache",
  });

  // Update Redux store when data changes
  useEffect(() => {
    if (data?.getCollections) {
      dispatch(
        setData({
          collections: data.getCollections.collections as Collection[],
          stats: data.getCollections.stats || {
            artworks: 0,
            artists: 0,
            collectors: 0,
          },
        })
      );
    }
  }, [data, dispatch]);

  // Handle loading state
  useEffect(() => {
    dispatch(setLoading(loading));
  }, [loading, dispatch]);

  // Handle error state
  useEffect(() => {
    if (error) {
      toast.error("Không thể tải collections", {
        description: error.message,
      });
      dispatch(setError(error.message));
    }
  }, [error, dispatch]);

  useCollectionModifiedPublicRealtimeSubscription({
    variables: {
      input: {
        chainId: chainId || "all",
      },
    },
    onData: ({ data }) => {
      const newCollection = data.data?.collectionModifiedPublicRealtime;
      console.log("newCollection", newCollection);

      switch (newCollection?.action) {
        case ActionType.Create:
          dispatch(
            setData({
              collections: [
                newCollection.data as Collection,
                ...currentCollections,
              ],
              stats: {
                ...currentStats,
                artworks: currentStats.artworks + 1,
              },
            })
          );
          toast.success(`Collection mới: ${newCollection.data.name}`);
          break;
        case ActionType.Update:
          const updatedCollection = newCollection.data as Collection;
          dispatch(
            setData({
              collections: currentCollections.map((collection) =>
                collection.id === updatedCollection.id
                  ? updatedCollection
                  : collection
              ),
              stats: currentStats,
            })
          );
          toast.success(`Collection đã cập nhật: ${updatedCollection.name}`);
          break;
      }
    },
  });
}
