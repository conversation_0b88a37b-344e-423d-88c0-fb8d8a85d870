"use client";

import { useState } from "react";
import { useAccount, useWalletClient } from "wagmi";
import { BrowserProvider, ethers } from "ethers";
import { toast } from "sonner";
import { ERC1967Proxy } from "@/lib/abi/ERC1967Proxy";
import { NFTManager } from "@/lib/abi/NFTManager";

interface DeploymentState {
  isDeploying: boolean;
  deployedAddress: string | null;
  deploymentStep: "idle" | "deploying" | "verifying" | "completed" | "failed";
}

interface DeploymentActions {
  deployContract: (initializeData: string) => Promise<string>;
  resetDeployment: () => void;
}

// Utility functions
function safeParseJSON(data: string, context: string): any {
  try {
    if (!data || typeof data !== "string") {
      throw new Error(`Invalid JSON string: ${data}`);
    }
    return JSON.parse(data, (key, value) =>
      typeof value === "string" && /^[0-9]+$/.test(value)
        ? BigInt(value)
        : value
    );
  } catch (error) {
    console.error(`Failed to parse JSON in ${context}:`, error);
    throw new Error(`Invalid JSON in ${context}: ${(error as Error).message}`);
  }
}

async function withRetry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 5000
): Promise<T> {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      console.warn(
        `Retry ${i + 1}/${retries} after ${delay}ms: ${
          (error as Error).message
        }`
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
  throw new Error("Retry limit reached");
}

async function estimateGasWithBuffer(signer: any, tx: any): Promise<bigint> {
  try {
    const estimatedGas = await signer.estimateGas(tx);
    const bufferedGas = (estimatedGas * BigInt(150)) / BigInt(100); // +50% buffer
    const maxGasLimit = BigInt(15_000_000);
    const minGasLimit = BigInt(500_000);
    
    console.log(
      `Estimated gas: ${estimatedGas.toString()}, Buffered gas: ${bufferedGas.toString()}`
    );
    
    return bufferedGas < minGasLimit
      ? minGasLimit
      : bufferedGas > maxGasLimit
      ? maxGasLimit
      : bufferedGas;
  } catch (error) {
    console.error(`Gas estimation failed: ${(error as Error).message}`);
    const fallbackGas = tx.data.includes("deploy_implementation")
      ? BigInt(8_000_000)
      : BigInt(1_000_000);
    console.warn(`Using fallback gas limit: ${fallbackGas.toString()}`);
    return fallbackGas;
  }
}

async function waitForBytecode(
  provider: BrowserProvider,
  address: string,
  txHash: string,
  maxAttempts = 15,
  delay = 10000
): Promise<string | null> {
  console.log(`Checking transaction ${txHash} for address ${address}`);
  
  try {
    const transaction = await provider.getTransaction(txHash);
    if (!transaction) {
      console.error(`Transaction not found: ${txHash}`);
      return null;
    }
    
    const receipt = await transaction.wait(2);
    if (!receipt || receipt.status !== 1) {
      console.error(
        `Transaction failed: status=${receipt?.status}, contractAddress=${receipt?.contractAddress}`
      );
      return null;
    }
    
    console.log(`Valid receipt: contractAddress=${receipt.contractAddress}`);
  } catch (error) {
    console.error(`Transaction verification failed for ${txHash}: ${error}`);
    return null;
  }

  for (let i = 0; i < maxAttempts; i++) {
    try {
      console.log(`Attempting to get bytecode at ${address}, attempt ${i + 1}/${maxAttempts}`);
      
      const bytecode = await Promise.race([
        provider.getCode(address),
        new Promise(
          (_, reject) =>
            setTimeout(() => reject(new Error("RPC Timeout")), 20000)
        ),
      ]);

      console.log(`Bytecode at ${address}: ${(bytecode as string).slice(0, 20)}...`);
      
      if (bytecode !== "0x") return bytecode as string;
      
      console.log(`Bytecode is 0x, retrying after ${delay}ms...`);
    } catch (error) {
      console.warn(`Failed to get bytecode: ${(error as Error).message}`);
    }
    
    await new Promise((resolve) => setTimeout(resolve, delay));
  }
  
  console.error(`No bytecode at ${address} after ${maxAttempts} attempts`);
  return null;
}

async function verifyProxyContract(
  provider: BrowserProvider,
  address: string,
  signer: any,
  txHash: string
): Promise<boolean> {
  console.log(`Starting proxy verification for ${address}`);
  
  try {
    const bytecode = await waitForBytecode(provider, address, txHash);
    if (!bytecode) {
      console.warn(`No valid bytecode found at ${address}. Skipping name check.`);
      return false;
    }
    
    console.log(`Proxy bytecode: ${bytecode.slice(0, 20)}...`);

    const nftManager = new ethers.Contract(address, NFTManager.abi, signer);
    const name = await withRetry(() => nftManager.name(), 3, 4000);
    console.log(`Name via proxy: ${name}`);
    return true;
  } catch (error) {
    console.error(`Proxy verification failed: ${(error as Error).message}`);
    return false;
  } finally {
    console.log(`Completed proxy verification for ${address}`);
  }
}

export function useContractDeployment(): DeploymentState & DeploymentActions {
  const { address } = useAccount();
  const { data: walletClient } = useWalletClient();
  
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployedAddress, setDeployedAddress] = useState<string | null>(null);
  const [deploymentStep, setDeploymentStep] = useState<DeploymentState["deploymentStep"]>("idle");

  const deployContract = async (initializeData: string): Promise<string> => {
    if (!walletClient || !address) {
      throw new Error("Wallet not connected");
    }

    setIsDeploying(true);
    setDeploymentStep("deploying");

    try {
      const provider = new BrowserProvider(walletClient.transport);
      const signer = await provider.getSigner();

      // Parse initialize data
      const parsedData = safeParseJSON(initializeData, "initializeData");
      const implementationAddress = parsedData.implementationAddress;
      const initData = parsedData.initData;

      if (!implementationAddress || !initData) {
        throw new Error("Invalid initialize data: missing implementationAddress or initData");
      }

      // Create proxy contract
      const proxyFactory = new ethers.ContractFactory(
        ERC1967Proxy.abi,
        ERC1967Proxy.bytecode,
        signer
      );

      const deployTx = {
        data: proxyFactory.getDeployTransaction(implementationAddress, initData).data,
      };

      const gasLimit = await estimateGasWithBuffer(signer, deployTx);
      
      console.log(`Deploying proxy with gas limit: ${gasLimit.toString()}`);
      
      const tx = await signer.sendTransaction({
        ...deployTx,
        gasLimit,
      });

      console.log(`Proxy deployment transaction sent: ${tx.hash}`);
      
      const receipt = await tx.wait(2);
      if (!receipt || receipt.status !== 1) {
        throw new Error(`Proxy deployment failed: ${receipt?.status}`);
      }

      const contractAddress = receipt.contractAddress;
      if (!contractAddress) {
        throw new Error("No contract address in receipt");
      }

      console.log(`Proxy deployed at: ${contractAddress}`);
      setDeployedAddress(contractAddress);
      setDeploymentStep("verifying");

      // Verify proxy contract
      const isVerified = await verifyProxyContract(provider, contractAddress, signer, tx.hash);
      
      if (isVerified) {
        setDeploymentStep("completed");
        toast.success("Contract deployed successfully", {
          description: `Contract address: ${contractAddress}`,
        });
      } else {
        setDeploymentStep("completed");
        toast.warning("Contract deployed but verification failed", {
          description: `Contract address: ${contractAddress}`,
        });
      }

      return contractAddress;
    } catch (error) {
      setDeploymentStep("failed");
      console.error("Contract deployment failed:", error);
      throw error;
    } finally {
      setIsDeploying(false);
    }
  };

  const resetDeployment = () => {
    setIsDeploying(false);
    setDeployedAddress(null);
    setDeploymentStep("idle");
  };

  return {
    // State
    isDeploying,
    deployedAddress,
    deploymentStep,
    
    // Actions
    deployContract,
    resetDeployment,
  };
}
