"use client";

import { useState, useEffect } from "react";
import { useAccount, useConnect } from "wagmi";
import { metaMask } from "wagmi/connectors";
import { Loader2, AlertTriangle, Wallet, Sparkles, Package } from "lucide-react";
import { Collection } from "@/lib/api/graphql/generated";
import { ButtonConfig, ValidationState, MintCostData, MintCost, NftMetadataInput } from "@/types/mint.types";

interface UseMintButtonProps {
  collection?: Collection | null;
  validations: ValidationState;
  agreedToTerms: boolean;
  mintCostData?: MintCostData;
  lastMintCost: MintCost;
  isLoadingMintCost: boolean;
  isAllowlistMint: boolean;
  isSameArtType: boolean;
  batchMetadata: NftMetadataInput[];
  amount: number;
  onConfirm: (isBatch: boolean) => void;
}

export function useMintButton({
  collection,
  validations,
  agreedToTerms,
  mintCostData,
  lastMintCost,
  isLoadingMintCost,
  isAllowlistMint,
  isSameArtType,
  batchMetadata,
  amount,
  onConfirm,
}: UseMintButtonProps) {
  const { isConnected } = useAccount();
  const { connect } = useConnect();
  const [isEstimating, setIsEstimating] = useState(false);
  const [currentAmount, setCurrentAmount] = useState(amount);

  useEffect(() => {
    const newAmount = batchMetadata.length
      ? batchMetadata.reduce((sum, meta) => sum + Number(meta.amount), 0)
      : amount;

    if (newAmount !== currentAmount) {
      setCurrentAmount(newAmount);
    }
  }, [amount, batchMetadata, currentAmount]);

  const getButtonConfig = (): ButtonConfig[] => {
    if (!collection) {
      return [
        {
          text: "Loading...",
          disabled: true,
          variant: "secondary",
          icon: <Loader2 className="mr-2 h-4 w-4 animate-spin" />,
        },
      ];
    }

    if (!isConnected) {
      return [
        {
          text: "Kết nối ví",
          disabled: false,
          variant: "default",
          icon: <Wallet className="mr-2 h-4 w-4" />,
          onClick: () => connect({ connector: metaMask() }),
        },
      ];
    }

    if (Number(collection.totalMinted) >= Number(collection.maxSupply)) {
      return [
        {
          text: "Đã bán hết",
          disabled: true,
          variant: "destructive",
          icon: <AlertTriangle className="mr-2 h-4 w-4" />,
        },
      ];
    }

    if (!mintCostData?.getMintCost?.success) {
      return [
        {
          text: `Mint ${currentAmount} NFT${
            currentAmount > 1 ? "s" : ""
          } (Gas: ${Number(lastMintCost.estimatedGas).toFixed(6)} ETH)`,
          disabled: true,
          variant: "secondary",
        },
      ];
    }

    if (!validations.isValidImage) {
      return [
        {
          text: isSameArtType
            ? "Collection image not available"
            : "Upload a valid image or use batch upload",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!validations.isValidAttributes) {
      return [
        {
          text: "Complete required attribute fields",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!validations.isValidAmount) {
      return [
        {
          text: `Invalid amount (max ${Math.min(
            collection?.mintLimit ? Number(collection.mintLimit) : 100,
            Number(collection?.maxSupply) - Number(collection?.totalMinted)
          )}, batch must match amount)`,
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (
      isAllowlistMint &&
      (!validations.isValidSignature || !validations.isValidNonce)
    ) {
      return [
        {
          text: "Provide valid signature and nonce",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!agreedToTerms) {
      return [
        {
          text: "Accept terms of service",
          disabled: true,
          variant: "secondary",
        },
      ];
    }

    if (
      !validations.hasSufficientBalance &&
      mintCostData?.getMintCost?.success
    ) {
      return [
        {
          text: `Insufficient balance (${currentAmount} NFT${
            currentAmount > 1 ? "s" : ""
          } - Gas: ${Number(mintCostData.getMintCost.estimatedGas).toFixed(
            6
          )} ETH)`,
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (isAllowlistMint && batchMetadata.length > 0) {
      return [
        {
          text: "Batch mint not supported for allowlist",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    const buttons: ButtonConfig[] = [
      {
        text: `Mint ${currentAmount} NFT${currentAmount > 1 ? "s" : ""} (Gas: ${
          mintCostData?.getMintCost?.estimatedGas
            ? Number(mintCostData.getMintCost.estimatedGas).toFixed(6)
            : Number(lastMintCost.estimatedGas).toFixed(6)
        } ETH)`,
        disabled: isEstimating || isLoadingMintCost,
        variant: "default",
        icon: <Sparkles className="mr-2 h-4 w-4" />,
        onClick: () => {
          setIsEstimating(true);
          onConfirm(false);
          setIsEstimating(false);
        },
      },
    ];

    if (
      !isSameArtType &&
      batchMetadata.length > 0 &&
      validations.isValidBatch
    ) {
      buttons.push({
        text: `Batch Mint ${currentAmount} NFT${
          currentAmount > 1 ? "s" : ""
        } (Gas: ${
          mintCostData?.getMintCost?.estimatedGas
            ? Number(mintCostData.getMintCost.estimatedGas).toFixed(6)
            : Number(lastMintCost.estimatedGas).toFixed(6)
        } ETH)`,
        disabled: isEstimating || isLoadingMintCost,
        variant: "default",
        icon: <Package className="mr-2 h-4 w-4" />,
        onClick: () => {
          setIsEstimating(true);
          onConfirm(true);
          setIsEstimating(false);
        },
      });
    }

    return buttons;
  };

  return {
    buttonConfigs: getButtonConfig(),
    isEstimating,
    currentAmount,
  };
}
