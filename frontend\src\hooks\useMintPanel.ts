import { useState, useEffect } from "react";
import { useAccount } from "wagmi";
import { useMintPanelData } from "./useMintPanelData";
import { useMintForm } from "./useMintForm";
import { ArtType } from "@/lib/api/graphql/generated";
import {
  FormState,
  FormActions,
  FormErrors,
  MintCostData,
  MintCost,
} from "@/types/mint.types";

export function useMintPanel() {
  const { address } = useAccount();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isBatchMint, setIsBatchMint] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("mint");

  const {
    collection: collectionData,
    isAllowlistMint,
    nftsData,
    refetchNfts,
    mintCostData,
    lastMintCost,
    isLoadingMintCost,
    currentAmount,
    setCurrentAmount,
  } = useMintPanelData();

  const {
    formState,
    formActions,
    formErrors,
  }: {
    formState: FormState;
    formActions: FormActions;
    formErrors: FormErrors;
  } = useMintForm({
    collection: collectionData,
    isSameArtType: collectionData?.artType === ArtType.Same,
    isAllowlistMint,
    onMintSuccess: () => {
      formActions.resetForm();
      refetchNfts();
    },
  });

  const isSameArtType = collectionData?.artType === ArtType.Same;

  useEffect(() => {
    const newAmount =
      formState.batchMetadata.length > 0
        ? formState.batchMetadata.reduce(
            (sum, meta) => sum + Number(meta.amount),
            0
          )
        : formState.amount;
    if (newAmount !== currentAmount) {
      setCurrentAmount(newAmount);
    }
  }, [
    formState.amount,
    formState.batchMetadata,
    currentAmount,
    setCurrentAmount,
  ]);

  const handleMintConfirm = async (isBatch: boolean) => {
    setIsBatchMint(isBatch);
    setShowConfirmModal(true);
  };

  return {
    // State
    address,
    showConfirmModal,
    setShowConfirmModal,
    isBatchMint,
    activeTab,
    setActiveTab,
    collectionData,
    isAllowlistMint,
    nftsData,
    mintCostData: mintCostData as MintCostData,
    lastMintCost: lastMintCost as MintCost,
    isLoadingMintCost,
    currentAmount,
    formState,
    formActions,
    formErrors,
    isSameArtType,

    // Actions
    handleMintConfirm,
    refetchNfts,
  };
}
