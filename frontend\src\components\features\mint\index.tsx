"use client";
import CollectionIntro from "@/components/features/mint/CollectionIntro";
import { CollectionMediaShowcase } from "@/components/features/mint/CollectionMediaShowcase";
import { MintNFTError } from "@/components/features/mint/MintNFTError";
import { MintNFTSkeleton } from "@/components/features/mint/MintNFTSkeleton";
import MintPanel from "@/components/features/mint/MintPanel";
import { useDetailCollection } from "@/hooks/useDetailCollection";
import ExploreButton from "@/components/features/mint/ExploreButton";
import { useState } from "react";
import { useTheme } from "next-themes";

interface MintNFTProps {
  chainId: string;
  contractAddress: string;
}

export default function MintNFT({ chainId, contractAddress }: MintNFTProps) {
  const { collection, loading, error } = useDetailCollection(
    chainId,
    contractAddress
  );
  const [currentImage, setCurrentImage] = useState<string>("");
  const { theme } = useTheme();

  if (loading) return <MintNFTSkeleton />;
  if (error || !collection) {
    return (
      <MintNFTError
        error={error || "Collection not found"}
        onRetry={() => {
          window.location.reload();
        }}
      />
    );
  }

  return (
    <>
      <div className="relative">
        {currentImage && (
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url(${currentImage})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              filter: `blur(50px) brightness(${
                theme === "dark" ? "0.3" : "0.5"
              })`,
              opacity: theme === "dark" ? "0.7" : "0.5",
            }}
          />
        )}
        <div className="flex xl:flex-row items-center justify-center gap-10 flex-col mb-20">
          <CollectionMediaShowcase onImageChange={setCurrentImage} />
          <div className="flex flex-col gap-5 z-1">
            <MintPanel currentGalleryImage={currentImage} />
            <ExploreButton collection={collection} />
          </div>
        </div>
      </div>
      <CollectionIntro />
    </>
  );
}
