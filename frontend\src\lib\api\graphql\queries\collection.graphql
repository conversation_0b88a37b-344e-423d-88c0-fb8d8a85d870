mutation CreateCollection($input: CreateCollectionInput!) {
  createCollection(input: $input) {
    collectionId
    contractAddress
    steps {
      id
      params
    }
    status
  }
}

subscription CollectionModifiedPublicRealtime(
  $input: CollectionModifiedPublicInput!
) {
  collectionModifiedPublicRealtime(input: $input) {
    data {
      id
      name
      chainId
      image
      mintPrice
      maxSupply
      chain
      totalMinted
      isVerified
      status
      description
      mintStartDate
      contractAddress
      royaltyFee
      mintLimit
      uri
      tokenStandard
      currency
      publicMint {
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
      }
      allowlistStages {
        stageId
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
        wallets
      }
    }
    action
  }
}

subscription CollectionModifiedPrivateRealtime(
  $input: CollectionModifiedPrivateInput!
) {
  collectionModifiedPrivateRealtime(input: $input) {
    data {
      id
      name
      image
    }
    action
  }
}

query GetCollections($input: GetCollectionsInput!) {
  getCollections(input: $input) {
    stats {
      artworks
      artists
      collectors
    }
    collections {
      id
      name
      chainId
      image
      mintPrice
      maxSupply
      chain
      totalMinted
      isVerified
      status
      description
      contractAddress
      mintStartDate
      royaltyFee
      mintLimit
      uri
      tokenStandard
      currency
      publicMint {
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
      }
      allowlistStages {
        stageId
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
        wallets
      }
    }
    pagination {
      total
      limit
      skip
      cursor
      hasMore
    }
  }
}

query GetCollection($input: GetCollectionsInput!) {
  getCollection(input: $input) {
    artType
    chain
    chainId
    contractAddress
    createdAt
    creatorId
    creatorRole
    description
    id
    mintStartDate
    image
    isVerified
    maxSupply
    mintLimit
    mintPrice
    name
    previewImages
    royaltyFee
    status
    tokenStandard
    totalMinted
    uri
    currency
    publicMint {
      mintPrice
      startDate
      endDate
      durationDays
      durationHours
    }
    allowlistStages {
      stageId
      mintPrice
      startDate
      endDate
      durationDays
      durationHours
      wallets
    }
  }
}

mutation ModifyCollection($input: ModifyCollectionInput!) {
  modifyCollection(input: $input)
}
