"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Chain, getSupportedChains } from "@/lib/constant/chains";
import { useHomeData } from "@/hooks/useHomeData";
import CarouselCollection from "@/components/features/home/<USER>";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import { setChain } from "@/store/slices/homeSlice";
import { HomeBanner } from "@/components/features/home/<USER>";
export function HomeContent() {
  const searchParams = useSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  const { selectedChain, collections } = useSelector(
    (state: RootState) => state.home
  );

  // Update selectedChain from URL
  // Update selectedChain from URL
  useEffect(() => {
    const chainParam = searchParams.get("chain");
    let newChain: Chain | null = null;

    if (chainParam && chainParam !== "all") {
      const matchedChain = getSupportedChains().find(
        (chain) => chain.id.toString() === chainParam
      );
      newChain = matchedChain || null; // Nếu không tìm thấy, gán null
    }

    dispatch(setChain(newChain));
  }, [searchParams, dispatch]);

  // Fetch data và subscription
  useHomeData({ chainId: selectedChain?.id.toString() || null });
  console.log(collections[0]);
  return (
    <div className="pt-4">
      <HomeBanner />
      <CarouselCollection />

      {/* <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mt-8">
        <h2 className="text-2xl font-bold">Hot Collections</h2>
        <ViewToggle viewMode={viewMode} setViewMode={setViewMode} />
      </div> */}

      {/* <FeaturedCollections chainId={selectedChain} viewMode={viewMode} /> */}
      {/* <NFTCollections /> */}

      {/* <NFTCarousel chainId={selectedChain} /> */}

      {/* <FeaturedArtists /> */}

      {/* <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <h2 className="text-2xl font-bold">Trending NFTs</h2>
      </div>

      <TrendingNFTs chainId={selectedChain} viewMode={viewMode} />

      <RecentlySold chainId={selectedChain} />  */}
    </div>
  );
}
