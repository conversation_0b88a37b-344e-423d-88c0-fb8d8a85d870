"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Check } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import type { Nft } from "@/lib/api/graphql/generated";
import { TooltipContent } from "@/components/ui/tooltip";
import { TooltipTrigger } from "@/components/ui/tooltip";
import { Tooltip } from "@/components/ui/tooltip";
import { TooltipProvider } from "@/components/ui/tooltip";

export type NFTTableItem = Nft & {
  selected?: boolean;
  onSelect?: () => void;
  onCardClick?: (nft: Nft) => void;
};

export const columns: ColumnDef<NFTTableItem>[] = [
  {
    id: "select",
    header: () => <span className="sr-only">Select</span>,
    cell: ({ row }) => {
      const selected = row.original.selected ?? false;
      return (
        <div className="flex justify-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              row.original.onSelect?.();
            }}
            className={`h-6 w-6 rounded-full border flex items-center justify-center ${
              selected
                ? "bg-pink-600 text-white"
                : "bg-muted/30 text-muted-foreground"
            }`}
          >
            {selected ? (
              <Check className="w-4 h-4" />
            ) : (
              <span className="font-bold text-lg">+</span>
            )}
          </button>
        </div>
      );
    },
  },
  {
    id: "item",
    header: "Item",
    cell: ({ row }) => {
      const nft = row.original;
      const imageSrc = nft.image ?? "https://placehold.co/32x32";
      console.log(imageSrc);
      const name = nft.name ?? "NFT";
      return (
        <div
          className="flex items-center gap-2 justify-start cursor-pointer"
          onClick={() => nft.onCardClick?.(nft)}
        >
          <div className={cn("h-8 w-8 rounded overflow-hidden flex-shrink-0")}>
            <Image
              src={imageSrc}
              alt={`NFT ${nft.id || "unknown"}`}
              width={32}
              height={32}
              className="h-full w-full object-cover"
            />
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="font-semibold truncate max-w-[120px]">
                  {name}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    id: "rarity",
    header: "Rarity",
    cell: () => "--",
  },
  {
    id: "price",
    header: "Price",
    cell: ({ row }) => {
      const nft = row.original;
      const mintPrice =
        nft.mintPrice && !isNaN(Number(nft.mintPrice))
          ? Number(nft.mintPrice).toFixed(3)
          : "0.000";
      return (
        <div className="flex items-center justify-start gap-1">
          <span>{mintPrice} SOL</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" fill="#9945FF" />
          </svg>
        </div>
      );
    },
  },
  {
    id: "floorDifference",
    header: "Floor Difference",
    cell: ({ row }) => {
      const nft = row.original;
      const mintPrice =
        nft.mintPrice && !isNaN(Number(nft.mintPrice))
          ? Number(nft.mintPrice)
          : 0;
      const basePrice = 0.0276;
      const diff = ((mintPrice - basePrice) / basePrice) * 100;

      if (diff === 0) return <span className="text-foreground">0.00%</span>;
      if (diff > 0)
        return <span className="text-green-500">+{diff.toFixed(2)}%</span>;
      return <span className="text-red-500">{diff.toFixed(2)}%</span>;
    },
  },

  {
    id: "topOffer",
    header: "Top Offer",
    cell: () => "--",
  },

  {
    id: "owner",
    header: "Owner",
    cell: ({ row }) => {
      const nft = row.original;
      const owner = nft.owner ?? "Unknown";
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className=" truncate max-w-[100px] inline-block">
                {owner}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>{owner}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    id: "listed",
    header: "Listed",
    cell: () => "3h",
  },
];
