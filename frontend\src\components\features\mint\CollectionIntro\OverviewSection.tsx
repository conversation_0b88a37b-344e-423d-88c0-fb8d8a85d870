import { RootState } from "@/store/store";
import { useSelector } from "react-redux";

const OverviewSection = () => {
  const { data } = useSelector((state: RootState) => state.collection);
  if (!data) return null;
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-4">
        <h1 className="text-xl font-bold text-gray-900 dark:text-white">
          {data.name}
        </h1>

        {data.contractAddress && (
          <div className="flex space-x-3">
            <button className="flex items-center space-x-1 text-gray-600 dark:text-gray-400 text-xs">
              <span>Contract {data.contractAddress}</span>
            </button>
          </div>
        )}

        <div className="space-y-3 text-xs text-gray-700 dark:text-gray-300">
          <p>{data.description}</p>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Data
        </h2>

        <div className="space-y-3">
          <div>
            <p className="text-xs font-medium text-gray-900 dark:text-white flex items-center">
              🏆 Mint start date: {data.mintStartDate}
            </p>
            <p className="text-[10px] text-gray-600 dark:text-gray-400">
              Max supply: {data.maxSupply}
            </p>
          </div>

          <div>
            <p className="text-xs font-medium text-gray-900 dark:text-white">
              Image:
            </p>
            <ul className="list-disc pl-5 space-y-0.5 text-[10px] text-gray-700 dark:text-gray-300">
              Link: {data.image}
            </ul>
          </div>
          <div>
            <p className="text-xs font-medium text-gray-900 dark:text-white">
              Uri:
            </p>

            <ul className="list-disc pl-5 space-y-0.5 text-[10px] text-gray-700 dark:text-gray-300">
              Link: {data.uri}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewSection;
