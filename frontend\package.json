{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.yml --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@apollo/client": "^3.13.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/postcss": "^4.0.9", "@tanstack/react-query": "^5.71.5", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "ethers": "6.13.5", "framer-motion": "^12.5.0", "graphql": "^16.10.0", "graphql-ws": "^6.0.4", "js-cookie": "^3.0.5", "lightweight-charts": "^5.0.6", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "next": "15.2.0", "next-themes": "^0.4.4", "pinata": "^2.1.3", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "recharts": "^2.15.1", "redux-persist": "^6.0.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tus-js-client": "^4.3.1", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "viem": "^2.25.0", "wagmi": "^2.14.16", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/typescript": "^4.1.5", "@graphql-codegen/typescript-operations": "^4.5.1", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@parcel/watcher": "^2.5.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axios-mock-adapter": "^2.1.0", "eslint": "^9", "eslint-config-next": "15.2.0", "jest": "^29.7.0", "tailwindcss": "^4.0.9", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5"}}