import { ListingStep } from "@/types/listingNft";
import { useState } from "react";

export const useListingSteps = (needsApproval: boolean) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showSteps, setShowSteps] = useState(false);

  const steps: ListingStep[] = [
    {
      step: 1,
      current: currentStep,
      label: "Step 1 - Approve NFT",
      desc: "Approve the marketplace to manage your NFTs...",
      hidden: !needsApproval,
    },
    {
      step: 2,
      current: currentStep,
      label: `Step ${needsApproval ? 2 : 1} - Initialize Listing`,
      desc: "Preparing your listing...",
    },
    {
      step: 3,
      current: currentStep,
      label: `Step ${needsApproval ? 3 : 2} - Sign Transaction`,
      desc: "Confirm transaction in your wallet...",
    },
    {
      step: 4,
      current: currentStep,
      label: `Step ${needsApproval ? 4 : 3} - Confirm Listing`,
      desc: "Waiting for backend confirmation...",
    },
  ];

  return {
    currentStep,
    setCurrentStep,
    showSteps,
    setShowSteps,
    steps,
  };
};
