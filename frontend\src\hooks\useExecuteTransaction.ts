/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAccount, useWalletClient, usePublicClient } from "wagmi";
import { BrowserProvider, ethers } from "ethers";
import { Step } from "@/lib/api/graphql/generated";
import { WalletClient } from "viem";
import { EIP712SignData } from "@/types/eip712";
import {
  getEIP712Types,
  constructMessage,
  validateMessage,
  validateChainId,
  validateDomain,
  validateWallet,
  withRetry,
} from "@/lib/utils/listing";

interface TransactionParams {
  to: `0x${string}`;
  data: `0x${string}`;
  value: string;
  chainId: number;
  estimatedGas?: string;
  sign?: EIP712SignData;
}

interface TransactionResult {
  txHash: `0x${string}`;
  receipt: ethers.TransactionReceipt;
  signature?: string;
}

// Main hook
export function useExecuteTransaction() {
  const { address, chainId: walletChainId } = useAccount();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();

  const validateStepParams = (step: Step): TransactionParams => {
    try {
      const params = JSON.parse(step.params);
      if (!params.to || !ethers.isAddress(params.to)) {
        throw new Error(`Invalid address in step ${step.id}: ${params.to}`);
      }
      if (!params.data || !ethers.isHexString(params.data)) {
        throw new Error(`Invalid transaction data for step ${step.id}`);
      }
      if (!walletChainId) {
        throw new Error("Wallet chain ID is not defined");
      }
      if (Number(params.chainId) !== walletChainId) {
        throw new Error(
          `Chain ID mismatch: expected ${walletChainId}, got ${params.chainId}`
        );
      }
      return {
        ...params,
        to: params.to as `0x${string}`,
        data: params.data as `0x${string}`,
        chainId: Number(params.chainId),
        sign: params.sign,
      };
    } catch (error) {
      throw new Error(
        `Invalid JSON in step ${step.id}: ${(error as Error).message}`
      );
    }
  };

  const simulateTransaction = async (
    provider: BrowserProvider,
    params: TransactionParams
  ): Promise<void> => {
    try {
      console.log("Simulating transaction:", {
        to: params.to,
        data: params.data.slice(0, 100) + "...",
        value: params.value,
      });
      await provider.call({
        to: params.to,
        data: params.data,
        value: BigInt(params.value || "0"),
      });
      console.log("Transaction simulation successful");
    } catch (error: any) {
      console.error("Transaction simulation error:", {
        message: error.message,
        details: error.details,
      });
      throw new Error(
        `Transaction simulation failed: ${
          error.reason || error.message || "Unknown error"
        }`
      );
    }
  };

  const executeSingleStep = async (
    step: Step,
    provider: BrowserProvider
  ): Promise<TransactionResult> => {
    console.log(`Processing step ${step.id}`);
    const params = validateStepParams(step);

    let signature: string | undefined;
    if (step.id.includes("sign_and_list") && params.sign) {
      try {
        const signData = params.sign;
        console.log("signData", signData);
        console.log("Preparing to sign EIP-712 data:", {
          domain: signData.domain,
          types: signData.types,
          value: signData.value,
          walletAddress: address,
          walletChainId,
        });

        if (!walletClient || !address) {
          throw new Error("Wallet not connected");
        }

        // Validate domain, wallet and chainId
        validateDomain(signData.domain, [
          "name",
          "version",
          "chainId",
          "verifyingContract",
        ]);
        validateWallet(signData.value.seller, address);
        validateChainId(Number(signData.domain.chainId), walletChainId);

        // Construct and validate message
        const message = constructMessage(signData);
        validateMessage(message);

        // Log the full signData for debugging
        console.log("Full signData:", JSON.stringify(signData, null, 2));
        console.log(
          "EIP-712 message to sign:",
          JSON.stringify(message, null, 2)
        );

        // Get signer and sign message
        const provider = new ethers.BrowserProvider(walletClient.transport);
        const signer = await provider.getSigner();
        signature = await signer.signTypedData(
          signData.domain,
          getEIP712Types(),
          message
        );

        console.log("Signature generated:", signature);

        // Verify signature locally
        const recoveredAddress = ethers.verifyTypedData(
          signData.domain,
          getEIP712Types(),
          message,
          signature as string
        );
        console.log("Recovered address from signature:", recoveredAddress);
        if (recoveredAddress.toLowerCase() !== address.toLowerCase()) {
          throw new Error(
            `Signature verification failed: recovered ${recoveredAddress}, expected ${address}`
          );
        }

        if (
          !signature ||
          !signature.startsWith("0x") ||
          signature.length !== 132
        ) {
          throw new Error("Invalid signature format");
        }

        return {
          txHash: "0x" as `0x${string}`,
          receipt: {} as ethers.TransactionReceipt,
          signature,
        };
      } catch (error) {
        console.error("Signing error:", error);
        throw new Error(
          `Failed to sign EIP712 for step ${step.id}: ${
            (error as Error).message
          }`
        );
      }
    }

    await simulateTransaction(provider, params);

    return await sendTransactionWithRetry({
      to: params.to,
      value: BigInt(params.value || "0"),
      data: params.data,
      chainId: params.chainId,
    });
  };

  const sendTransactionWithRetry = async ({
    to,
    value,
    data,
    chainId,
  }: {
    to: `0x${string}`;
    value: bigint;
    data: `0x${string}`;
    chainId: number;
  }): Promise<TransactionResult> => {
    try {
      console.log("Sending transaction with params:", {
        to,
        value: value.toString(),
        data: data.slice(0, 100) + "...",
        chainId,
      });

      const txHash = await withRetry(
        async () => {
          const nonce = await publicClient?.getTransactionCount({
            address: address!,
            blockTag: "pending",
          });
          console.log(`Using nonce: ${nonce}`);

          return await (
            walletClient as unknown as WalletClient
          ).sendTransaction({
            to,
            value,
            data,
            account: address!,
            chainId,
            nonce,
            gas: undefined,
            chain: null,
          });
        },
        5,
        3000
      );

      console.log(`Transaction sent: ${txHash}`);
      const provider = new BrowserProvider(walletClient!.transport);
      const transaction = await provider.getTransaction(txHash);

      if (!transaction) {
        throw new Error(`Transaction not found: ${txHash}`);
      }

      console.log("Waiting for transaction receipt...");
      const receipt = await transaction.wait();

      if (!receipt || receipt.status !== 1) {
        throw new Error(`Transaction failed: ${txHash}`);
      }

      console.log(`Transaction completed:`, {
        hash: txHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
      });

      return { txHash, receipt };
    } catch (error: any) {
      console.error("Transaction error:", error);
      if (error.code === "INSUFFICIENT_FUNDS") {
        throw new Error("Insufficient funds for gas");
      } else if (error.code === "ACTION_REJECTED") {
        throw new Error("User rejected transaction");
      } else if (error.message?.includes("nonce")) {
        throw new Error(
          "Nonce error: Please reset wallet or wait for pending transaction"
        );
      }
      throw new Error(
        `Transaction failed: ${error.message || "Unknown error"}`
      );
    }
  };

  const executeTransaction = async (
    steps: Step[]
  ): Promise<TransactionResult> => {
    if (!steps?.length || !walletClient || !address) {
      throw new Error("No steps to execute or wallet not connected");
    }

    const provider = new BrowserProvider(walletClient.transport);
    return await executeSingleStep(steps[0], provider);
  };

  return { executeTransaction };
}
