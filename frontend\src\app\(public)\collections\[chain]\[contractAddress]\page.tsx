import { CollectionDetail } from "@/components/features/collections/CollectionDetail";
// import { NFTGrid } from "@/components/features/nft/NFTGrid";
import { notFound } from "next/navigation";

interface CollectionDetailPageProps {
  params: {
    chain: string;
    contractAddress: string; // <PERSON><PERSON><PERSON> từ collectionAddress thành contractAddress
  };
}

export async function generateMetadata({ params }: CollectionDetailPageProps) {
  // Validate chain và contractAddress trước
  const isValidAddress = /^0x[a-fA-F0-9]{40}$/.test(params.contractAddress);
  if (!isValidAddress) {
    return {
      title: "Collection Not Found | NFT Marketplace",
      description: "The requested collection address is invalid",
    };
  }
  const collection = {
    name: "",
  };
  if (!collection) {
    return {
      title: "Collection Not Found | NFT Marketplace",
      description: "The requested collection was not found",
    };
  }

  return {
    title: `${collection.name} | ${params.chain} | NFT Marketplace`,
    description: `View details and NFTs in ${collection.name} collection on ${params.chain}`,
  };
}

export default async function CollectionDetailPage({
  params,
}: CollectionDetailPageProps) {
  // Validate contractAddress format (Ethereum address)
  const isValidAddress = /^0x[a-fA-F0-9]{40}$/.test(params.contractAddress);
  if (!isValidAddress) {
    notFound();
  }

  return (
    <div className="space-y-8">
      <CollectionDetail />
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">NFTs in this Collection</h2>
        {/* <NFTGrid /> */}
      </div>
    </div>
  );
}
