import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { notFound } from "next/navigation";
import { ChainFilter } from "@/components/features/shared/ChainFilter";
import { CollectionsList } from "@/components/features/collections/CollectionsList";
import { validate<PERSON>hain } from "@/lib/utils/validate";

interface ChainCollectionsPageProps {
  params: {
    chain: string; // Dùng tên chain thay vì chainId
  };
}

export function generateMetadata({ params }: ChainCollectionsPageProps) {
  const validChain = validateChain(params.chain);
  if (!validChain) {
    return {
      title: "Chain Not Found | NFT Marketplace",
      description: "The requested blockchain was not found",
    };
  }

  const chainName = validChain.name;

  return {
    title: `${chainName} Collections | NFT Marketplace`,
    description: `Browse NFT collections on the ${chainName} blockchain`,
  };
}

export default function ChainCollectionsPage({
  params,
}: ChainCollectionsPageProps) {
  const validChain = validateChain(params.chain);
  if (!validChain) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/collections">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Collections on {validChain.name}</h1>
      </div>
      <ChainFilter baseUrl="/collections/chain" />
      <CollectionsList /> {/* Truyền chainId */}
    </div>
  );
}
