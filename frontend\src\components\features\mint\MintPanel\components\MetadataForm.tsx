"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Collection } from "@/lib/api/graphql/generated";
import { NftMetadataInput } from "@/types/mint.types";

interface MetadataFormProps {
  isSameArtType: boolean;
  collection?: Collection | null;
  name: string;
  setName: (name: string) => void;
  description: string;
  setDescription: (description: string) => void;
  amount: number;
  setAmount: (amount: number) => void;
  nameError?: string;
  descriptionError?: string;
  batchMetadata: NftMetadataInput[];
}

export function MetadataForm({
  isSameArtType,
  collection,
  name,
  setName,
  description,
  setDescription,
  amount,
  setAmount,
  nameError,
  descriptionError,
  batchMetadata,
}: MetadataFormProps) {
  return (
    <div className="space-y-3">
      {/* Name Field */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label
            htmlFor="name"
            className="text-sm text-white flex items-center gap-1"
          >
            Name{" "}
            {!isSameArtType && <span className="text-pink-500">*</span>}
          </Label>
          <span className="text-sm text-gray-400">
            {isSameArtType ? "Optional" : "Required"}
          </span>
        </div>
        <div className="relative">
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value.slice(0, 100))}
            placeholder={
              isSameArtType ? `${collection?.name || "NFT"}` : "NFT Name"
            }
            className={cn(
              "h-10 text-sm bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 dark:bg-[#0f0a19] dark:border-gray-800/50 dark:text-white dark:placeholder:text-gray-600",
              nameError &&
                "border-red-500 focus-visible:ring-red-500 dark:border-red-800 dark:focus-visible:ring-red-800"
            )}
            aria-invalid={nameError ? "true" : "false"}
          />
          {!nameError && name.trim() && (
            <CheckCircle2 className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
        </div>
        {nameError ? (
          <p className="text-sm text-red-400">{nameError}</p>
        ) : (
          <p className="text-sm text-gray-400">
            Max 100 characters
            {isSameArtType &&
              `. Defaults to "${
                collection?.name || "NFT"
              } #<number>" if empty.`}
          </p>
        )}
      </div>

      {/* Description Field */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label
            htmlFor="description"
            className="text-sm text-white flex items-center gap-1"
          >
            Description{" "}
            {!isSameArtType && <span className="text-pink-500">*</span>}
          </Label>
          <span className="text-sm text-gray-400">
            {isSameArtType ? "Optional" : "Required"}
          </span>
        </div>
        <div className="relative">
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value.slice(0, 1000))}
            placeholder={
              isSameArtType
                ? collection?.description || "Describe your NFT"
                : "Describe your NFT"
            }
            className={cn(
              "h-20 text-sm bg-white border-gray-300 text-gray-900 placeholder:text-gray-500 dark:bg-[#0f0a19] dark:border-gray-800/50 dark:text-white dark:placeholder:text-gray-600",
              descriptionError &&
                "border-red-500 focus-visible:ring-red-500 dark:border-red-800 dark:focus-visible:ring-red-800"
            )}
            aria-invalid={descriptionError ? "true" : "false"}
          />
          {!descriptionError && description.trim() && (
            <CheckCircle2 className="absolute right-2 top-2 h-4 w-4 text-green-500" />
          )}
        </div>
        {descriptionError ? (
          <p className="text-sm text-red-400">{descriptionError}</p>
        ) : (
          <p className="text-sm text-gray-400">
            Max 1000 characters
            {isSameArtType &&
              `. Defaults to collection description if empty.`}
          </p>
        )}
      </div>

      {/* Amount Field */}
      {(!batchMetadata.length || isSameArtType) && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="amount" className="text-sm text-white">
              Amount (1-{collection?.mintLimit || 100})
            </Label>
            <span className="text-sm text-gray-400">
              Number of NFTs to mint
            </span>
          </div>
          <Input
            id="amount"
            type="number"
            value={amount}
            onChange={(e) =>
              setAmount(
                Math.max(
                  1,
                  Math.min(
                    Number(collection?.mintLimit || 100),
                    Number(e.target.value)
                  )
                )
              )
            }
            min={1}
            max={collection?.mintLimit || 100}
            className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
            aria-label="Number of NFTs to mint"
          />
          <p className="text-sm text-gray-400">
            Number of NFTs to mint in this transaction.{" "}
            {isSameArtType
              ? "All NFTs share the same metadata."
              : "Each NFT requires unique metadata (use Batch Upload for multiple NFTs)."}
          </p>
        </div>
      )}
    </div>
  );
}
