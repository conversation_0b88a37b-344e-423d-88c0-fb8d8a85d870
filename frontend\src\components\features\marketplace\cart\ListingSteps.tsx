import { StepRow } from "@/components/features/marketplace/cart/CommonComponents";
import { ListingStep } from "@/types/listingNft";

interface ListingStepsProps {
  steps: ListingStep[];
}

export const ListingSteps = ({ steps }: ListingStepsProps) => {
  return (
    <div className="rounded-2xl p-8 bg-[#18141c] border border-neutral-700 mb-2">
      <div className="text-xl font-bold mb-8">Listing...</div>
      <div className="space-y-7">
        {steps.map((step) => (
          <StepRow key={step.step} {...step} />
        ))}
      </div>
    </div>
  );
};
