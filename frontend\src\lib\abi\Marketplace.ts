export const Marketplace = {
  "_format": "hh-sol-artifact-1",
  "contractName": "Marketplace",
  "sourceName": "contracts/Marketplace.sol",
  "abi": [
    {
      "inputs": [],
      "name": "EnforcedPause",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "ExpectedPause",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "FailedCall",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "balance",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "needed",
          "type": "uint256"
        }
      ],
      "name": "InsufficientBalance",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "InvalidInitialization",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "NotInitializing",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        }
      ],
      "name": "OwnableInvalidOwner",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "OwnableUnauthorizedAccount",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "ReentrancyGuardReentrantCall",
      "type": "error"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "reservePrice",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        }
      ],
      "name": "AuctionCreated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "highestBidder",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "totalPrice",
          "type": "uint256"
        }
      ],
      "name": "AuctionEnded",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "bidder",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "BidPlaced",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "startPrice",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "endPrice",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "startTimestamp",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "string",
          "name": "metadataURI",
          "type": "string"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        }
      ],
      "name": "DutchAuctionCreated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [],
      "name": "EIP712DomainChanged",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "uint64",
          "name": "version",
          "type": "uint64"
        }
      ],
      "name": "Initialized",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        }
      ],
      "name": "ListingCancelled",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "amounts",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "prices",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "expiryTimestamp",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "string[]",
          "name": "metadataURIs",
          "type": "string[]"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        }
      ],
      "name": "ListingCreated",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "string",
          "name": "reason",
          "type": "string"
        }
      ],
      "name": "ListingFailed",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "buyer",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "amounts",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "totalPrice",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        }
      ],
      "name": "ListingPurchased",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "amounts",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "uint256[]",
          "name": "prices",
          "type": "uint256[]"
        },
        {
          "indexed": false,
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "expiryTimestamp",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "nonce",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "bytes32",
          "name": "metadataURIsHash",
          "type": "bytes32"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        }
      ],
      "name": "OffchainListingSigned",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "previousOwner",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "OwnershipTransferred",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "Paused",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "indexed": false,
          "internalType": "string",
          "name": "reason",
          "type": "string"
        }
      ],
      "name": "RoyaltyInfoFailed",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "Unpaused",
      "type": "event"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "auctions",
      "outputs": [
        {
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "reservePrice",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "highestBid",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "highestBidder",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "bool",
          "name": "active",
          "type": "bool"
        },
        {
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address[]",
          "name": "contractAddresses",
          "type": "address[]"
        },
        {
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        }
      ],
      "name": "autoCleanupExpiredListings",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        }
      ],
      "name": "cancelListing",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        }
      ],
      "name": "checkNFTsListingStatus",
      "outputs": [
        {
          "components": [
            {
              "internalType": "bool",
              "name": "isListed",
              "type": "bool"
            },
            {
              "internalType": "uint256",
              "name": "price",
              "type": "uint256"
            },
            {
              "internalType": "address",
              "name": "seller",
              "type": "address"
            },
            {
              "internalType": "uint256",
              "name": "expiryTimestamp",
              "type": "uint256"
            }
          ],
          "internalType": "struct Marketplace.ListingStatus[]",
          "name": "",
          "type": "tuple[]"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "cleanupBot",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address[]",
          "name": "contractAddresses",
          "type": "address[]"
        },
        {
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        }
      ],
      "name": "cleanupExpiredListings",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "reservePrice",
          "type": "uint256"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        }
      ],
      "name": "createAuction",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "startPrice",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "endPrice",
          "type": "uint256"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "uint256",
          "name": "startTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "string",
          "name": "metadataURI",
          "type": "string"
        }
      ],
      "name": "createDutchAuction",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "dutchAuctions",
      "outputs": [
        {
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "startPrice",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "endPrice",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "bool",
          "name": "active",
          "type": "bool"
        },
        {
          "internalType": "uint256",
          "name": "startTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "endTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "string",
          "name": "metadataURI",
          "type": "string"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "eip712Domain",
      "outputs": [
        {
          "internalType": "bytes1",
          "name": "fields",
          "type": "bytes1"
        },
        {
          "internalType": "string",
          "name": "name",
          "type": "string"
        },
        {
          "internalType": "string",
          "name": "version",
          "type": "string"
        },
        {
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "verifyingContract",
          "type": "address"
        },
        {
          "internalType": "bytes32",
          "name": "salt",
          "type": "bytes32"
        },
        {
          "internalType": "uint256[]",
          "name": "extensions",
          "type": "uint256[]"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        }
      ],
      "name": "endAuction",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "feeManager",
      "outputs": [
        {
          "internalType": "contract NFTFeeManager",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "getActiveListingCount",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "_feeManager",
          "type": "address"
        }
      ],
      "name": "initialize",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "isListed",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "lastListingTimestamp",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        },
        {
          "internalType": "uint256[]",
          "name": "amounts",
          "type": "uint256[]"
        },
        {
          "internalType": "uint256[]",
          "name": "prices",
          "type": "uint256[]"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "uint256",
          "name": "expiryTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "string[]",
          "name": "metadataURIs",
          "type": "string[]"
        }
      ],
      "name": "listNFTs",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "listingCount",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "name": "listings",
      "outputs": [
        {
          "internalType": "address",
          "name": "seller",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "price",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        },
        {
          "internalType": "enum Marketplace.NFTStandard",
          "name": "standard",
          "type": "uint8"
        },
        {
          "internalType": "bool",
          "name": "active",
          "type": "bool"
        },
        {
          "internalType": "uint256",
          "name": "expiryTimestamp",
          "type": "uint256"
        },
        {
          "internalType": "string",
          "name": "metadataURI",
          "type": "string"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "maxBatchSize",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "maxExpiryDuration",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "minListingInterval",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "nonces",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "owner",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "pause",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "paused",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        }
      ],
      "name": "placeBid",
      "outputs": [],
      "stateMutability": "payable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "tokenId",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "purchaseDutchAuction",
      "outputs": [],
      "stateMutability": "payable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "contractAddress",
          "type": "address"
        },
        {
          "internalType": "uint256[]",
          "name": "tokenIds",
          "type": "uint256[]"
        },
        {
          "internalType": "uint256[]",
          "name": "amounts",
          "type": "uint256[]"
        },
        {
          "components": [
            {
              "internalType": "address",
              "name": "seller",
              "type": "address"
            },
            {
              "internalType": "address",
              "name": "contractAddress",
              "type": "address"
            },
            {
              "internalType": "uint256[]",
              "name": "tokenIds",
              "type": "uint256[]"
            },
            {
              "internalType": "uint256[]",
              "name": "amounts",
              "type": "uint256[]"
            },
            {
              "internalType": "uint256[]",
              "name": "prices",
              "type": "uint256[]"
            },
            {
              "internalType": "enum Marketplace.NFTStandard",
              "name": "standard",
              "type": "uint8"
            },
            {
              "internalType": "uint256",
              "name": "expiryTimestamp",
              "type": "uint256"
            },
            {
              "internalType": "uint256",
              "name": "nonce",
              "type": "uint256"
            },
            {
              "internalType": "bytes32",
              "name": "metadataURIsHash",
              "type": "bytes32"
            }
          ],
          "internalType": "struct Marketplace.OffchainListing",
          "name": "offchainListing",
          "type": "tuple"
        },
        {
          "internalType": "bytes",
          "name": "signature",
          "type": "bytes"
        }
      ],
      "name": "purchaseNFTs",
      "outputs": [],
      "stateMutability": "payable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "renounceOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "_cleanupBot",
          "type": "address"
        }
      ],
      "name": "setCleanupBot",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "size",
          "type": "uint256"
        }
      ],
      "name": "setMaxBatchSize",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "duration",
          "type": "uint256"
        }
      ],
      "name": "setMaxExpiryDuration",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "interval",
          "type": "uint256"
        }
      ],
      "name": "setMinListingInterval",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "components": [
            {
              "internalType": "address",
              "name": "seller",
              "type": "address"
            },
            {
              "internalType": "address",
              "name": "contractAddress",
              "type": "address"
            },
            {
              "internalType": "uint256[]",
              "name": "tokenIds",
              "type": "uint256[]"
            },
            {
              "internalType": "uint256[]",
              "name": "amounts",
              "type": "uint256[]"
            },
            {
              "internalType": "uint256[]",
              "name": "prices",
              "type": "uint256[]"
            },
            {
              "internalType": "enum Marketplace.NFTStandard",
              "name": "standard",
              "type": "uint8"
            },
            {
              "internalType": "uint256",
              "name": "expiryTimestamp",
              "type": "uint256"
            },
            {
              "internalType": "uint256",
              "name": "nonce",
              "type": "uint256"
            },
            {
              "internalType": "bytes32",
              "name": "metadataURIsHash",
              "type": "bytes32"
            }
          ],
          "internalType": "struct Marketplace.OffchainListing",
          "name": "listing",
          "type": "tuple"
        },
        {
          "internalType": "bytes",
          "name": "signature",
          "type": "bytes"
        }
      ],
      "name": "signOffchainListing",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "unpause",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    }
  ],
  "bytecode": "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",
  "deployedBytecode": "0x6080604052600436101561001257600080fd5b60003560e01c806207df3014612e3c5780631082eea214612d73578063148cb63914612cf057806324361c4d14612cd257806326e9995914612ca95780632913daa014612c8b5780632b26a6bf14612c2a5780633f4ba83a14612ba757806344f91c1e14612af65780635c975abb14612ac6578063634185c21461284657806368bffffc146127d5578063715018a61461276b5780637742084414611a4a5780637ecebe00146127315780638456cb59146126bd57806384b0196e1461240957806386fa279b146123e757806388cedbc4146122175780638da5cb5b146121e15780639ba21beb14611d335780639c9e63ec14611aa25780639d335c4e14611a68578063a9b07c2614611a4a578063acd0a818146113cb578063b248e496146113ad578063b2ddee061461127f578063b84644c514610ed9578063c4d66de814610998578063cd1cbe8f14610939578063cdb3cd25146108ec578063d0fb0203146108c3578063d1e00e0614610591578063d5d7dd8514610570578063d98b9bb5146103f2578063f2fde38b146103c75763f98afb5a146101b257600080fd5b60603660031901126103c2576101c6612eed565b602435604435916101d561387f565b6101dd613855565b60018060a01b0381169283600052600a6020526040600020836000526020526040600020600481019283549061021860ff8360081c1661351f565b600583015494854210610387578387936006879601548042111561023b9061376d565b60038301908154998a891115610250906135d7565b6001850154914291600287015461026694614860565b87610271818361370a565b34101561027d9061371d565b6102869161370a565b98899861029291613760565b809155159661034695610323956102fc61031d9661030c957f17b7968264a5ca336687d23a4b34a480868095c8f8d5ea88ef2010e88d5582ef9c61036c575b506102db846138bb565b60ff6102e68d6138bb565b92541660018060a01b03895416923392866138e6565b84546001600160a01b0316613a58565b50546001600160a01b0316966138bb565b926138bb565b6103386040519360808552608085019061311b565b90838203602085015261311b565b9260408201524660608201528033930390a46001600080516020614f5583398151915255005b61ff001916815561037e600154613679565b600155386102d1565b60405162461bcd60e51b8152602060048201526013602482015272105d58dd1a5bdb881b9bdd081cdd185c9d1959606a1b6044820152606490fd5b600080fd5b346103c25760203660031901126103c2576103f06103e3612eed565b6103eb61381f565b6137a9565b005b60403660031901126103c257610406612eed565b6024359061041261387f565b61041a613855565b60018060a01b031680600052600b602052604060002082600052602052604060002061044f60ff600583015460081c1661351f565b61045f600682015442111561376d565b600281018054918234111561053d57600181015434106104f8576003019160018060a01b0383541690816104e8575b5050349081905581546001600160a01b031916339081179092556040519081527fdd49bbb40d47a514dddcd458e9718364143bc24a0cca58439ee6f4f45e4ce10d90602090a46001600080516020614f5583398151915255005b6104f1916147fb565b848061048e565b60405162461bcd60e51b815260206004820152601760248201527f4269642062656c6f7720726573657276652070726963650000000000000000006044820152606490fd5b60405162461bcd60e51b815260206004820152600b60248201526a42696420746f6f206c6f7760a81b6044820152606490fd5b346103c25760203660031901126103c25761058961381f565b600435600655005b346103c25760403660031901126103c2576004356001600160401b0381116103c2576105c19036906004016131d0565b6024356001600160401b0381116103c2576106106105e66106159236906004016132fb565b6105ee61387f565b6105f6613855565b83516001600160a01b031661060a85614577565b90614711565b6136ca565b60018060a01b03815116600052600c60205260406000205460e082019081510361088e57602082019160018060a01b0383511692604082019182519460608201805190608084019283519860a08601998a519060028210156107745760c08801958651938b5151956106868761314f565b966106946040519889612f88565b8088526106a3601f199161314f565b0160005b81811061087d575050906106bf969594939291614004565b60005b865190815181101561078a5786516001600160a01b0316916106e5908290613388565b518a519260028410156107745760019361070892858060a01b038a511692613d0e565b818060a01b03875116600052600d6020526040600020610729828a51613388565b5160005260205280604060002055818060a01b0387511660005260096020526040600020610758828a51613388565b5160005260205260406000208260ff19825416179055016106c2565b634e487b7160e01b600052602160045260246000fd5b505090918690889460018060a01b03815116600052600c60205260406000206107b381546135a7565b90558051965197519351915195516001600160a01b039889169890971696926002841015610774576108516108467f3e0fd51f7129161a385331d4de55b6a2ccc302d3690aaa9602e86af60448a6b99861083861010061082a9a519551960151966040519a8b9a6101008c526101008c019061311b565b908a820360208c015261311b565b9088820360408a015261311b565b94606087019061304d565b608085015260a084015260c08301524660e08301520390a36001600080516020614f5583398151915255005b806060602080938c010152016106a7565b60405162461bcd60e51b815260206004820152600d60248201526c496e76616c6964206e6f6e636560981b6044820152606490fd5b346103c25760003660031901126103c2576000546040516001600160a01b039091168152602090f35b346103c25760403660031901126103c2576001600160a01b0361090d612eed565b1660005260096020526040600020602435600052602052602060ff604060002054166040519015158152f35b346103c25760203660031901126103c25760043561095561381f565b801561096057600355005b60405162461bcd60e51b815260206004820152601060248201526f24b73b30b634b210323ab930ba34b7b760811b6044820152606490fd5b346103c25760203660031901126103c2576109b1612eed565b600080516020614f758339815191525460ff8160401c1615906001600160401b03811680159081610ed1575b6001149081610ec7575b159081610ebe575b50610ead5767ffffffffffffffff198116600117600080516020614f758339815191525581610e80575b50610a22614bbe565b610a2a614bbe565b610a33336137a9565b610a3b614bbe565b610a43614bbe565b610a4b614bbe565b6001600080516020614f5583398151915255604090815192610a6d8385612f88565b600b84526a4d61726b6574706c61636560a81b6020850152825193610a928486612f88565b60018552603160f81b6020860152610aa8614bbe565b610ab0614bbe565b8051906001600160401b038211610d60578190610adb600080516020614eb583398151915254612f17565b601f8111610e03575b50602090601f8311600114610d8157600092610d76575b50508160011b916000199060031b1c191617600080516020614eb5833981519152555b83516001600160401b038111610d6057610b46600080516020614f1583398151915254612f17565b601f8111610cee575b50602094601f8211600114610c6d57948192939495600092610c62575b50508160011b916000199060031b1c191617600080516020614f15833981519152555b60007fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d1005560007fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d1015560018060a01b03166001600160601b0360a01b6000541617600055603c60065560056002556301e13380600355610c0a57005b60207fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29168ff000000000000000019600080516020614f758339815191525416600080516020614f75833981519152555160018152a1005b015190508580610b6c565b601f19821695600080516020614f15833981519152600052806000209160005b888110610cd657508360019596979810610cbd575b505050811b01600080516020614f1583398151915255610b8f565b015160001960f88460031b161c19169055858080610ca2565b91926020600181928685015181550194019201610c8d565b600080516020614f158339815191526000527f5f9ce34815f8e11431c7bb75a8e6886a91478f7ffc1dbb0a98dc240fddd76b75601f830160051c81019160208410610d56575b601f0160051c01905b818110610d4a5750610b4f565b60008155600101610d3d565b9091508190610d34565b634e487b7160e01b600052604160045260246000fd5b015190508680610afb565b600080516020614eb583398151915260009081528281209350601f198516905b818110610deb5750908460019594939210610dd2575b505050811b01600080516020614eb583398151915255610b1e565b015160001960f88460031b161c19169055868080610db7565b92936020600181928786015181550195019301610da1565b600080516020614eb58339815191526000529091507f42ad5d3e1f2e6e70edcf6d991b8a3023d3fca8047a131592f9edb9fd9b89d57d601f840160051c81019160208510610e76575b90601f859493920160051c01905b818110610e675750610ae4565b60008155849350600101610e5a565b9091508190610e4c565b68ffffffffffffffffff19166801000000000000000117600080516020614f758339815191525582610a19565b63f92ee8a960e01b60005260046000fd5b905015846109ef565b303b1591506109e7565b8391506109dd565b60a03660031901126103c257610eed612eed565b6024356001600160401b0381116103c257610f0c90369060040161309b565b916044356001600160401b0381116103c257610f2c90369060040161309b565b93906064356001600160401b0381116103c257610f4d9036906004016131d0565b946084356001600160401b0381116103c257610610610f73610fb09236906004016132fb565b610f7b61387f565b610f83613855565b610f91600254871115613316565b610f9c848714613686565b88516001600160a01b031661060a8a614577565b60208601516001600160a01b038581169791168790036112465760c0810151421161120f57600094610fe18561314f565b90610fef6040519283612f88565b858252601f19610ffe8761314f565b01366020840137866060840160408501608086015b898410611137575050505061102a8734101561371d565b60a0830151600281101561077457835161106a916001600160a01b0390911690611055368a8d613166565b903391611063368a8c613166565b90866138e6565b60005b8681106110ed5750505091610346916110c77f17b7968264a5ca336687d23a4b34a480868095c8f8d5ea88ef2010e88d5582ef95948734116110d5575b60018060a01b039051169760405195608087526080870191613655565b918483036020860152613655565b6110e86110e28934613760565b336147fb565b6110aa565b8061113061111361110060019487613388565b5161110c848a8c613362565b359061370a565b8b85611129858d888060a01b038c511694613362565b3591613a58565b500161106d565b909192998c600052600d6020528b6111558c8c604060002093613362565b356000526020526040600020548351518110156111d7576001916111c78b61110c8f8d6111c0828e6111af8a8f6111a8906111a06111cd9e611198888b8f613362565b359251613388565b5110156135d7565b8d51613388565b516111ba8383613388565b52613388565b5193613362565b90613560565b9a01929190611013565b60405162461bcd60e51b815260206004820152601060248201526f151bdad95b881b9bdd081b1a5cdd195960821b6044820152606490fd5b60405162461bcd60e51b815260206004820152600f60248201526e131a5cdd1a5b99c8195e1c1a5c9959608a1b6044820152606490fd5b60405162461bcd60e51b8152602060048201526011602482015270086dedce8e4c2c6e840dad2e6dac2e8c6d607b1b6044820152606490fd5b346103c25760403660031901126103c257611298612eed565b602435906112a461387f565b6112ac613855565b60018060a01b03168060005260086020526040600020826000526020526040600020600381019081549060ff8260081c161561137357546001600160a01b031633036113415761ff0019169055611304600154613679565b600155337fe1bfe61cb157e0896411ccf9a5c40e4c346f7bb6e1d2a44de4a724f0cb5c6fb0600080a46001600080516020614f5583398151915255005b60405162461bcd60e51b815260206004820152600a6024820152692737ba1039b2b63632b960b11b6044820152606490fd5b60405162461bcd60e51b81526020600482015260126024820152714c697374696e67206e6f742061637469766560701b6044820152606490fd5b346103c25760003660031901126103c2576020600654604051908152f35b346103c25760e03660031901126103c2576113e4612eed565b6024356001600160401b0381116103c25761140390369060040161309b565b916044356001600160401b0381116103c25761142390369060040161309b565b906064356001600160401b0381116103c25761144390369060040161309b565b9091600260843510156103c25760c4356001600160401b0381116103c25761146f90369060040161309b565b94909661147a61387f565b611482613855565b33600052600560205261149d60406000205460065490613560565b4210611a0e57336000526005602052426040600020556114be368a83613166565b6114c9368486613166565b6114d4368789613166565b916114de8961314f565b926114ec6040519485612f88565b89845260208401368d8c60051b01116103c2578c9a9c905b8d60051b8c0182106119cc5750611526949360a435936084359392508d614004565b60005b89811061166757509161156e9161156061157c96959461154b8c600154613560565b6001556040519b60e08d5260e08d0191613655565b918a830360208c0152613655565b918783036040890152613655565b9261158c6060860160843561304d565b60a435608086015284840360a0860152808452602084019060208160051b8601019483600091601e1982360301905b84841061160d574660c08b01526001600160a01b038816337faeea3086bafaf698a8764ee378fb7536e76d965a21023a3704677bb3277d79de8c8c038da36001600080516020614f5583398151915255005b90919293949597601f198282030187528835838112156103c257840190602082359201916001600160401b0381116103c25780360383136103c25761165860209283926001956135b6565b9a0197019594019291906115bb565b6001600160a01b038816600090815260096020526040902096989661168d828c85613362565b3560005260205260ff60406000205416611992576116ba336084356116b3848e87613362565b358b613d0e565b6116c581888b613614565b905015611958576116d7818688613362565b356116e3828587613362565b356116ef838a8d613614565b90604051938460e08101106001600160401b0360e087011117610d60578c938f9160e087016040523387526020870190815260408701918252606087019261173960843585613583565b61175660808901956001875260a08a019760a435895236916132c4565b9660c0890197885260018060a01b0316600052600860205261177e896040600020928c613362565b35600090815260209190915260409020965187546001600160a01b0319166001600160a01b0391909116178755516001870155516002808701919091559051916003860191831015610774576117d76117ee938361358f565b51815461ff00191690151560081b61ff0016179055565b516004830155518051906001600160401b038211610d60576118136005840154612f17565b601f811161190d575b50602090601f831160011461189c5791806005926001969594600092611891575b5050600019600383901b1c191690851b179101555b818060a01b03891660005260096020526040600020611872828d86613362565b3560005260205260406000208260ff1982541617905501979597611529565b015190508f8061183d565b90601f1983169160058501600052816000209260005b8181106118f557509260019695949192879383600596106118dc575b505050811b01910155611852565b015160001960f88460031b161c191690558f80806118ce565b929360206001819287860151815501950193016118b2565b600584016000526020600020601f840160051c8101916020851061194e575b601f0160051c01905b818110611942575061181c565b60008155600101611935565b909150819061192c565b60405162461bcd60e51b8152602060048201526012602482015271456d707479206d657461646174612055524960701b6044820152606490fd5b60405162461bcd60e51b815260206004820152601260248201527113919508185b1c9958591e481b1a5cdd195960721b6044820152606490fd5b819d9b9d356001600160401b0381116103c2578e0136601f820112156103c257602091611a008392369084813591016132c4565b8152019101909c9a9c611504565b60405162461bcd60e51b8152602060048201526014602482015273131a5cdd1a5b99c81d1bdbc8199c995c5d595b9d60621b6044820152606490fd5b346103c25760003660031901126103c2576020600154604051908152f35b346103c25760203660031901126103c2576001600160a01b03611a89612eed565b1660005260056020526020604060002054604051908152f35b346103c25760c03660031901126103c257611abb612eed565b6024359060643560843560443560028210156103c25760a43590611add61387f565b611ae5613855565b42821115611cf6578315611cb957600160ff841611611c815780151580611c6f575b611b10906135d7565b611b1c33848888613d0e565b60405161010081018181106001600160401b03821117610d6057604052338152602081019585875260408201906000825260608301600081526080840190858252611b6b60a086019889613583565b60c08501936001855260e086019388855260018060a01b03169a8b600052600b60205260406000208d60005260205260406000209660018060a01b039060018060a01b03905116166001600160601b0360a01b88541617875551600187015551600286015560018060a01b03905116600385019060018060a01b03166001600160601b0360a01b825416179055516004840155600583019551600281101561077457866006936117d7611c3f937f3b7b4c5af27f2d215ab04ff752eb66d4d2500c6584590ed58288a4275196886d9a61358f565b519101556040805194855260208501919091528301523391606090a46001600080516020614f5583398151915255005b50821580611b07575060018114611b07565b60405162461bcd60e51b815260206004820152601060248201526f125b9d985b1a59081cdd185b99185c9960821b6044820152606490fd5b60405162461bcd60e51b8152602060048201526015602482015274496e76616c6964207265736572766520707269636560581b6044820152606490fd5b60405162461bcd60e51b81526020600482015260156024820152740496e76616c696420656e642074696d657374616d7605c1b6044820152606490fd5b346103c2576101203660031901126103c257611d4d612eed565b60a4359060028210156103c257610104356001600160401b0381116103c257366023820112156103c2576001600160401b038160040135116103c2573660248260040135830101116103c257611da161387f565b611da9613855565b608435606435118015906121d7575b612141574260c435108015612133575b801561211d575b61208b57611de1338460243585613d0e565b60405192611dee84612f6c565b3384526020840160643581526040850190608435825260608601916044358352611e1c608088019485613583565b60a08701926001845260c088019060c435825260e089019260e4358452611e4b36896004013560248b016132c4565b6101008b01526001600160a01b038981166000908152600a60209081526040808320602435845290915290208b5181546001600160a01b03191692169190911781559451600186015551600280860191909155905160038501559451600484019581101561077457856007956117d7611ec7936101009961358f565b516005830155516006820155019301519283516001600160401b038111610d6057611ef28254612f17565b601f8111612043575b50602094601f8211600114611fde57948192939495600092611fd3575b50508160011b916000199060031b1c19161790555b611f386001546135a7565b600155611f816040519160643583526084356020840152604435604084015260c435606084015260e435608084015260e060a084015260e08301906024816004013591016135b6565b4660c0830152602435926001600160a01b03169133917f2b1fcac0dc1e88d0492d102c7c7c9807d150c1d1d37d23ac1356c943596f422c919081900390a46001600080516020614f5583398151915255005b015190508580611f18565b601f1982169583600052806000209160005b88811061202b57508360019596979810612012575b505050811b019055611f2d565b015160001960f88460031b161c19169055858080612005565b91926020600181928685015181550194019201611ff0565b826000526020600020601f830160051c81019160208410612081575b601f0160051c01905b8181106120755750611efb565b60008155600101612068565b909150819061205f565b50604080516001600160a01b0392909216825260243560208301526060908201819052601190820152700496e76616c69642074696d657374616d7607c1b60808201523390600080516020614ef58339815191529060a090a260405162461bcd60e51b81526020600482015260116024820152700496e76616c69642074696d657374616d7607c1b6044820152606490fd5b5061212a60035442613560565b60e43511611dcf565b5060c43560e4351115611dc8565b50604080516001600160a01b039290921682526024356020830152606090820181905260139082015272496e76616c69642070726963652072616e676560681b60808201523390600080516020614ef58339815191529060a090a260405162461bcd60e51b8152602060048201526013602482015272496e76616c69642070726963652072616e676560681b6044820152606490fd5b5060643515611db8565b346103c25760003660031901126103c257600080516020614ed5833981519152546040516001600160a01b039091168152602090f35b346103c25760403660031901126103c257612230612eed565b60243561223b61387f565b612243613855565b60018060a01b0382169182600052600b6020526040600020826000526020526040600020906005820180549061227e60ff8360081c1661351f565b60068401544211156123ae5761ff00198216905560038301805490926001600160a01b03909116908161230b57505050507f78704b361450701e7c8b2731aa726084c803f3eb77e8ff2256874d0ea5a0a90f6060600093600460018060a01b038554169401546040519182526020820152846040820152a45b6001600080516020614f5583398151915255005b84612371879361235b986123a6946102fc60027f78704b361450701e7c8b2731aa726084c803f3eb77e8ff2256874d0ea5a0a90f9a9b015498899461234f856138bb565b600489019e8f546138bb565b89546001600160a01b03169360ff1691866138e6565b5054945495546040805193845260208401919091528201929092526001600160a01b0394851694909316929081906060820190565b0390a46122f7565b60405162461bcd60e51b8152602060048201526011602482015270105d58dd1a5bdb881b9bdd08195b991959607a1b6044820152606490fd5b346103c2576103f06123f8366130cb565b9261240492919261381f565b61339c565b346103c25760003660031901126103c2577fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d100541580612694575b1561265757604051600080516020614eb58339815191525481600061246783612f17565b808352926001811690811561263857506001146125cb575b61248b92500382612f88565b6040516000600080516020614f15833981519152546124a981612f17565b80845290600181169081156125a85750600114612541575b5060e0926124d48361253d930384612f88565b61251c60209361250e604051936124eb8786612f88565b600085526000368137604051978897600f60f81b895288015260e087019061305a565b90858203604087015261305a565b90466060850152306080850152600060a085015283820360c085015261311b565b0390f35b600080516020614f15833981519152600090815291507f5f9ce34815f8e11431c7bb75a8e6886a91478f7ffc1dbb0a98dc240fddd76b755b81831061258e575050810160200160e06124c1565b600181602092949394548385880101520191019190612579565b60ff191660208581019190915291151560051b8401909101915060e090506124c1565b50600080516020614eb5833981519152600090815290917f42ad5d3e1f2e6e70edcf6d991b8a3023d3fca8047a131592f9edb9fd9b89d57d5b81831061261c57505090602061248b9282010161247f565b6020919350806001915483858801015201910190918392612604565b6020925061248b94915060ff191682840152151560051b82010161247f565b60405162461bcd60e51b81526020600482015260156024820152741152540dcc4c8e88155b9a5b9a5d1a585b1a5e9959605a1b6044820152606490fd5b507fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d1015415612443565b346103c25760003660031901126103c2576126d661381f565b6126de613855565b600160ff19600080516020614f35833981519152541617600080516020614f35833981519152557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a1005b346103c25760203660031901126103c2576001600160a01b03612752612eed565b16600052600c6020526020604060002054604051908152f35b346103c25760003660031901126103c25761278461381f565b600080516020614ed583398151915280546001600160a01b031981169091556000906001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3005b346103c2576127e3366130cb565b6004549092906001600160a01b03163303612801576103f09361339c565b60405162461bcd60e51b815260206004820152601960248201527f4f6e6c7920636c65616e757020626f742063616e2063616c6c000000000000006044820152606490fd5b346103c25760403660031901126103c25761285f612eed565b6024356001600160401b0381116103c25761287e90369060040161309b565b90916001600160a01b03168015612a865761289d600254831115613316565b6128a68261314f565b926128b46040519485612f88565b828452601f196128c38461314f565b0160005b818110612a5357505060005b83811061294a578460405180916020820160208352815180915260206040840192019060005b818110612907575050500390f35b9193509160206080600192606087518051151583528481015185840152858060a01b036040820151166040840152015160608201520194019101918493926128f9565b6001908360005260086020526040600020612966828786613362565b356000526020526040600020846000526009602052604060002061298b838887613362565b3560005260205260ff604060002054169081612a41575b81612a32575b60008215612a2c575083810154905b60008315612a265750848060a01b03815416905b6000908415612a1f57600491500154915b604051936129e985612f51565b151584526020840152848060a01b031660408301526060820152612a0d8288613388565b52612a188187613388565b50016128d3565b50916129dc565b906129cb565b906129b7565b600481015442111591506129a8565b600381015460081c60ff1691506129a2565b602090604051612a6281612f51565b600081526000838201526000604082015260006060820152828289010152016128c7565b60405162461bcd60e51b8152602060048201526018602482015277496e76616c696420636f6e7472616374206164647265737360401b6044820152606490fd5b346103c25760003660031901126103c257602060ff600080516020614f3583398151915254166040519015158152f35b346103c25760403660031901126103c2576001600160a01b03612b17612eed565b16600052600b6020526040600020602435600052602052610100604060002060018060a01b038154169060ff600182015491600281015460018060a01b0360038301541660048301549160066005850154940154956040519788526020880152604087015260608601526080850152612b9560a0850183831661304d565b60081c16151560c083015260e0820152f35b346103c25760003660031901126103c257612bc061381f565b600080516020614f358339815191525460ff811615612c195760ff1916600080516020614f35833981519152557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a1005b638dfc202b60e01b60005260046000fd5b346103c25760203660031901126103c257600435612c4661381f565b8015612c5157600255005b60405162461bcd60e51b8152602060048201526012602482015271496e76616c69642062617463682073697a6560701b6044820152606490fd5b346103c25760003660031901126103c2576020600254604051908152f35b346103c25760003660031901126103c2576004546040516001600160a01b039091168152602090f35b346103c25760003660031901126103c2576020600354604051908152f35b346103c25760203660031901126103c257612d09612eed565b612d1161381f565b6001600160a01b03168015612d38576001600160601b0360a01b6004541617600455600080f35b60405162461bcd60e51b8152602060048201526013602482015272496e76616c696420626f74206164647265737360681b6044820152606490fd5b346103c25760403660031901126103c2576001600160a01b03612d94612eed565b16600052600a6020526040600020602435600052602052604060002060018060a01b0381541661253d600183015492600281015490600381015460ff6004830154600584015492612dec600760068701549601612fa9565b95604051998a998a5260208a015260408901526060880152612e136080880183831661304d565b60081c16151560a086015260c085015260e084015261012061010084015261012083019061305a565b346103c25760403660031901126103c2576001600160a01b03612e5d612eed565b1660005260086020526040600020602435600052602052604060002060018060a01b0381541661253d60018301549260028101549060ff6003820154612eaa600560048501549401612fa9565b93604051978897885260208801526040870152612ecc6060870183831661304d565b60081c161515608085015260a084015260e060c084015260e083019061305a565b600435906001600160a01b03821682036103c257565b35906001600160a01b03821682036103c257565b90600182811c92168015612f47575b6020831014612f3157565b634e487b7160e01b600052602260045260246000fd5b91607f1691612f26565b608081019081106001600160401b03821117610d6057604052565b61012081019081106001600160401b03821117610d6057604052565b90601f801991011681019081106001600160401b03821117610d6057604052565b9060405191826000825492612fbd84612f17565b808452936001811690811561302b5750600114612fe4575b50612fe292500383612f88565b565b90506000929192526020600020906000915b81831061300f575050906020612fe29282010138612fd5565b6020919350806001915483858901015201910190918492612ff6565b905060209250612fe294915060ff191682840152151560051b82010138612fd5565b9060028210156107745752565b919082519283825260005b848110613086575050826000602080949584010152601f8019910116010190565b80602080928401015182828601015201613065565b9181601f840112156103c2578235916001600160401b0383116103c2576020808501948460051b0101116103c257565b60406003198201126103c2576004356001600160401b0381116103c257816130f59160040161309b565b92909291602435906001600160401b0382116103c2576131179160040161309b565b9091565b906020808351928381520192019060005b8181106131395750505090565b825184526020938401939092019160010161312c565b6001600160401b038111610d605760051b60200190565b9291906131728161314f565b936131806040519586612f88565b602085838152019160051b81019283116103c257905b8282106131a257505050565b8135815260209182019101613196565b9080601f830112156103c2578160206131cd93359101613166565b90565b919091610120818403126103c257604051906131eb82612f6c565b81936131f682612f03565b835261320460208301612f03565b602084015260408201356001600160401b0381116103c257816132289184016131b2565b604084015260608201356001600160401b0381116103c2578161324c9184016131b2565b60608401526080820135906001600160401b0382116103c2576132709183016131b2565b608083015260a081013560028110156103c25761010091829160a085015260c081013560c085015260e081013560e08501520135910152565b6001600160401b038111610d6057601f01601f191660200190565b9291926132d0826132a9565b916132de6040519384612f88565b8294818452818301116103c2578281602093846000960137010152565b9080601f830112156103c2578160206131cd933591016132c4565b1561331d57565b60405162461bcd60e51b815260206004820152601860248201527f457863656564732062617463682073697a65206c696d697400000000000000006044820152606490fd5b91908110156133725760051b0190565b634e487b7160e01b600052603260045260246000fd5b80518210156133725760209160051b010190565b9192906133a7613855565b6133b2828214613686565b60005b8181106133c3575050505050565b6133ce818386613362565b356001600160a01b03811691908290036103c2576001916133f0828689613362565b3590806000526008602052604060002082600052602052604060002081600052600a60205260406000208360005260205260406000206003820180549060ff8260081c169182613511575b600484019160ff835460081c169182613503575b8485156134fc575b61346b575b505050505050505050016133b5565b846134ee575b50506134df575b506134838754613679565b8755156134cf5750848060a01b039054165b848060a01b03167fe1bfe61cb157e0896411ccf9a5c40e4c346f7bb6e1d2a44de4a724f0cb5c6fb0600080a438808080808080808061345c565b9050848060a01b03905416613495565b805461ff001916905538613478565b61ff00191690553880613471565b5082613457565b60068601544211925061344f565b60048501544211925061343b565b1561352657565b60405162461bcd60e51b815260206004820152601260248201527141756374696f6e206e6f742061637469766560701b6044820152606490fd5b9190820180921161356d57565b634e487b7160e01b600052601160045260246000fd5b60028210156107745752565b9060028110156107745760ff80198354169116179055565b600019811461356d5760010190565b908060209392818452848401376000828201840152601f01601f1916010190565b156135de57565b60405162461bcd60e51b815260206004820152600e60248201526d125b9d985b1a5908185b5bdd5b9d60921b6044820152606490fd5b91908110156133725760051b81013590601e19813603018212156103c25701908135916001600160401b0383116103c25760200182360381136103c2579190565b81835290916001600160fb1b0383116103c25760209260051b809284830137010190565b801561356d576000190190565b1561368d57565b60405162461bcd60e51b8152602060048201526015602482015274082e4e4c2f240d8cadccee8d040dad2e6dac2e8c6d605b1b6044820152606490fd5b156136d157565b60405162461bcd60e51b8152602060048201526011602482015270496e76616c6964207369676e617475726560781b6044820152606490fd5b8181029291811591840414171561356d57565b1561372457565b60405162461bcd60e51b8152602060048201526014602482015273125b9cdd59999a58da595b9d081c185e5b595b9d60621b6044820152606490fd5b9190820391821161356d57565b1561377457565b60405162461bcd60e51b815260206004820152600d60248201526c105d58dd1a5bdb88195b991959609a1b6044820152606490fd5b6001600160a01b0316801561380957600080516020614ed583398151915280546001600160a01b0319811683179091556001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3565b631e4fbdf760e01b600052600060045260246000fd5b600080516020614ed5833981519152546001600160a01b0316330361384057565b63118cdaa760e01b6000523360045260246000fd5b60ff600080516020614f35833981519152541661386e57565b63d93c066560e01b60005260046000fd5b6002600080516020614f5583398151915254146138aa576002600080516020614f5583398151915255565b633ee5aeb560e01b60005260046000fd5b6040908151916138cb8184612f88565b600183526020830190601f1901368237825115613372575290565b91909594939260028110156107745761399457506001600160a01b03169360005b815181101561398c5761391a8183613388565b5190863b156103c257604051632142170760e11b81526001600160a01b038681166004830152851660248201526044810192909252600082606481838b5af19182156139805760019261396f575b5001613907565b600061397a91612f88565b38613968565b6040513d6000823e3d90fd5b505050509050565b946001600160a01b039091169060005b8151811015613a3b576139b78183613388565b51906139c38189613388565b51843b156103c257604051637921219560e11b81526001600160a01b038881166004830152871660248201526044810193909352606483015260a06084830152600060a483018190528260c48183885af191821561398057600192613a2a575b50016139a4565b6000613a3591612f88565b38613a23565b50505050509050565b51906001600160a01b03821682036103c257565b9392909181613a6791846148de565b6000805460405163478e4cc960e11b8152600481018690526001600160a01b0396871660248201529591949293929160209187916044918391165afa948515613bba578495613c45575b5084613ac084613ac593613760565b613760565b958680613c2c575b505081613bc5575b50509080613ae1575050565b815460405163624ec8ef60e11b81526001600160a01b0390911690602081600481855afa908115613bba578491613b80575b50813b15613b7c57604051637ea5a8c960e01b81526001600160a01b03919091166004820152602481019290925282908290604490829084905af18015613b7157613b5c575050565b613b67828092612f88565b613b6e5750565b80fd5b6040513d84823e3d90fd5b8380fd5b90506020813d602011613bb2575b81613b9b60209383612f88565b81010312613b7c57613bac90613a44565b38613b13565b3d9150613b8e565b6040513d86823e3d90fd5b82546001600160a01b031690813b15613b7c57604051637ea5a8c960e01b81526001600160a01b03919091166004820152602481019290925282908290604490829084905af18015613b7157613c1c575b80613ad5565b81613c2691612f88565b38613c16565b613c3e916001600160a01b03166147fb565b3886613acd565b90919294506020813d602011613c77575b81613c6360209383612f88565b81010312613b7c5751939190613ac5613ab1565b3d9150613c56565b15613c8657565b60405162461bcd60e51b815260206004820152600d60248201526c2737ba1027232a1037bbb732b960991b6044820152606490fd5b908160209103126103c2575180151581036103c25790565b15613cda57565b60405162461bcd60e51b815260206004820152600c60248201526b139bdd08185c1c1c9bdd995960a21b6044820152606490fd5b909291613d1b8183614ab3565b15613fc857600281101561077457613ed0576040516331a9108f60e11b8152600481018490526001600160a01b03919091169290602081602481875afa90811561398057600091613e8a575b50613db592602091613d86906001600160a01b03808416911614613c7f565b60405163e985e9c560e01b81526001600160a01b03909116600482015230602482015292839081906044820190565b0381865afa91821561398057600092613e59575b508115613ddc575b50612fe29150613cd3565b6020915060246040518094819363020604bf60e21b835260048301525afa801561398057600090613e1e575b612fe291506001600160a01b0316301438613dd1565b506020813d602011613e51575b81613e3860209383612f88565b810103126103c257613e4c612fe291613a44565b613e08565b3d9150613e2b565b613e7c91925060203d602011613e83575b613e748183612f88565b810190613cbb565b9038613dc9565b503d613e6a565b90506020813d602011613ec8575b81613ea560209383612f88565b810103126103c257613db592613d86613ebf602093613a44565b92505092613d67565b3d9150613e98565b604051627eeac760e11b81526001600160a01b0383811660048301526024820194909452921691602081604481865afa90811561398057600091613f92575b5090613f5792613f2460016020941015613c7f565b60405163e985e9c560e01b81526001600160a01b0390921660048301523060248301529092839190829081906044820190565b03915afa801561398057612fe291600091613f73575b50613cd3565b613f8c915060203d602011613e8357613e748183612f88565b38613f6d565b9190506020823d602011613fc0575b81613fae60209383612f88565b810103126103c2579051613f57613f0f565b3d9150613fa1565b60405162461bcd60e51b8152602060048201526014602482015273125b9d985b1a59081391950818dbdb9d1c9858dd60621b6044820152606490fd5b6001600160a01b0316969590949391929087156144ac57855180159081156144a0575b5061440f5785518451811491821592614403575b82156143f7575b5050614360576002821015938461077457600160ff8416116142d357804210908115916142be575b506142215760005b8551811015614217576140858183613388565b5115614167576140958185613388565b511585811561413e575b506140ac57600101614072565b6140b7889187613388565b51604051918252602082015260606040820152600080516020614ef583398151915233918061410560608201604090600e81526d125b9d985b1a5908185b5bdd5b9d60921b60208201520190565b0390a260405162461bcd60e51b815260206004820152600e60248201526d125b9d985b1a5908185b5bdd5b9d60921b6044820152606490fd5b905061077457821580614152575b8561409f565b50600161415f8286613388565b51141561414c565b614172889187613388565b51604051918252602082015260606040820152600080516020614ef58339815191523391806141cf60608201604090601c81527f5072696365206d7573742062652067726561746572207468616e20300000000060208201520190565b0390a260405162461bcd60e51b815260206004820152601c60248201527f5072696365206d7573742062652067726561746572207468616e2030000000006044820152606490fd5b5050505050509050565b866040519081526000602082015260606040820152600080516020614ef583398151915233918061427b6060820160409060188152770496e76616c6964206578706972792074696d657374616d760441b60208201520190565b0390a260405162461bcd60e51b81526020600482015260186024820152770496e76616c6964206578706972792074696d657374616d760441b6044820152606490fd5b90506142cc60035442613560565b103861406a565b876040519081526000602082015260606040820152600080516020614ef583398151915233918061432560608201604090601081526f125b9d985b1a59081cdd185b99185c9960821b60208201520190565b0390a260405162461bcd60e51b815260206004820152601060248201526f125b9d985b1a59081cdd185b99185c9960821b6044820152606490fd5b866040519081526000602082015260606040820152600080516020614ef58339815191523391806143b7606082016040906015815274082e4e4c2f240d8cadccee8d040dad2e6dac2e8c6d605b1b60208201520190565b0390a260405162461bcd60e51b8152602060048201526015602482015274082e4e4c2f240d8cadccee8d040dad2e6dac2e8c6d605b1b6044820152606490fd5b51141590503880614042565b8351821415925061403b565b876040519081526000602082015260606040820152600080516020614ef5833981519152339180614463606082016040906012815271496e76616c69642062617463682073697a6560701b60208201520190565b0390a260405162461bcd60e51b8152602060048201526012602482015271496e76616c69642062617463682073697a6560701b6044820152606490fd5b90506002541038614027565b876040519081526000602082015260606040820152600080516020614ef5833981519152339180614506606082016040906018815277496e76616c696420636f6e7472616374206164647265737360401b60208201520190565b0390a260405162461bcd60e51b8152602060048201526018602482015277496e76616c696420636f6e7472616374206164647265737360401b6044820152606490fd5b805160209091019060005b8181106145615750505090565b8251845260209384019390920191600101614554565b60018060a01b0381511660018060a01b0360208301511660408301516040516145b6816145a8602082018095614549565b03601f198101835282612f88565b51902060608401516040516145d3816145a8602082018095614549565b5190209360808101516040516145f1816145a8602082018095614549565b5190209460a08201519560028710156107745760429661466c9160c08501519361010060e0870151960151966040519860208a019a7fca91f495274a684388af1db8e9f2213bf5c4534b07ebae420eaca01de553efe88c5260408b015260608a0152608089015260a088015260c087015260e086019061304d565b610100840152610120830152610140820152610140815261468f61016082612f88565b51902061469a614c75565b6146a2614da7565b6040519060208201927f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f8452604083015260608201524660808201523060a082015260a081526146f360c082612f88565b519020906040519161190160f01b8352600283015260228201522090565b9190823b61474c579061472391614b82565b5060048110156107745715918261473957505090565b6001600160a01b03918216911614919050565b916000926145a861478385946040519283916020830195630b135d3f60e11b8752602484015260406044840152606483019061305a565b51915afa61478f6147cb565b816147bd575b8161479e575090565b90506020818051810103126103c25760200151630b135d3f60e11b1490565b905060208151101590614795565b3d156147f6573d906147dc826132a9565b916147ea6040519384612f88565b82523d6000602084013e565b606090565b814710614847576000918291829182916001600160a01b03165af161481e6147cb565b90156148275750565b80511561483657805190602001fd5b63d6bda27560e01b60005260046000fd5b504763cf47918160e01b60005260045260245260446000fd5b92909391818310156148ba576148868161488061488d9361489296613760565b93613760565b9484613760565b61370a565b9180156148a4576131cd920490613760565b634e487b7160e01b600052601260045260246000fd5b5050505090565b91908260409103126103c25760206148d883613a44565b92015190565b6040516301ffc9a760e01b815263152a902d60e11b6004820152919392916001600160a01b0390911690602081602481855afa90811561398057600091614a94575b5061499b57506000546040805163152a902d60e11b815260048101959095526024850192909252839060449082906001600160a01b03165afa9182156139805760009060009361496f57509190565b905061311791925060403d604011614994575b61498c8183612f88565b8101906148c1565b503d614982565b60405163152a902d60e11b8152846004820152826024820152604081604481855afa9081600091600093614a71575b50614a69575050907f38d745faf78da8fabd6852cff6abfd62331077fc5858edf414f373bb077950056040926149fe6147cb565b90614a1f85519283928352886020840152606087840152606083019061305a565b0390a1600054825163152a902d60e11b815260048101959095526024850191909152839060449082906001600160a01b03165afa9182156139805760009060009361496f57509190565b945092915050565b909250614a8d915060403d6040116149945761498c8183612f88565b91386149ca565b614aad915060203d602011613e8357613e748183612f88565b38614920565b6001600160a01b031660008181526007602052604090205490919060ff16614b7b57600281101561077457614b6d576380ac58cd60e01b905b6040516301ffc9a760e01b81526001600160e01b03199092166004830152602082602481845afa60009281614b4c575b50614b28575050600090565b81614b31575090565b60005260076020526040600020600160ff1982541617905590565b614b6691935060203d602011613e8357613e748183612f88565b9138614b1c565b636cdb3d1360e11b90614aec565b5050600190565b8151919060418303614bb357614bac92506020820151906060604084015193015160001a90614bec565b9192909190565b505060009160029190565b60ff600080516020614f758339815191525460401c1615614bdb57565b631afcd79f60e31b60005260046000fd5b91907f7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a08411614c69579160209360809260ff60009560405194855216868401526040830152606082015282805260015afa15613980576000516001600160a01b03811615614c5d5790600090600090565b50600090600190600090565b50505060009160039190565b604051600080516020614eb58339815191525490600081614c9584612f17565b918282526020820194600181169081600014614d8b5750600114614d1e575b614cc092500382612f88565b51908115614ccc572090565b50507fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d100548015614cf95790565b507fc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a47090565b50600080516020614eb5833981519152600090815290917f42ad5d3e1f2e6e70edcf6d991b8a3023d3fca8047a131592f9edb9fd9b89d57d5b818310614d6f575050906020614cc092820101614cb4565b6020919350806001915483858801015201910190918392614d57565b60ff1916865250614cc092151560051b82016020019050614cb4565b604051600080516020614f158339815191525490600081614dc784612f17565b918282526020820194600181169081600014614e985750600114614e2b575b614df292500382612f88565b51908115614dfe572090565b50507fa16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d101548015614cf95790565b50600080516020614f15833981519152600090815290917f5f9ce34815f8e11431c7bb75a8e6886a91478f7ffc1dbb0a98dc240fddd76b755b818310614e7c575050906020614df292820101614de6565b6020919350806001915483858801015201910190918392614e64565b60ff1916865250614df292151560051b82016020019050614de656fea16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d1029016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300e9edab15d8a727f5c1558fdcaf2ec1cfc2c3f84706e9ddbd01ec6edc5db9b7a0a16a46d94261c7517cc8ff89f61c0ce93598e3c849801011dee649a6a557d103cd5ed15c6e187e77e9aee88184c21f4f2182ab5827cb3b7e07fbedcd63f033009b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00f0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00a264697066735822122017d0c2d089bf7791c638edf2f8a755627468247fbbace5fa3d162a8370bd740264736f6c634300081c0033",
  "linkReferences": {},
  "deployedLinkReferences": {}
};