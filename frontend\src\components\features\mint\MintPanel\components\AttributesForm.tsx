"use client";

import React, { useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Trash2 } from "lucide-react";
import { AttributeInput } from "@/lib/api/graphql/generated";

interface AttributesFormProps {
  attributes: AttributeInput[];
  setAttributes: (attributes: AttributeInput[]) => void;
  attributesError?: string;
}

export function AttributesForm({
  attributes,
  setAttributes,
  attributesError,
}: AttributesFormProps) {
  const handleAttributeChange = useCallback(
    (index: number, field: "trait_type" | "value", value: string) => {
      const newAttributes = [...attributes];
      newAttributes[index][field] = value.slice(0, 50);
      setAttributes(newAttributes);
    },
    [attributes, setAttributes]
  );

  const removeAttribute = useCallback(
    (index: number) => {
      setAttributes(attributes.filter((_, i) => i !== index));
    },
    [attributes, setAttributes]
  );

  const addAttribute = () => {
    if (attributes.length < 10) {
      setAttributes([...attributes, { trait_type: "", value: "" }]);
    }
  };

  return (
    <div className="space-y-3">
      <ScrollArea className="h-[150px]">
        {attributes.map((attr, index) => (
          <div key={index} className="flex gap-2 items-center mb-2">
            <Input
              value={attr.trait_type}
              onChange={(e) =>
                handleAttributeChange(index, "trait_type", e.target.value)
              }
              placeholder="Trait Type"
              className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
              aria-label={`Trait type ${index + 1}`}
            />
            <Input
              value={attr.value}
              onChange={(e) =>
                handleAttributeChange(index, "value", e.target.value)
              }
              placeholder="Value"
              className="h-10 text-sm bg-[#0f0a19] border-gray-800/50 text-white"
              aria-label={`Trait value ${index + 1}`}
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => removeAttribute(index)}
              className="h-10 w-10 text-gray-400 hover:text-red-400 hover:bg-red-900/20"
              aria-label={`Remove attribute ${index + 1}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}

        {attributesError ? (
          <p className="text-sm text-red-400">{attributesError}</p>
        ) : (
          <p className="text-sm text-gray-400">
            Trait types must be unique. Both fields are required for each
            attribute. Max 10 attributes.
          </p>
        )}
      </ScrollArea>

      <Button
        onClick={addAttribute}
        disabled={attributes.length >= 10}
        variant="outline"
        className="w-full h-10 border-gray-800/50 text-gray-300 hover:bg-gray-800/50 text-sm"
        aria-label="Add new attribute"
      >
        Add Attribute
      </Button>
    </div>
  );
}
