import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

/** Action type of the collection */
export enum ActionType {
  Create = 'CREATE',
  Delete = 'DELETE',
  Update = 'UPDATE'
}

export type AllowlistStage = {
  __typename?: 'AllowlistStage';
  durationDays: Scalars['String']['output'];
  durationHours: Scalars['String']['output'];
  endDate: Scalars['String']['output'];
  mintPrice: Scalars['String']['output'];
  stageId: Scalars['String']['output'];
  startDate: Scalars['String']['output'];
  wallets: Array<Scalars['String']['output']>;
};

export type AllowlistStageInput = {
  durationDays: Scalars['String']['input'];
  durationHours: Scalars['String']['input'];
  mintPrice: Scalars['String']['input'];
  startDate: Scalars['String']['input'];
  wallets: Array<Scalars['String']['input']>;
};

/** Type of artwork in the collection */
export enum ArtType {
  Same = 'SAME',
  Unique = 'UNIQUE'
}

export enum AssetType {
  Item = 'ITEM',
  Nft = 'NFT',
  Token = 'TOKEN'
}

export type Attribute = {
  __typename?: 'Attribute';
  trait_type: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type AttributeInput = {
  trait_type: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type AuthVerifyInput = {
  message: Scalars['String']['input'];
  refreshToken?: InputMaybe<Scalars['String']['input']>;
  signature: Scalars['String']['input'];
};

export type AuthVerifyResponse = {
  __typename?: 'AuthVerifyResponse';
  accessToken: Scalars['String']['output'];
  refreshToken: Scalars['String']['output'];
};

export type Bid = {
  __typename?: 'Bid';
  amount: Scalars['String']['output'];
  bidder: Scalars['String']['output'];
  timestamp: Scalars['DateTime']['output'];
};

export type Collection = {
  __typename?: 'Collection';
  allowlistStages?: Maybe<Array<AllowlistStage>>;
  artType: ArtType;
  chain: Scalars['String']['output'];
  chainId: Scalars['String']['output'];
  contractAddress: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  creatorId: Scalars['String']['output'];
  creatorRole: UserRole;
  currency: Currency;
  description: Scalars['String']['output'];
  id: Scalars['String']['output'];
  image: Scalars['String']['output'];
  isVerified: Scalars['Boolean']['output'];
  maxSupply: Scalars['String']['output'];
  mintLimit: Scalars['String']['output'];
  mintPrice: Scalars['String']['output'];
  mintStartDate: Scalars['String']['output'];
  name: Scalars['String']['output'];
  previewImages: Array<Scalars['String']['output']>;
  publicMint: PublicMint;
  royaltyFee: Scalars['String']['output'];
  status: CollectionStatus;
  tokenStandard: TokenStandard;
  totalMinted: Scalars['String']['output'];
  uri: Scalars['String']['output'];
};

export type CollectionModifiedPrivateInput = {
  chainId: Scalars['String']['input'];
  wallet: Scalars['String']['input'];
};

export type CollectionModifiedPublicInput = {
  chainId: Scalars['String']['input'];
};

export type CollectionModifiedRealtimeResponse = {
  __typename?: 'CollectionModifiedRealtimeResponse';
  action: ActionType;
  data: Collection;
};

/** Status of a collection */
export enum CollectionStatus {
  Approved = 'APPROVED',
  Deployed = 'DEPLOYED',
  Ended = 'ENDED',
  Pending = 'PENDING'
}

export type CollectionsResponse = {
  __typename?: 'CollectionsResponse';
  collections: Array<Collection>;
  pagination: Pagination;
  stats: Stats;
};

export type ConfirmListingNftInput = {
  /** Chain ID */
  chainId: Scalars['String']['input'];
  /** List of listed NFTs */
  listings: Array<DataListNftInput>;
  /** Nonce */
  nonce: Scalars['String']['input'];
  /** EIP712 signature (for off-chain listing) */
  signature?: InputMaybe<Scalars['String']['input']>;
  /** Transaction hash */
  txHash: Scalars['String']['input'];
  /** Wallet address of the seller */
  walletAddress: Scalars['String']['input'];
};

export type ConfirmListingNftResponse = {
  __typename?: 'ConfirmListingNftResponse';
  /** Error message if confirmation failed */
  errorMessage?: Maybe<Scalars['String']['output']>;
  /** IDs of created listings */
  listingIds?: Maybe<Array<Scalars['String']['output']>>;
  /** Whether confirmation was successful */
  success: Scalars['Boolean']['output'];
};

export type CreateCollectionInput = {
  /** Allowlist stages */
  allowlistStages: Array<AllowlistStageInput>;
  /** Art type: SAME or UNIQUE */
  artType: ArtType;
  /** Blockchain name (e.g., Ethereum, Polygon) */
  chain: Scalars['String']['input'];
  /** Chain ID (e.g., 1, 137) */
  chainId: Scalars['String']['input'];
  /** Optional contract address */
  contractAddress?: InputMaybe<Scalars['String']['input']>;
  currency?: Currency;
  /** Collection description */
  description: Scalars['String']['input'];
  /** IPFS URI for main image: ipfs://... */
  image: Scalars['String']['input'];
  /** Maximum supply of NFTs */
  maxSupply: Scalars['String']['input'];
  /** Mint limit per wallet */
  mintLimit: Scalars['String']['input'];
  /** Mint price in native token (e.g., 0.1) */
  mintPrice: Scalars['String']['input'];
  /** Mint start date (ISO format) */
  mintStartDate: Scalars['String']['input'];
  /** Collection name */
  name: Scalars['String']['input'];
  /** Public mint phase */
  publicMint: PublicMintInput;
  /** Royalty fee percentage (e.g., 5.0) */
  royaltyFee: Scalars['String']['input'];
  /** Token standard: ERC721 or ERC1155 */
  tokenStandard: TokenStandard;
  /** Optional transaction hash */
  txHash?: InputMaybe<Scalars['String']['input']>;
  /** IPFS URI for metadata: ipfs://... */
  uri: Scalars['String']['input'];
};

export type CreateCollectionResponse = {
  __typename?: 'CreateCollectionResponse';
  collectionId?: Maybe<Scalars['String']['output']>;
  contractAddress?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  steps?: Maybe<Array<Step>>;
};

export enum Currency {
  Eth = 'ETH',
  Sol = 'SOL',
  Usdc = 'USDC',
  Usdt = 'USDT'
}

export type DataListNftInput = {
  /** Amount of NFTs to list (required for ERC1155, 1 for ERC721) */
  amount?: InputMaybe<Scalars['String']['input']>;
  /** Chain ID (e.g., 1 for Ethereum, 137 for Polygon) */
  chainId: Scalars['String']['input'];
  /** NFT contract address */
  contractAddress: Scalars['String']['input'];
  /** Unix timestamp when listing expires */
  expiryTimestamp: Scalars['String']['input'];
  /** Listing price in wei (ETH) */
  listingPrice: Scalars['String']['input'];
  /** Listing type: onchain or offchain */
  listingType?: Scalars['String']['input'];
  /** Metadata URI for the NFT (e.g., ipfs://Qm...) */
  metadataURI?: InputMaybe<Scalars['String']['input']>;
  /** Token standard: 0 for ERC721, 1 for ERC1155 */
  standard: Scalars['String']['input'];
  /** Token ID of the NFT */
  tokenId: Scalars['String']['input'];
};

export type DeListing = {
  __typename?: 'DeListing';
  chainId: Scalars['String']['output'];
  contractAddress: Scalars['String']['output'];
  tokenId: Scalars['String']['output'];
};

export type DeListingsError = {
  __typename?: 'DeListingsError';
  message: Scalars['String']['output'];
  tokenId: Scalars['String']['output'];
};

export type DeListingsInput = {
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  tokenIds: Array<Scalars['String']['input']>;
};

export type DeListingsResponse = {
  __typename?: 'DeListingsResponse';
  deListings: Array<DeListing>;
  errors?: Maybe<Array<DeListingsError>>;
  steps: Array<Step>;
};

export type EventData = {
  __typename?: 'EventData';
  buyer?: Maybe<Scalars['String']['output']>;
  errorMessage?: Maybe<Scalars['String']['output']>;
  marketplaceFee?: Maybe<Scalars['String']['output']>;
  owner: Scalars['String']['output'];
  price?: Maybe<Scalars['String']['output']>;
  royaltyFee?: Maybe<Scalars['String']['output']>;
  tokenId: Scalars['String']['output'];
};

export type FilterInput = {
  field: Scalars['String']['input'];
  operator: FilterOperator;
  value: Scalars['String']['input'];
};

/** Operator for filtering */
export enum FilterOperator {
  Eq = 'EQ',
  Exists = 'EXISTS',
  Gt = 'GT',
  Gte = 'GTE',
  In = 'IN',
  Like = 'LIKE',
  Lt = 'LT',
  Lte = 'LTE',
  Ne = 'NE'
}

export type GetActiveStageInput = {
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  wallet?: InputMaybe<Scalars['String']['input']>;
};

export type GetActiveStageResponse = {
  __typename?: 'GetActiveStageResponse';
  isPublicMint: Scalars['Boolean']['output'];
  mintPrice?: Maybe<Scalars['String']['output']>;
  stageId: Scalars['String']['output'];
};

export type GetCollectionsInput = {
  filters?: InputMaybe<Array<FilterInput>>;
  includeStats?: InputMaybe<Scalars['Boolean']['input']>;
  pagination?: InputMaybe<PaginationInput>;
  search?: InputMaybe<SearchInput>;
  sort?: InputMaybe<SortInput>;
};

export type GetMintCostInput = {
  amount: Scalars['String']['input'];
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  stageId?: InputMaybe<Scalars['String']['input']>;
  wallet?: InputMaybe<Scalars['String']['input']>;
};

export type GetNftsInput = {
  filters?: InputMaybe<Array<FilterInput>>;
  pagination: PaginationInput;
  search?: InputMaybe<SearchInput>;
  sort?: InputMaybe<SortInput>;
};

export type GetNonceListingInput = {
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  wallet: Scalars['String']['input'];
};

export type GetNonceListingResponse = {
  __typename?: 'GetNonceListingResponse';
  errorMessage?: Maybe<Scalars['String']['output']>;
  nonce: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export type GetPriceListingInput = {
  attributes?: InputMaybe<Array<AttributeInput>>;
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  priceType: PriceType;
};

export type GetPriceListingResponse = {
  __typename?: 'GetPriceListingResponse';
  errorMessage?: Maybe<Scalars['String']['output']>;
  listingPrice?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  updatedAt: Scalars['String']['output'];
};

export type ListNfTsInput = {
  /** Chain ID */
  chainId: Scalars['String']['input'];
  /** List of NFTs to list */
  items: Array<DataListNftInput>;
  /** Wallet address of the seller */
  walletAddress: Scalars['String']['input'];
};

export type ListNfTsResponse = {
  __typename?: 'ListNFTsResponse';
  /** List of errors (if any) */
  errors?: Maybe<Array<ListingError>>;
  /** List of successfully listed NFTs */
  listings: Array<ListingNft>;
  /** Overall status (full_success, partial_success, failed) */
  status: Scalars['String']['output'];
  /** List of transaction steps to execute */
  steps: Array<Step>;
};

export type ListingError = {
  __typename?: 'ListingError';
  /** Contract address of the failed NFT */
  contractAddress: Scalars['String']['output'];
  /** Error code (e.g., INVALID_PRICE, NOT_APPROVED) */
  errorCode?: Maybe<Scalars['String']['output']>;
  /** Error message */
  message: Scalars['String']['output'];
  /** Token ID of the failed NFT */
  tokenId: Scalars['String']['output'];
};

export type ListingNft = {
  __typename?: 'ListingNFT';
  /** Amount of NFTs listed (required for ERC1155, 1 for ERC721) */
  amount?: Maybe<Scalars['String']['output']>;
  /** NFT contract address */
  contractAddress: Scalars['String']['output'];
  /** Unix timestamp when listing expires */
  expiryTimestamp: Scalars['String']['output'];
  /** Listing price in wei (ETH) */
  listingPrice: Scalars['String']['output'];
  /** Listing type: onchain or offchain */
  listingType: Scalars['String']['output'];
  /** Metadata URI for the NFT */
  metadataURI?: Maybe<Scalars['String']['output']>;
  /** Token standard: 0 for ERC721, 1 for ERC1155 */
  standard: Scalars['String']['output'];
  /** Token ID of the listed NFT */
  tokenId: Scalars['String']['output'];
};

export type MintCostResponse = {
  __typename?: 'MintCostResponse';
  errorMessage?: Maybe<Scalars['String']['output']>;
  estimatedGas: Scalars['String']['output'];
  isPublicMint: Scalars['Boolean']['output'];
  mintPrice: Scalars['String']['output'];
  stageId?: Maybe<Scalars['String']['output']>;
  stageInfo?: Maybe<StageInfo>;
  success: Scalars['Boolean']['output'];
  totalPrice: Scalars['String']['output'];
};

export type MintNftInput = {
  amount: Scalars['String']['input'];
  batchAmounts?: InputMaybe<Array<Scalars['String']['input']>>;
  chain: Scalars['String']['input'];
  chainId: Scalars['String']['input'];
  collectionId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  currency?: Currency;
  isBatch: Scalars['Boolean']['input'];
  metadata?: InputMaybe<Array<NftMetadataInput>>;
  mintPrice: Scalars['String']['input'];
  nonce?: InputMaybe<Scalars['String']['input']>;
  owner: Scalars['String']['input'];
  signature?: InputMaybe<Scalars['String']['input']>;
  stageId?: InputMaybe<Scalars['String']['input']>;
  standard: TokenStandard;
  tokenUris: Array<Scalars['String']['input']>;
};

export type MintNftResponse = {
  __typename?: 'MintNftResponse';
  errorMessage?: Maybe<Scalars['String']['output']>;
  estimatedGas?: Maybe<Scalars['String']['output']>;
  maxSupply?: Maybe<Scalars['String']['output']>;
  mintPrice?: Maybe<Scalars['String']['output']>;
  stageId?: Maybe<Scalars['String']['output']>;
  stageType?: Maybe<Scalars['String']['output']>;
  steps: Array<Step>;
  success: Scalars['Boolean']['output'];
  totalMinted?: Maybe<Scalars['String']['output']>;
};

export type MintProgress = {
  __typename?: 'MintProgress';
  maxSupply: Scalars['String']['output'];
  totalMinted: Scalars['String']['output'];
};

export type MintProgressInput = {
  chainId: Scalars['String']['input'];
  collectionId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
};

export type ModifyCollectionDataInput = {
  attribute: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type ModifyCollectionInput = {
  collectionId: Scalars['String']['input'];
  data?: InputMaybe<Array<ModifyCollectionDataInput>>;
  delete?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  confirmListingNft: ConfirmListingNftResponse;
  createCollection: CreateCollectionResponse;
  deListNFTs: DeListingsResponse;
  listNFTs: ListNfTsResponse;
  logout: Scalars['Boolean']['output'];
  mintNft: MintNftResponse;
  modifyCollection: Scalars['Boolean']['output'];
  refreshToken: AuthVerifyResponse;
  submitMintTransaction: SubmitMintTransactionResponse;
  verify: AuthVerifyResponse;
};


export type MutationConfirmListingNftArgs = {
  input: ConfirmListingNftInput;
};


export type MutationCreateCollectionArgs = {
  input: CreateCollectionInput;
};


export type MutationDeListNfTsArgs = {
  input: DeListingsInput;
};


export type MutationListNfTsArgs = {
  input: ListNfTsInput;
};


export type MutationLogoutArgs = {
  wallet: Scalars['String']['input'];
};


export type MutationMintNftArgs = {
  input: MintNftInput;
};


export type MutationModifyCollectionArgs = {
  input: ModifyCollectionInput;
};


export type MutationRefreshTokenArgs = {
  refreshToken: Scalars['String']['input'];
};


export type MutationSubmitMintTransactionArgs = {
  input: SubmitMintTransactionInput;
};


export type MutationVerifyArgs = {
  input: AuthVerifyInput;
};

export type Nft = {
  __typename?: 'Nft';
  animation_url?: Maybe<Scalars['String']['output']>;
  attributes?: Maybe<Array<Attribute>>;
  auctionId?: Maybe<Scalars['String']['output']>;
  bids?: Maybe<Array<Bid>>;
  chain: Scalars['String']['output'];
  chainId: Scalars['String']['output'];
  collectionId: Scalars['String']['output'];
  contractAddress: Scalars['String']['output'];
  creator: Scalars['String']['output'];
  currency: Currency;
  description: Scalars['String']['output'];
  id: Scalars['String']['output'];
  image: Scalars['String']['output'];
  isFeatured: Scalars['Boolean']['output'];
  mintPrice: Scalars['String']['output'];
  name: Scalars['String']['output'];
  owner: Scalars['String']['output'];
  standard: TokenStandard;
  status: NftStatus;
  tokenId: Scalars['String']['output'];
  tokenUri: Scalars['String']['output'];
  txHash: Scalars['String']['output'];
};

export type NftListedEvent = {
  __typename?: 'NftListedEvent';
  chainId: Scalars['String']['output'];
  contractAddress: Scalars['String']['output'];
  currency: Currency;
  listingPrice: Scalars['String']['output'];
  nftId: Scalars['String']['output'];
  standard: TokenStandard;
  tokenId: Scalars['String']['output'];
  wallet?: Maybe<Scalars['String']['output']>;
};

export type NftListedFilter = {
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  wallet?: InputMaybe<Scalars['String']['input']>;
};

export type NftMetadataInput = {
  amount?: Scalars['String']['input'];
  animation_url?: InputMaybe<Scalars['String']['input']>;
  attributes: Array<AttributeInput>;
  description: Scalars['String']['input'];
  image?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type NftMintedEvent = {
  __typename?: 'NftMintedEvent';
  amounts: Array<Scalars['String']['output']>;
  contractAddress: Scalars['String']['output'];
  recipient: Scalars['String']['output'];
  tokenIds: Array<Scalars['String']['output']>;
  tokenUris: Array<Scalars['String']['output']>;
};

export type NftMintedEventInput = {
  contractAddress: Scalars['String']['input'];
  wallet: Scalars['String']['input'];
};

export type NftModifiedPrivateInput = {
  contractAddress: Scalars['String']['input'];
  wallet: Scalars['String']['input'];
};

export type NftModifiedPublicInput = {
  collectionId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
};

export type NftModifiedRealtimeResponse = {
  __typename?: 'NftModifiedRealtimeResponse';
  action: ActionType;
  data: Array<Nft>;
};

/** Status of an NFT */
export enum NftStatus {
  Auctioned = 'AUCTIONED',
  Cancelled = 'CANCELLED',
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  Listed = 'LISTED',
  Pending = 'PENDING'
}

export type NftsResponse = {
  __typename?: 'NftsResponse';
  nfts: Array<Nft>;
  pagination: Pagination;
};

export type Pagination = {
  __typename?: 'Pagination';
  cursor?: Maybe<Scalars['String']['output']>;
  hasMore: Scalars['Boolean']['output'];
  limit: Scalars['String']['output'];
  skip?: Maybe<Scalars['String']['output']>;
  total: Scalars['String']['output'];
};

export type PaginationInput = {
  cursor?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['String']['input'];
  skip?: InputMaybe<Scalars['String']['input']>;
};

export type PriceChangePercentage = {
  __typename?: 'PriceChangePercentage';
  currency: Currency;
  percentage: Scalars['String']['output'];
};

export type PriceHistory = {
  __typename?: 'PriceHistory';
  assetType: AssetType;
  chainId: Scalars['String']['output'];
  close: Scalars['String']['output'];
  contractAddress: Scalars['String']['output'];
  currency: Currency;
  high: Scalars['String']['output'];
  low: Scalars['String']['output'];
  open: Scalars['String']['output'];
  source?: Maybe<Source>;
  timestamp: Scalars['String']['output'];
  volume: Scalars['String']['output'];
};

export type PriceHistoryInput = {
  assetType?: InputMaybe<AssetType>;
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  timeRange?: InputMaybe<Scalars['String']['input']>;
};

/** Price type */
export enum PriceType {
  FloorPrice = 'FLOOR_PRICE',
  LastedPrice = 'LASTED_PRICE',
  TraitPrice = 'TRAIT_PRICE'
}

export type PublicMint = {
  __typename?: 'PublicMint';
  durationDays: Scalars['String']['output'];
  durationHours: Scalars['String']['output'];
  endDate: Scalars['String']['output'];
  mintPrice: Scalars['String']['output'];
  startDate: Scalars['String']['output'];
};

export type PublicMintInput = {
  durationDays: Scalars['String']['input'];
  durationHours: Scalars['String']['input'];
  mintPrice: Scalars['String']['input'];
  startDate: Scalars['String']['input'];
};

export type Query = {
  __typename?: 'Query';
  getActiveStage: GetActiveStageResponse;
  getCollection: Collection;
  getCollections: CollectionsResponse;
  getMintCost: MintCostResponse;
  getNfts: NftsResponse;
  getNonceListing: GetNonceListingResponse;
  getPriceChangePercentage: PriceChangePercentage;
  getPriceHistory: Array<PriceHistory>;
  getPriceListing: GetPriceListingResponse;
  getSignedUrl: Scalars['String']['output'];
  getUser: User;
  me: UserResponse;
  mintProgress: MintProgress;
  nonce: Scalars['String']['output'];
};


export type QueryGetActiveStageArgs = {
  input: GetActiveStageInput;
};


export type QueryGetCollectionArgs = {
  input: GetCollectionsInput;
};


export type QueryGetCollectionsArgs = {
  input: GetCollectionsInput;
};


export type QueryGetMintCostArgs = {
  input: GetMintCostInput;
};


export type QueryGetNftsArgs = {
  input: GetNftsInput;
};


export type QueryGetNonceListingArgs = {
  input: GetNonceListingInput;
};


export type QueryGetPriceChangePercentageArgs = {
  input: PriceHistoryInput;
};


export type QueryGetPriceHistoryArgs = {
  input: PriceHistoryInput;
};


export type QueryGetPriceListingArgs = {
  input: GetPriceListingInput;
};


export type QueryGetUserArgs = {
  userId: Scalars['String']['input'];
};


export type QueryMintProgressArgs = {
  input: MintProgressInput;
};

export type SearchInput = {
  fields: Array<Scalars['String']['input']>;
  value: Scalars['String']['input'];
};

export type SortInput = {
  field: Scalars['String']['input'];
  order: SortOrder;
};

/** Order for sorting */
export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum Source {
  External = 'EXTERNAL',
  Internal = 'INTERNAL',
  Onchain = 'ONCHAIN'
}

export type StageInfo = {
  __typename?: 'StageInfo';
  mintLimit: Scalars['String']['output'];
  mintedInStage: Scalars['String']['output'];
  stageId: Scalars['String']['output'];
};

export type Stats = {
  __typename?: 'Stats';
  artists: Scalars['String']['output'];
  artworks: Scalars['String']['output'];
  collectors: Scalars['String']['output'];
};

export type Step = {
  __typename?: 'Step';
  id: Scalars['String']['output'];
  params: Scalars['String']['output'];
};

export type SubmitMintTransactionInput = {
  chainId: Scalars['String']['input'];
  contractAddress: Scalars['String']['input'];
  txHash: Scalars['String']['input'];
};

export type SubmitMintTransactionResponse = {
  __typename?: 'SubmitMintTransactionResponse';
  amounts?: Maybe<Array<Scalars['String']['output']>>;
  collectionId?: Maybe<Scalars['String']['output']>;
  errorMessage?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  tokenIds?: Maybe<Array<Scalars['String']['output']>>;
  txHash?: Maybe<Scalars['String']['output']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  collectionModifiedPrivateRealtime: CollectionModifiedRealtimeResponse;
  collectionModifiedPublicRealtime: CollectionModifiedRealtimeResponse;
  mintProgressRealtime: MintProgress;
  nftListedRealTimePrivate: NftListedEvent;
  nftListedRealTimePublic: NftListedEvent;
  nftMintedRealtime: NftMintedEvent;
  nftModifiedPrivateRealtime: NftModifiedRealtimeResponse;
  nftModifiedPublicRealtime: NftModifiedRealtimeResponse;
  priceChangePercentageUpdated: PriceChangePercentage;
  priceHistoryUpdated: PriceHistory;
};


export type SubscriptionCollectionModifiedPrivateRealtimeArgs = {
  input: CollectionModifiedPrivateInput;
};


export type SubscriptionCollectionModifiedPublicRealtimeArgs = {
  input: CollectionModifiedPublicInput;
};


export type SubscriptionMintProgressRealtimeArgs = {
  input: MintProgressInput;
};


export type SubscriptionNftListedRealTimePrivateArgs = {
  input: NftListedFilter;
};


export type SubscriptionNftListedRealTimePublicArgs = {
  input: NftListedFilter;
};


export type SubscriptionNftMintedRealtimeArgs = {
  input: NftMintedEventInput;
};


export type SubscriptionNftModifiedPrivateRealtimeArgs = {
  input: NftModifiedPrivateInput;
};


export type SubscriptionNftModifiedPublicRealtimeArgs = {
  input: NftModifiedPublicInput;
};


export type SubscriptionPriceChangePercentageUpdatedArgs = {
  input: PriceHistoryInput;
};


export type SubscriptionPriceHistoryUpdatedArgs = {
  input: PriceHistoryInput;
};

/** Token standard of the collection */
export enum TokenStandard {
  Erc721 = 'ERC721',
  Erc1155 = 'ERC1155'
}

export type User = {
  __typename?: 'User';
  email: Scalars['String']['output'];
  id: Scalars['String']['output'];
  is_verified: Scalars['Boolean']['output'];
  last_login_at: Scalars['String']['output'];
  login_count: Scalars['String']['output'];
  role: UserRole;
  username: Scalars['String']['output'];
  wallet: Scalars['String']['output'];
};

export type UserResponse = {
  __typename?: 'UserResponse';
  avatar_url?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  role: UserRole;
  wallet: Scalars['String']['output'];
};

/** Role of a user */
export enum UserRole {
  Admin = 'ADMIN',
  User = 'USER'
}

export type LogoutMutationVariables = Exact<{
  wallet: Scalars['String']['input'];
}>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me: { __typename?: 'UserResponse', id: string, wallet: string, email?: string | null, avatar_url?: string | null } };

export type NonceQueryVariables = Exact<{ [key: string]: never; }>;


export type NonceQuery = { __typename?: 'Query', nonce: string };

export type RefreshTokenMutationVariables = Exact<{
  refreshToken: Scalars['String']['input'];
}>;


export type RefreshTokenMutation = { __typename?: 'Mutation', refreshToken: { __typename?: 'AuthVerifyResponse', accessToken: string, refreshToken: string } };

export type VerifyMutationVariables = Exact<{
  input: AuthVerifyInput;
}>;


export type VerifyMutation = { __typename?: 'Mutation', verify: { __typename?: 'AuthVerifyResponse', accessToken: string, refreshToken: string } };

export type CreateCollectionMutationVariables = Exact<{
  input: CreateCollectionInput;
}>;


export type CreateCollectionMutation = { __typename?: 'Mutation', createCollection: { __typename?: 'CreateCollectionResponse', collectionId?: string | null, contractAddress?: string | null, status?: string | null, steps?: Array<{ __typename?: 'Step', id: string, params: string }> | null } };

export type CollectionModifiedPublicRealtimeSubscriptionVariables = Exact<{
  input: CollectionModifiedPublicInput;
}>;


export type CollectionModifiedPublicRealtimeSubscription = { __typename?: 'Subscription', collectionModifiedPublicRealtime: { __typename?: 'CollectionModifiedRealtimeResponse', action: ActionType, data: { __typename?: 'Collection', id: string, name: string, chainId: string, image: string, mintPrice: string, maxSupply: string, chain: string, totalMinted: string, isVerified: boolean, status: CollectionStatus, description: string, mintStartDate: string, contractAddress: string, royaltyFee: string, mintLimit: string, uri: string, tokenStandard: TokenStandard, currency: Currency, publicMint: { __typename?: 'PublicMint', mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string }, allowlistStages?: Array<{ __typename?: 'AllowlistStage', stageId: string, mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string, wallets: Array<string> }> | null } } };

export type CollectionModifiedPrivateRealtimeSubscriptionVariables = Exact<{
  input: CollectionModifiedPrivateInput;
}>;


export type CollectionModifiedPrivateRealtimeSubscription = { __typename?: 'Subscription', collectionModifiedPrivateRealtime: { __typename?: 'CollectionModifiedRealtimeResponse', action: ActionType, data: { __typename?: 'Collection', id: string, name: string, image: string } } };

export type GetCollectionsQueryVariables = Exact<{
  input: GetCollectionsInput;
}>;


export type GetCollectionsQuery = { __typename?: 'Query', getCollections: { __typename?: 'CollectionsResponse', stats: { __typename?: 'Stats', artworks: string, artists: string, collectors: string }, collections: Array<{ __typename?: 'Collection', id: string, name: string, chainId: string, image: string, mintPrice: string, maxSupply: string, chain: string, totalMinted: string, isVerified: boolean, status: CollectionStatus, description: string, contractAddress: string, mintStartDate: string, royaltyFee: string, mintLimit: string, uri: string, tokenStandard: TokenStandard, currency: Currency, publicMint: { __typename?: 'PublicMint', mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string }, allowlistStages?: Array<{ __typename?: 'AllowlistStage', stageId: string, mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string, wallets: Array<string> }> | null }>, pagination: { __typename?: 'Pagination', total: string, limit: string, skip?: string | null, cursor?: string | null, hasMore: boolean } } };

export type GetCollectionQueryVariables = Exact<{
  input: GetCollectionsInput;
}>;


export type GetCollectionQuery = { __typename?: 'Query', getCollection: { __typename?: 'Collection', artType: ArtType, chain: string, chainId: string, contractAddress: string, createdAt: string, creatorId: string, creatorRole: UserRole, description: string, id: string, mintStartDate: string, image: string, isVerified: boolean, maxSupply: string, mintLimit: string, mintPrice: string, name: string, previewImages: Array<string>, royaltyFee: string, status: CollectionStatus, tokenStandard: TokenStandard, totalMinted: string, uri: string, currency: Currency, publicMint: { __typename?: 'PublicMint', mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string }, allowlistStages?: Array<{ __typename?: 'AllowlistStage', stageId: string, mintPrice: string, startDate: string, endDate: string, durationDays: string, durationHours: string, wallets: Array<string> }> | null } };

export type ModifyCollectionMutationVariables = Exact<{
  input: ModifyCollectionInput;
}>;


export type ModifyCollectionMutation = { __typename?: 'Mutation', modifyCollection: boolean };

export type GetSignedUrlQueryVariables = Exact<{ [key: string]: never; }>;


export type GetSignedUrlQuery = { __typename?: 'Query', getSignedUrl: string };

export type ListNfTsMutationVariables = Exact<{
  input: ListNfTsInput;
}>;


export type ListNfTsMutation = { __typename?: 'Mutation', listNFTs: { __typename?: 'ListNFTsResponse', status: string, steps: Array<{ __typename?: 'Step', id: string, params: string }>, listings: Array<{ __typename?: 'ListingNFT', tokenId: string, contractAddress: string, listingPrice: string, standard: string, amount?: string | null, metadataURI?: string | null, listingType: string, expiryTimestamp: string }>, errors?: Array<{ __typename?: 'ListingError', tokenId: string, contractAddress: string, message: string, errorCode?: string | null }> | null } };

export type GetNonceListingQueryVariables = Exact<{
  input: GetNonceListingInput;
}>;


export type GetNonceListingQuery = { __typename?: 'Query', getNonceListing: { __typename?: 'GetNonceListingResponse', success: boolean, nonce: string } };

export type ConfirmListingNftMutationVariables = Exact<{
  input: ConfirmListingNftInput;
}>;


export type ConfirmListingNftMutation = { __typename?: 'Mutation', confirmListingNft: { __typename?: 'ConfirmListingNftResponse', success: boolean, listingIds?: Array<string> | null, errorMessage?: string | null } };

export type GetPriceListingQueryVariables = Exact<{
  input: GetPriceListingInput;
}>;


export type GetPriceListingQuery = { __typename?: 'Query', getPriceListing: { __typename?: 'GetPriceListingResponse', success: boolean, listingPrice?: string | null, updatedAt: string, errorMessage?: string | null } };

export type NftListedRealTimePublicSubscriptionVariables = Exact<{
  input: NftListedFilter;
}>;


export type NftListedRealTimePublicSubscription = { __typename?: 'Subscription', nftListedRealTimePublic: { __typename?: 'NftListedEvent', nftId: string, tokenId: string, contractAddress: string, chainId: string, listingPrice: string, standard: TokenStandard, currency: Currency, wallet?: string | null } };

export type NftListedRealTimePrivateSubscriptionVariables = Exact<{
  input: NftListedFilter;
}>;


export type NftListedRealTimePrivateSubscription = { __typename?: 'Subscription', nftListedRealTimePrivate: { __typename?: 'NftListedEvent', nftId: string, tokenId: string, contractAddress: string, chainId: string, listingPrice: string, standard: TokenStandard, currency: Currency, wallet?: string | null } };

export type GetNftsQueryVariables = Exact<{
  input: GetNftsInput;
}>;


export type GetNftsQuery = { __typename?: 'Query', getNfts: { __typename?: 'NftsResponse', nfts: Array<{ __typename?: 'Nft', id: string, collectionId: string, txHash: string, tokenId: string, tokenUri: string, name: string, contractAddress: string, status: NftStatus, description: string, owner: string, image: string, creator: string, chainId: string, chain: string, isFeatured: boolean, mintPrice: string, currency: Currency, auctionId?: string | null, animation_url?: string | null, standard: TokenStandard, bids?: Array<{ __typename?: 'Bid', amount: string, bidder: string, timestamp: any }> | null, attributes?: Array<{ __typename?: 'Attribute', trait_type: string, value: string }> | null }>, pagination: { __typename?: 'Pagination', total: string, limit: string, skip?: string | null, cursor?: string | null, hasMore: boolean } } };

export type MintNftMutationVariables = Exact<{
  input: MintNftInput;
}>;


export type MintNftMutation = { __typename?: 'Mutation', mintNft: { __typename?: 'MintNftResponse', success: boolean, stageId?: string | null, stageType?: string | null, mintPrice?: string | null, estimatedGas?: string | null, maxSupply?: string | null, totalMinted?: string | null, errorMessage?: string | null, steps: Array<{ __typename?: 'Step', id: string, params: string }> } };

export type SubmitMintTransactionMutationVariables = Exact<{
  input: SubmitMintTransactionInput;
}>;


export type SubmitMintTransactionMutation = { __typename?: 'Mutation', submitMintTransaction: { __typename?: 'SubmitMintTransactionResponse', success: boolean, collectionId?: string | null, tokenIds?: Array<string> | null, amounts?: Array<string> | null, txHash?: string | null, errorMessage?: string | null } };

export type GetActiveStageQueryVariables = Exact<{
  input: GetActiveStageInput;
}>;


export type GetActiveStageQuery = { __typename?: 'Query', getActiveStage: { __typename?: 'GetActiveStageResponse', stageId: string, isPublicMint: boolean, mintPrice?: string | null } };

export type MintProgressRealtimeSubscriptionVariables = Exact<{
  input: MintProgressInput;
}>;


export type MintProgressRealtimeSubscription = { __typename?: 'Subscription', mintProgressRealtime: { __typename?: 'MintProgress', totalMinted: string, maxSupply: string } };

export type MintProgressQueryVariables = Exact<{
  input: MintProgressInput;
}>;


export type MintProgressQuery = { __typename?: 'Query', mintProgress: { __typename?: 'MintProgress', totalMinted: string, maxSupply: string } };

export type NftMintedRealtimeSubscriptionVariables = Exact<{
  input: NftMintedEventInput;
}>;


export type NftMintedRealtimeSubscription = { __typename?: 'Subscription', nftMintedRealtime: { __typename?: 'NftMintedEvent', contractAddress: string, tokenIds: Array<string>, amounts: Array<string>, tokenUris: Array<string>, recipient: string } };

export type GetMintCostQueryVariables = Exact<{
  input: GetMintCostInput;
}>;


export type GetMintCostQuery = { __typename?: 'Query', getMintCost: { __typename?: 'MintCostResponse', success: boolean, errorMessage?: string | null, mintPrice: string, estimatedGas: string, totalPrice: string, isPublicMint: boolean, stageId?: string | null, stageInfo?: { __typename?: 'StageInfo', stageId: string, mintLimit: string, mintedInStage: string } | null } };

export type NftModifiedPublicRealtimeSubscriptionVariables = Exact<{
  input: NftModifiedPublicInput;
}>;


export type NftModifiedPublicRealtimeSubscription = { __typename?: 'Subscription', nftModifiedPublicRealtime: { __typename?: 'NftModifiedRealtimeResponse', action: ActionType, data: Array<{ __typename?: 'Nft', id: string, collectionId: string, txHash: string, tokenId: string, tokenUri: string, name: string, contractAddress: string, status: NftStatus, description: string, owner: string, image: string, creator: string, chainId: string, chain: string, isFeatured: boolean, mintPrice: string, currency: Currency, auctionId?: string | null, animation_url?: string | null, standard: TokenStandard, bids?: Array<{ __typename?: 'Bid', amount: string, bidder: string, timestamp: any }> | null, attributes?: Array<{ __typename?: 'Attribute', trait_type: string, value: string }> | null }> } };

export type NftModifiedPrivateRealtimeSubscriptionVariables = Exact<{
  input: NftModifiedPrivateInput;
}>;


export type NftModifiedPrivateRealtimeSubscription = { __typename?: 'Subscription', nftModifiedPrivateRealtime: { __typename?: 'NftModifiedRealtimeResponse', action: ActionType, data: Array<{ __typename?: 'Nft', id: string, collectionId: string, txHash: string, tokenId: string, tokenUri: string, name: string, contractAddress: string, status: NftStatus, description: string, owner: string, image: string, creator: string, chainId: string, chain: string, isFeatured: boolean, mintPrice: string, currency: Currency, auctionId?: string | null, animation_url?: string | null, standard: TokenStandard, bids?: Array<{ __typename?: 'Bid', amount: string, bidder: string, timestamp: any }> | null, attributes?: Array<{ __typename?: 'Attribute', trait_type: string, value: string }> | null }> } };

export type GetPriceHistoryQueryVariables = Exact<{
  input: PriceHistoryInput;
}>;


export type GetPriceHistoryQuery = { __typename?: 'Query', getPriceHistory: Array<{ __typename?: 'PriceHistory', contractAddress: string, chainId: string, timestamp: string, open: string, high: string, low: string, close: string, volume: string, currency: Currency, assetType: AssetType, source?: Source | null }> };

export type GetPriceChangePercentageQueryVariables = Exact<{
  input: PriceHistoryInput;
}>;


export type GetPriceChangePercentageQuery = { __typename?: 'Query', getPriceChangePercentage: { __typename?: 'PriceChangePercentage', percentage: string, currency: Currency } };

export type PriceHistoryUpdatedSubscriptionVariables = Exact<{
  input: PriceHistoryInput;
}>;


export type PriceHistoryUpdatedSubscription = { __typename?: 'Subscription', priceHistoryUpdated: { __typename?: 'PriceHistory', contractAddress: string, chainId: string, timestamp: string, open: string, high: string, low: string, close: string, volume: string, currency: Currency, assetType: AssetType, source?: Source | null } };

export type PriceChangePercentageUpdatedSubscriptionVariables = Exact<{
  input: PriceHistoryInput;
}>;


export type PriceChangePercentageUpdatedSubscription = { __typename?: 'Subscription', priceChangePercentageUpdated: { __typename?: 'PriceChangePercentage', percentage: string, currency: Currency } };


export const LogoutDocument = gql`
    mutation Logout($wallet: String!) {
  logout(wallet: $wallet)
}
    `;
export type LogoutMutationFn = Apollo.MutationFunction<LogoutMutation, LogoutMutationVariables>;

/**
 * __useLogoutMutation__
 *
 * To run a mutation, you first call `useLogoutMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLogoutMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [logoutMutation, { data, loading, error }] = useLogoutMutation({
 *   variables: {
 *      wallet: // value for 'wallet'
 *   },
 * });
 */
export function useLogoutMutation(baseOptions?: Apollo.MutationHookOptions<LogoutMutation, LogoutMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<LogoutMutation, LogoutMutationVariables>(LogoutDocument, options);
      }
export type LogoutMutationHookResult = ReturnType<typeof useLogoutMutation>;
export type LogoutMutationResult = Apollo.MutationResult<LogoutMutation>;
export type LogoutMutationOptions = Apollo.BaseMutationOptions<LogoutMutation, LogoutMutationVariables>;
export const MeDocument = gql`
    query Me {
  me {
    id
    wallet
    email
    avatar_url
  }
}
    `;

/**
 * __useMeQuery__
 *
 * To run a query within a React component, call `useMeQuery` and pass it any options that fit your needs.
 * When your component renders, `useMeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMeQuery({
 *   variables: {
 *   },
 * });
 */
export function useMeQuery(baseOptions?: Apollo.QueryHookOptions<MeQuery, MeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MeQuery, MeQueryVariables>(MeDocument, options);
      }
export function useMeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MeQuery, MeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MeQuery, MeQueryVariables>(MeDocument, options);
        }
export function useMeSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<MeQuery, MeQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<MeQuery, MeQueryVariables>(MeDocument, options);
        }
export type MeQueryHookResult = ReturnType<typeof useMeQuery>;
export type MeLazyQueryHookResult = ReturnType<typeof useMeLazyQuery>;
export type MeSuspenseQueryHookResult = ReturnType<typeof useMeSuspenseQuery>;
export type MeQueryResult = Apollo.QueryResult<MeQuery, MeQueryVariables>;
export const NonceDocument = gql`
    query Nonce {
  nonce
}
    `;

/**
 * __useNonceQuery__
 *
 * To run a query within a React component, call `useNonceQuery` and pass it any options that fit your needs.
 * When your component renders, `useNonceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNonceQuery({
 *   variables: {
 *   },
 * });
 */
export function useNonceQuery(baseOptions?: Apollo.QueryHookOptions<NonceQuery, NonceQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<NonceQuery, NonceQueryVariables>(NonceDocument, options);
      }
export function useNonceLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<NonceQuery, NonceQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<NonceQuery, NonceQueryVariables>(NonceDocument, options);
        }
export function useNonceSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<NonceQuery, NonceQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<NonceQuery, NonceQueryVariables>(NonceDocument, options);
        }
export type NonceQueryHookResult = ReturnType<typeof useNonceQuery>;
export type NonceLazyQueryHookResult = ReturnType<typeof useNonceLazyQuery>;
export type NonceSuspenseQueryHookResult = ReturnType<typeof useNonceSuspenseQuery>;
export type NonceQueryResult = Apollo.QueryResult<NonceQuery, NonceQueryVariables>;
export const RefreshTokenDocument = gql`
    mutation RefreshToken($refreshToken: String!) {
  refreshToken(refreshToken: $refreshToken) {
    accessToken
    refreshToken
  }
}
    `;
export type RefreshTokenMutationFn = Apollo.MutationFunction<RefreshTokenMutation, RefreshTokenMutationVariables>;

/**
 * __useRefreshTokenMutation__
 *
 * To run a mutation, you first call `useRefreshTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRefreshTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [refreshTokenMutation, { data, loading, error }] = useRefreshTokenMutation({
 *   variables: {
 *      refreshToken: // value for 'refreshToken'
 *   },
 * });
 */
export function useRefreshTokenMutation(baseOptions?: Apollo.MutationHookOptions<RefreshTokenMutation, RefreshTokenMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RefreshTokenMutation, RefreshTokenMutationVariables>(RefreshTokenDocument, options);
      }
export type RefreshTokenMutationHookResult = ReturnType<typeof useRefreshTokenMutation>;
export type RefreshTokenMutationResult = Apollo.MutationResult<RefreshTokenMutation>;
export type RefreshTokenMutationOptions = Apollo.BaseMutationOptions<RefreshTokenMutation, RefreshTokenMutationVariables>;
export const VerifyDocument = gql`
    mutation Verify($input: AuthVerifyInput!) {
  verify(input: $input) {
    accessToken
    refreshToken
  }
}
    `;
export type VerifyMutationFn = Apollo.MutationFunction<VerifyMutation, VerifyMutationVariables>;

/**
 * __useVerifyMutation__
 *
 * To run a mutation, you first call `useVerifyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useVerifyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [verifyMutation, { data, loading, error }] = useVerifyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useVerifyMutation(baseOptions?: Apollo.MutationHookOptions<VerifyMutation, VerifyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<VerifyMutation, VerifyMutationVariables>(VerifyDocument, options);
      }
export type VerifyMutationHookResult = ReturnType<typeof useVerifyMutation>;
export type VerifyMutationResult = Apollo.MutationResult<VerifyMutation>;
export type VerifyMutationOptions = Apollo.BaseMutationOptions<VerifyMutation, VerifyMutationVariables>;
export const CreateCollectionDocument = gql`
    mutation CreateCollection($input: CreateCollectionInput!) {
  createCollection(input: $input) {
    collectionId
    contractAddress
    steps {
      id
      params
    }
    status
  }
}
    `;
export type CreateCollectionMutationFn = Apollo.MutationFunction<CreateCollectionMutation, CreateCollectionMutationVariables>;

/**
 * __useCreateCollectionMutation__
 *
 * To run a mutation, you first call `useCreateCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCollectionMutation, { data, loading, error }] = useCreateCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateCollectionMutation(baseOptions?: Apollo.MutationHookOptions<CreateCollectionMutation, CreateCollectionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateCollectionMutation, CreateCollectionMutationVariables>(CreateCollectionDocument, options);
      }
export type CreateCollectionMutationHookResult = ReturnType<typeof useCreateCollectionMutation>;
export type CreateCollectionMutationResult = Apollo.MutationResult<CreateCollectionMutation>;
export type CreateCollectionMutationOptions = Apollo.BaseMutationOptions<CreateCollectionMutation, CreateCollectionMutationVariables>;
export const CollectionModifiedPublicRealtimeDocument = gql`
    subscription CollectionModifiedPublicRealtime($input: CollectionModifiedPublicInput!) {
  collectionModifiedPublicRealtime(input: $input) {
    data {
      id
      name
      chainId
      image
      mintPrice
      maxSupply
      chain
      totalMinted
      isVerified
      status
      description
      mintStartDate
      contractAddress
      royaltyFee
      mintLimit
      uri
      tokenStandard
      currency
      publicMint {
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
      }
      allowlistStages {
        stageId
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
        wallets
      }
    }
    action
  }
}
    `;

/**
 * __useCollectionModifiedPublicRealtimeSubscription__
 *
 * To run a query within a React component, call `useCollectionModifiedPublicRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useCollectionModifiedPublicRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCollectionModifiedPublicRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCollectionModifiedPublicRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<CollectionModifiedPublicRealtimeSubscription, CollectionModifiedPublicRealtimeSubscriptionVariables> & ({ variables: CollectionModifiedPublicRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<CollectionModifiedPublicRealtimeSubscription, CollectionModifiedPublicRealtimeSubscriptionVariables>(CollectionModifiedPublicRealtimeDocument, options);
      }
export type CollectionModifiedPublicRealtimeSubscriptionHookResult = ReturnType<typeof useCollectionModifiedPublicRealtimeSubscription>;
export type CollectionModifiedPublicRealtimeSubscriptionResult = Apollo.SubscriptionResult<CollectionModifiedPublicRealtimeSubscription>;
export const CollectionModifiedPrivateRealtimeDocument = gql`
    subscription CollectionModifiedPrivateRealtime($input: CollectionModifiedPrivateInput!) {
  collectionModifiedPrivateRealtime(input: $input) {
    data {
      id
      name
      image
    }
    action
  }
}
    `;

/**
 * __useCollectionModifiedPrivateRealtimeSubscription__
 *
 * To run a query within a React component, call `useCollectionModifiedPrivateRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useCollectionModifiedPrivateRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCollectionModifiedPrivateRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCollectionModifiedPrivateRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<CollectionModifiedPrivateRealtimeSubscription, CollectionModifiedPrivateRealtimeSubscriptionVariables> & ({ variables: CollectionModifiedPrivateRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<CollectionModifiedPrivateRealtimeSubscription, CollectionModifiedPrivateRealtimeSubscriptionVariables>(CollectionModifiedPrivateRealtimeDocument, options);
      }
export type CollectionModifiedPrivateRealtimeSubscriptionHookResult = ReturnType<typeof useCollectionModifiedPrivateRealtimeSubscription>;
export type CollectionModifiedPrivateRealtimeSubscriptionResult = Apollo.SubscriptionResult<CollectionModifiedPrivateRealtimeSubscription>;
export const GetCollectionsDocument = gql`
    query GetCollections($input: GetCollectionsInput!) {
  getCollections(input: $input) {
    stats {
      artworks
      artists
      collectors
    }
    collections {
      id
      name
      chainId
      image
      mintPrice
      maxSupply
      chain
      totalMinted
      isVerified
      status
      description
      contractAddress
      mintStartDate
      royaltyFee
      mintLimit
      uri
      tokenStandard
      currency
      publicMint {
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
      }
      allowlistStages {
        stageId
        mintPrice
        startDate
        endDate
        durationDays
        durationHours
        wallets
      }
    }
    pagination {
      total
      limit
      skip
      cursor
      hasMore
    }
  }
}
    `;

/**
 * __useGetCollectionsQuery__
 *
 * To run a query within a React component, call `useGetCollectionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCollectionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCollectionsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetCollectionsQuery(baseOptions: Apollo.QueryHookOptions<GetCollectionsQuery, GetCollectionsQueryVariables> & ({ variables: GetCollectionsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCollectionsQuery, GetCollectionsQueryVariables>(GetCollectionsDocument, options);
      }
export function useGetCollectionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCollectionsQuery, GetCollectionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCollectionsQuery, GetCollectionsQueryVariables>(GetCollectionsDocument, options);
        }
export function useGetCollectionsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCollectionsQuery, GetCollectionsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCollectionsQuery, GetCollectionsQueryVariables>(GetCollectionsDocument, options);
        }
export type GetCollectionsQueryHookResult = ReturnType<typeof useGetCollectionsQuery>;
export type GetCollectionsLazyQueryHookResult = ReturnType<typeof useGetCollectionsLazyQuery>;
export type GetCollectionsSuspenseQueryHookResult = ReturnType<typeof useGetCollectionsSuspenseQuery>;
export type GetCollectionsQueryResult = Apollo.QueryResult<GetCollectionsQuery, GetCollectionsQueryVariables>;
export const GetCollectionDocument = gql`
    query GetCollection($input: GetCollectionsInput!) {
  getCollection(input: $input) {
    artType
    chain
    chainId
    contractAddress
    createdAt
    creatorId
    creatorRole
    description
    id
    mintStartDate
    image
    isVerified
    maxSupply
    mintLimit
    mintPrice
    name
    previewImages
    royaltyFee
    status
    tokenStandard
    totalMinted
    uri
    currency
    publicMint {
      mintPrice
      startDate
      endDate
      durationDays
      durationHours
    }
    allowlistStages {
      stageId
      mintPrice
      startDate
      endDate
      durationDays
      durationHours
      wallets
    }
  }
}
    `;

/**
 * __useGetCollectionQuery__
 *
 * To run a query within a React component, call `useGetCollectionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCollectionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCollectionQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetCollectionQuery(baseOptions: Apollo.QueryHookOptions<GetCollectionQuery, GetCollectionQueryVariables> & ({ variables: GetCollectionQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCollectionQuery, GetCollectionQueryVariables>(GetCollectionDocument, options);
      }
export function useGetCollectionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCollectionQuery, GetCollectionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCollectionQuery, GetCollectionQueryVariables>(GetCollectionDocument, options);
        }
export function useGetCollectionSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCollectionQuery, GetCollectionQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCollectionQuery, GetCollectionQueryVariables>(GetCollectionDocument, options);
        }
export type GetCollectionQueryHookResult = ReturnType<typeof useGetCollectionQuery>;
export type GetCollectionLazyQueryHookResult = ReturnType<typeof useGetCollectionLazyQuery>;
export type GetCollectionSuspenseQueryHookResult = ReturnType<typeof useGetCollectionSuspenseQuery>;
export type GetCollectionQueryResult = Apollo.QueryResult<GetCollectionQuery, GetCollectionQueryVariables>;
export const ModifyCollectionDocument = gql`
    mutation ModifyCollection($input: ModifyCollectionInput!) {
  modifyCollection(input: $input)
}
    `;
export type ModifyCollectionMutationFn = Apollo.MutationFunction<ModifyCollectionMutation, ModifyCollectionMutationVariables>;

/**
 * __useModifyCollectionMutation__
 *
 * To run a mutation, you first call `useModifyCollectionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useModifyCollectionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [modifyCollectionMutation, { data, loading, error }] = useModifyCollectionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useModifyCollectionMutation(baseOptions?: Apollo.MutationHookOptions<ModifyCollectionMutation, ModifyCollectionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ModifyCollectionMutation, ModifyCollectionMutationVariables>(ModifyCollectionDocument, options);
      }
export type ModifyCollectionMutationHookResult = ReturnType<typeof useModifyCollectionMutation>;
export type ModifyCollectionMutationResult = Apollo.MutationResult<ModifyCollectionMutation>;
export type ModifyCollectionMutationOptions = Apollo.BaseMutationOptions<ModifyCollectionMutation, ModifyCollectionMutationVariables>;
export const GetSignedUrlDocument = gql`
    query GetSignedUrl {
  getSignedUrl
}
    `;

/**
 * __useGetSignedUrlQuery__
 *
 * To run a query within a React component, call `useGetSignedUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSignedUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSignedUrlQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSignedUrlQuery(baseOptions?: Apollo.QueryHookOptions<GetSignedUrlQuery, GetSignedUrlQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSignedUrlQuery, GetSignedUrlQueryVariables>(GetSignedUrlDocument, options);
      }
export function useGetSignedUrlLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSignedUrlQuery, GetSignedUrlQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSignedUrlQuery, GetSignedUrlQueryVariables>(GetSignedUrlDocument, options);
        }
export function useGetSignedUrlSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSignedUrlQuery, GetSignedUrlQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSignedUrlQuery, GetSignedUrlQueryVariables>(GetSignedUrlDocument, options);
        }
export type GetSignedUrlQueryHookResult = ReturnType<typeof useGetSignedUrlQuery>;
export type GetSignedUrlLazyQueryHookResult = ReturnType<typeof useGetSignedUrlLazyQuery>;
export type GetSignedUrlSuspenseQueryHookResult = ReturnType<typeof useGetSignedUrlSuspenseQuery>;
export type GetSignedUrlQueryResult = Apollo.QueryResult<GetSignedUrlQuery, GetSignedUrlQueryVariables>;
export const ListNfTsDocument = gql`
    mutation ListNFTs($input: ListNFTsInput!) {
  listNFTs(input: $input) {
    steps {
      id
      params
    }
    listings {
      tokenId
      contractAddress
      listingPrice
      standard
      amount
      metadataURI
      listingType
      expiryTimestamp
    }
    errors {
      tokenId
      contractAddress
      message
      errorCode
    }
    status
  }
}
    `;
export type ListNfTsMutationFn = Apollo.MutationFunction<ListNfTsMutation, ListNfTsMutationVariables>;

/**
 * __useListNfTsMutation__
 *
 * To run a mutation, you first call `useListNfTsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useListNfTsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [listNfTsMutation, { data, loading, error }] = useListNfTsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useListNfTsMutation(baseOptions?: Apollo.MutationHookOptions<ListNfTsMutation, ListNfTsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ListNfTsMutation, ListNfTsMutationVariables>(ListNfTsDocument, options);
      }
export type ListNfTsMutationHookResult = ReturnType<typeof useListNfTsMutation>;
export type ListNfTsMutationResult = Apollo.MutationResult<ListNfTsMutation>;
export type ListNfTsMutationOptions = Apollo.BaseMutationOptions<ListNfTsMutation, ListNfTsMutationVariables>;
export const GetNonceListingDocument = gql`
    query GetNonceListing($input: GetNonceListingInput!) {
  getNonceListing(input: $input) {
    success
    nonce
  }
}
    `;

/**
 * __useGetNonceListingQuery__
 *
 * To run a query within a React component, call `useGetNonceListingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetNonceListingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetNonceListingQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetNonceListingQuery(baseOptions: Apollo.QueryHookOptions<GetNonceListingQuery, GetNonceListingQueryVariables> & ({ variables: GetNonceListingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetNonceListingQuery, GetNonceListingQueryVariables>(GetNonceListingDocument, options);
      }
export function useGetNonceListingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetNonceListingQuery, GetNonceListingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetNonceListingQuery, GetNonceListingQueryVariables>(GetNonceListingDocument, options);
        }
export function useGetNonceListingSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetNonceListingQuery, GetNonceListingQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetNonceListingQuery, GetNonceListingQueryVariables>(GetNonceListingDocument, options);
        }
export type GetNonceListingQueryHookResult = ReturnType<typeof useGetNonceListingQuery>;
export type GetNonceListingLazyQueryHookResult = ReturnType<typeof useGetNonceListingLazyQuery>;
export type GetNonceListingSuspenseQueryHookResult = ReturnType<typeof useGetNonceListingSuspenseQuery>;
export type GetNonceListingQueryResult = Apollo.QueryResult<GetNonceListingQuery, GetNonceListingQueryVariables>;
export const ConfirmListingNftDocument = gql`
    mutation ConfirmListingNft($input: ConfirmListingNftInput!) {
  confirmListingNft(input: $input) {
    success
    listingIds
    errorMessage
  }
}
    `;
export type ConfirmListingNftMutationFn = Apollo.MutationFunction<ConfirmListingNftMutation, ConfirmListingNftMutationVariables>;

/**
 * __useConfirmListingNftMutation__
 *
 * To run a mutation, you first call `useConfirmListingNftMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useConfirmListingNftMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [confirmListingNftMutation, { data, loading, error }] = useConfirmListingNftMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useConfirmListingNftMutation(baseOptions?: Apollo.MutationHookOptions<ConfirmListingNftMutation, ConfirmListingNftMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ConfirmListingNftMutation, ConfirmListingNftMutationVariables>(ConfirmListingNftDocument, options);
      }
export type ConfirmListingNftMutationHookResult = ReturnType<typeof useConfirmListingNftMutation>;
export type ConfirmListingNftMutationResult = Apollo.MutationResult<ConfirmListingNftMutation>;
export type ConfirmListingNftMutationOptions = Apollo.BaseMutationOptions<ConfirmListingNftMutation, ConfirmListingNftMutationVariables>;
export const GetPriceListingDocument = gql`
    query GetPriceListing($input: GetPriceListingInput!) {
  getPriceListing(input: $input) {
    success
    listingPrice
    updatedAt
    errorMessage
  }
}
    `;

/**
 * __useGetPriceListingQuery__
 *
 * To run a query within a React component, call `useGetPriceListingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPriceListingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPriceListingQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPriceListingQuery(baseOptions: Apollo.QueryHookOptions<GetPriceListingQuery, GetPriceListingQueryVariables> & ({ variables: GetPriceListingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPriceListingQuery, GetPriceListingQueryVariables>(GetPriceListingDocument, options);
      }
export function useGetPriceListingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPriceListingQuery, GetPriceListingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPriceListingQuery, GetPriceListingQueryVariables>(GetPriceListingDocument, options);
        }
export function useGetPriceListingSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPriceListingQuery, GetPriceListingQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPriceListingQuery, GetPriceListingQueryVariables>(GetPriceListingDocument, options);
        }
export type GetPriceListingQueryHookResult = ReturnType<typeof useGetPriceListingQuery>;
export type GetPriceListingLazyQueryHookResult = ReturnType<typeof useGetPriceListingLazyQuery>;
export type GetPriceListingSuspenseQueryHookResult = ReturnType<typeof useGetPriceListingSuspenseQuery>;
export type GetPriceListingQueryResult = Apollo.QueryResult<GetPriceListingQuery, GetPriceListingQueryVariables>;
export const NftListedRealTimePublicDocument = gql`
    subscription NftListedRealTimePublic($input: NftListedFilter!) {
  nftListedRealTimePublic(input: $input) {
    nftId
    tokenId
    contractAddress
    chainId
    listingPrice
    standard
    currency
    wallet
  }
}
    `;

/**
 * __useNftListedRealTimePublicSubscription__
 *
 * To run a query within a React component, call `useNftListedRealTimePublicSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNftListedRealTimePublicSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNftListedRealTimePublicSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useNftListedRealTimePublicSubscription(baseOptions: Apollo.SubscriptionHookOptions<NftListedRealTimePublicSubscription, NftListedRealTimePublicSubscriptionVariables> & ({ variables: NftListedRealTimePublicSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<NftListedRealTimePublicSubscription, NftListedRealTimePublicSubscriptionVariables>(NftListedRealTimePublicDocument, options);
      }
export type NftListedRealTimePublicSubscriptionHookResult = ReturnType<typeof useNftListedRealTimePublicSubscription>;
export type NftListedRealTimePublicSubscriptionResult = Apollo.SubscriptionResult<NftListedRealTimePublicSubscription>;
export const NftListedRealTimePrivateDocument = gql`
    subscription NftListedRealTimePrivate($input: NftListedFilter!) {
  nftListedRealTimePrivate(input: $input) {
    nftId
    tokenId
    contractAddress
    chainId
    listingPrice
    standard
    currency
    wallet
  }
}
    `;

/**
 * __useNftListedRealTimePrivateSubscription__
 *
 * To run a query within a React component, call `useNftListedRealTimePrivateSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNftListedRealTimePrivateSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNftListedRealTimePrivateSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useNftListedRealTimePrivateSubscription(baseOptions: Apollo.SubscriptionHookOptions<NftListedRealTimePrivateSubscription, NftListedRealTimePrivateSubscriptionVariables> & ({ variables: NftListedRealTimePrivateSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<NftListedRealTimePrivateSubscription, NftListedRealTimePrivateSubscriptionVariables>(NftListedRealTimePrivateDocument, options);
      }
export type NftListedRealTimePrivateSubscriptionHookResult = ReturnType<typeof useNftListedRealTimePrivateSubscription>;
export type NftListedRealTimePrivateSubscriptionResult = Apollo.SubscriptionResult<NftListedRealTimePrivateSubscription>;
export const GetNftsDocument = gql`
    query GetNfts($input: GetNftsInput!) {
  getNfts(input: $input) {
    nfts {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    pagination {
      total
      limit
      skip
      cursor
      hasMore
    }
  }
}
    `;

/**
 * __useGetNftsQuery__
 *
 * To run a query within a React component, call `useGetNftsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetNftsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetNftsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetNftsQuery(baseOptions: Apollo.QueryHookOptions<GetNftsQuery, GetNftsQueryVariables> & ({ variables: GetNftsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetNftsQuery, GetNftsQueryVariables>(GetNftsDocument, options);
      }
export function useGetNftsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetNftsQuery, GetNftsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetNftsQuery, GetNftsQueryVariables>(GetNftsDocument, options);
        }
export function useGetNftsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetNftsQuery, GetNftsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetNftsQuery, GetNftsQueryVariables>(GetNftsDocument, options);
        }
export type GetNftsQueryHookResult = ReturnType<typeof useGetNftsQuery>;
export type GetNftsLazyQueryHookResult = ReturnType<typeof useGetNftsLazyQuery>;
export type GetNftsSuspenseQueryHookResult = ReturnType<typeof useGetNftsSuspenseQuery>;
export type GetNftsQueryResult = Apollo.QueryResult<GetNftsQuery, GetNftsQueryVariables>;
export const MintNftDocument = gql`
    mutation MintNft($input: MintNftInput!) {
  mintNft(input: $input) {
    success
    steps {
      id
      params
    }
    stageId
    stageType
    mintPrice
    estimatedGas
    maxSupply
    totalMinted
    errorMessage
  }
}
    `;
export type MintNftMutationFn = Apollo.MutationFunction<MintNftMutation, MintNftMutationVariables>;

/**
 * __useMintNftMutation__
 *
 * To run a mutation, you first call `useMintNftMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMintNftMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [mintNftMutation, { data, loading, error }] = useMintNftMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMintNftMutation(baseOptions?: Apollo.MutationHookOptions<MintNftMutation, MintNftMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MintNftMutation, MintNftMutationVariables>(MintNftDocument, options);
      }
export type MintNftMutationHookResult = ReturnType<typeof useMintNftMutation>;
export type MintNftMutationResult = Apollo.MutationResult<MintNftMutation>;
export type MintNftMutationOptions = Apollo.BaseMutationOptions<MintNftMutation, MintNftMutationVariables>;
export const SubmitMintTransactionDocument = gql`
    mutation SubmitMintTransaction($input: SubmitMintTransactionInput!) {
  submitMintTransaction(input: $input) {
    success
    collectionId
    tokenIds
    amounts
    txHash
    errorMessage
  }
}
    `;
export type SubmitMintTransactionMutationFn = Apollo.MutationFunction<SubmitMintTransactionMutation, SubmitMintTransactionMutationVariables>;

/**
 * __useSubmitMintTransactionMutation__
 *
 * To run a mutation, you first call `useSubmitMintTransactionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSubmitMintTransactionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [submitMintTransactionMutation, { data, loading, error }] = useSubmitMintTransactionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSubmitMintTransactionMutation(baseOptions?: Apollo.MutationHookOptions<SubmitMintTransactionMutation, SubmitMintTransactionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SubmitMintTransactionMutation, SubmitMintTransactionMutationVariables>(SubmitMintTransactionDocument, options);
      }
export type SubmitMintTransactionMutationHookResult = ReturnType<typeof useSubmitMintTransactionMutation>;
export type SubmitMintTransactionMutationResult = Apollo.MutationResult<SubmitMintTransactionMutation>;
export type SubmitMintTransactionMutationOptions = Apollo.BaseMutationOptions<SubmitMintTransactionMutation, SubmitMintTransactionMutationVariables>;
export const GetActiveStageDocument = gql`
    query GetActiveStage($input: GetActiveStageInput!) {
  getActiveStage(input: $input) {
    stageId
    isPublicMint
    mintPrice
  }
}
    `;

/**
 * __useGetActiveStageQuery__
 *
 * To run a query within a React component, call `useGetActiveStageQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetActiveStageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetActiveStageQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetActiveStageQuery(baseOptions: Apollo.QueryHookOptions<GetActiveStageQuery, GetActiveStageQueryVariables> & ({ variables: GetActiveStageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetActiveStageQuery, GetActiveStageQueryVariables>(GetActiveStageDocument, options);
      }
export function useGetActiveStageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetActiveStageQuery, GetActiveStageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetActiveStageQuery, GetActiveStageQueryVariables>(GetActiveStageDocument, options);
        }
export function useGetActiveStageSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetActiveStageQuery, GetActiveStageQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetActiveStageQuery, GetActiveStageQueryVariables>(GetActiveStageDocument, options);
        }
export type GetActiveStageQueryHookResult = ReturnType<typeof useGetActiveStageQuery>;
export type GetActiveStageLazyQueryHookResult = ReturnType<typeof useGetActiveStageLazyQuery>;
export type GetActiveStageSuspenseQueryHookResult = ReturnType<typeof useGetActiveStageSuspenseQuery>;
export type GetActiveStageQueryResult = Apollo.QueryResult<GetActiveStageQuery, GetActiveStageQueryVariables>;
export const MintProgressRealtimeDocument = gql`
    subscription MintProgressRealtime($input: MintProgressInput!) {
  mintProgressRealtime(input: $input) {
    totalMinted
    maxSupply
  }
}
    `;

/**
 * __useMintProgressRealtimeSubscription__
 *
 * To run a query within a React component, call `useMintProgressRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useMintProgressRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMintProgressRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMintProgressRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<MintProgressRealtimeSubscription, MintProgressRealtimeSubscriptionVariables> & ({ variables: MintProgressRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<MintProgressRealtimeSubscription, MintProgressRealtimeSubscriptionVariables>(MintProgressRealtimeDocument, options);
      }
export type MintProgressRealtimeSubscriptionHookResult = ReturnType<typeof useMintProgressRealtimeSubscription>;
export type MintProgressRealtimeSubscriptionResult = Apollo.SubscriptionResult<MintProgressRealtimeSubscription>;
export const MintProgressDocument = gql`
    query MintProgress($input: MintProgressInput!) {
  mintProgress(input: $input) {
    totalMinted
    maxSupply
  }
}
    `;

/**
 * __useMintProgressQuery__
 *
 * To run a query within a React component, call `useMintProgressQuery` and pass it any options that fit your needs.
 * When your component renders, `useMintProgressQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMintProgressQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMintProgressQuery(baseOptions: Apollo.QueryHookOptions<MintProgressQuery, MintProgressQueryVariables> & ({ variables: MintProgressQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MintProgressQuery, MintProgressQueryVariables>(MintProgressDocument, options);
      }
export function useMintProgressLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MintProgressQuery, MintProgressQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MintProgressQuery, MintProgressQueryVariables>(MintProgressDocument, options);
        }
export function useMintProgressSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<MintProgressQuery, MintProgressQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<MintProgressQuery, MintProgressQueryVariables>(MintProgressDocument, options);
        }
export type MintProgressQueryHookResult = ReturnType<typeof useMintProgressQuery>;
export type MintProgressLazyQueryHookResult = ReturnType<typeof useMintProgressLazyQuery>;
export type MintProgressSuspenseQueryHookResult = ReturnType<typeof useMintProgressSuspenseQuery>;
export type MintProgressQueryResult = Apollo.QueryResult<MintProgressQuery, MintProgressQueryVariables>;
export const NftMintedRealtimeDocument = gql`
    subscription NftMintedRealtime($input: NftMintedEventInput!) {
  nftMintedRealtime(input: $input) {
    contractAddress
    tokenIds
    amounts
    tokenUris
    recipient
  }
}
    `;

/**
 * __useNftMintedRealtimeSubscription__
 *
 * To run a query within a React component, call `useNftMintedRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNftMintedRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNftMintedRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useNftMintedRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<NftMintedRealtimeSubscription, NftMintedRealtimeSubscriptionVariables> & ({ variables: NftMintedRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<NftMintedRealtimeSubscription, NftMintedRealtimeSubscriptionVariables>(NftMintedRealtimeDocument, options);
      }
export type NftMintedRealtimeSubscriptionHookResult = ReturnType<typeof useNftMintedRealtimeSubscription>;
export type NftMintedRealtimeSubscriptionResult = Apollo.SubscriptionResult<NftMintedRealtimeSubscription>;
export const GetMintCostDocument = gql`
    query GetMintCost($input: GetMintCostInput!) {
  getMintCost(input: $input) {
    success
    errorMessage
    mintPrice
    estimatedGas
    totalPrice
    isPublicMint
    stageId
    stageInfo {
      stageId
      mintLimit
      mintedInStage
    }
  }
}
    `;

/**
 * __useGetMintCostQuery__
 *
 * To run a query within a React component, call `useGetMintCostQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMintCostQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMintCostQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetMintCostQuery(baseOptions: Apollo.QueryHookOptions<GetMintCostQuery, GetMintCostQueryVariables> & ({ variables: GetMintCostQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMintCostQuery, GetMintCostQueryVariables>(GetMintCostDocument, options);
      }
export function useGetMintCostLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMintCostQuery, GetMintCostQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMintCostQuery, GetMintCostQueryVariables>(GetMintCostDocument, options);
        }
export function useGetMintCostSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetMintCostQuery, GetMintCostQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetMintCostQuery, GetMintCostQueryVariables>(GetMintCostDocument, options);
        }
export type GetMintCostQueryHookResult = ReturnType<typeof useGetMintCostQuery>;
export type GetMintCostLazyQueryHookResult = ReturnType<typeof useGetMintCostLazyQuery>;
export type GetMintCostSuspenseQueryHookResult = ReturnType<typeof useGetMintCostSuspenseQuery>;
export type GetMintCostQueryResult = Apollo.QueryResult<GetMintCostQuery, GetMintCostQueryVariables>;
export const NftModifiedPublicRealtimeDocument = gql`
    subscription NftModifiedPublicRealtime($input: NftModifiedPublicInput!) {
  nftModifiedPublicRealtime(input: $input) {
    data {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    action
  }
}
    `;

/**
 * __useNftModifiedPublicRealtimeSubscription__
 *
 * To run a query within a React component, call `useNftModifiedPublicRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNftModifiedPublicRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNftModifiedPublicRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useNftModifiedPublicRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<NftModifiedPublicRealtimeSubscription, NftModifiedPublicRealtimeSubscriptionVariables> & ({ variables: NftModifiedPublicRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<NftModifiedPublicRealtimeSubscription, NftModifiedPublicRealtimeSubscriptionVariables>(NftModifiedPublicRealtimeDocument, options);
      }
export type NftModifiedPublicRealtimeSubscriptionHookResult = ReturnType<typeof useNftModifiedPublicRealtimeSubscription>;
export type NftModifiedPublicRealtimeSubscriptionResult = Apollo.SubscriptionResult<NftModifiedPublicRealtimeSubscription>;
export const NftModifiedPrivateRealtimeDocument = gql`
    subscription NftModifiedPrivateRealtime($input: NftModifiedPrivateInput!) {
  nftModifiedPrivateRealtime(input: $input) {
    data {
      id
      collectionId
      txHash
      tokenId
      tokenUri
      name
      contractAddress
      status
      description
      owner
      image
      creator
      chainId
      chain
      isFeatured
      mintPrice
      currency
      bids {
        amount
        bidder
        timestamp
      }
      auctionId
      animation_url
      attributes {
        trait_type
        value
      }
      standard
    }
    action
  }
}
    `;

/**
 * __useNftModifiedPrivateRealtimeSubscription__
 *
 * To run a query within a React component, call `useNftModifiedPrivateRealtimeSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNftModifiedPrivateRealtimeSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNftModifiedPrivateRealtimeSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useNftModifiedPrivateRealtimeSubscription(baseOptions: Apollo.SubscriptionHookOptions<NftModifiedPrivateRealtimeSubscription, NftModifiedPrivateRealtimeSubscriptionVariables> & ({ variables: NftModifiedPrivateRealtimeSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<NftModifiedPrivateRealtimeSubscription, NftModifiedPrivateRealtimeSubscriptionVariables>(NftModifiedPrivateRealtimeDocument, options);
      }
export type NftModifiedPrivateRealtimeSubscriptionHookResult = ReturnType<typeof useNftModifiedPrivateRealtimeSubscription>;
export type NftModifiedPrivateRealtimeSubscriptionResult = Apollo.SubscriptionResult<NftModifiedPrivateRealtimeSubscription>;
export const GetPriceHistoryDocument = gql`
    query GetPriceHistory($input: PriceHistoryInput!) {
  getPriceHistory(input: $input) {
    contractAddress
    chainId
    timestamp
    open
    high
    low
    close
    volume
    currency
    assetType
    source
  }
}
    `;

/**
 * __useGetPriceHistoryQuery__
 *
 * To run a query within a React component, call `useGetPriceHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPriceHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPriceHistoryQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPriceHistoryQuery(baseOptions: Apollo.QueryHookOptions<GetPriceHistoryQuery, GetPriceHistoryQueryVariables> & ({ variables: GetPriceHistoryQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>(GetPriceHistoryDocument, options);
      }
export function useGetPriceHistoryLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>(GetPriceHistoryDocument, options);
        }
export function useGetPriceHistorySuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>(GetPriceHistoryDocument, options);
        }
export type GetPriceHistoryQueryHookResult = ReturnType<typeof useGetPriceHistoryQuery>;
export type GetPriceHistoryLazyQueryHookResult = ReturnType<typeof useGetPriceHistoryLazyQuery>;
export type GetPriceHistorySuspenseQueryHookResult = ReturnType<typeof useGetPriceHistorySuspenseQuery>;
export type GetPriceHistoryQueryResult = Apollo.QueryResult<GetPriceHistoryQuery, GetPriceHistoryQueryVariables>;
export const GetPriceChangePercentageDocument = gql`
    query GetPriceChangePercentage($input: PriceHistoryInput!) {
  getPriceChangePercentage(input: $input) {
    percentage
    currency
  }
}
    `;

/**
 * __useGetPriceChangePercentageQuery__
 *
 * To run a query within a React component, call `useGetPriceChangePercentageQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPriceChangePercentageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPriceChangePercentageQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPriceChangePercentageQuery(baseOptions: Apollo.QueryHookOptions<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables> & ({ variables: GetPriceChangePercentageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>(GetPriceChangePercentageDocument, options);
      }
export function useGetPriceChangePercentageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>(GetPriceChangePercentageDocument, options);
        }
export function useGetPriceChangePercentageSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>(GetPriceChangePercentageDocument, options);
        }
export type GetPriceChangePercentageQueryHookResult = ReturnType<typeof useGetPriceChangePercentageQuery>;
export type GetPriceChangePercentageLazyQueryHookResult = ReturnType<typeof useGetPriceChangePercentageLazyQuery>;
export type GetPriceChangePercentageSuspenseQueryHookResult = ReturnType<typeof useGetPriceChangePercentageSuspenseQuery>;
export type GetPriceChangePercentageQueryResult = Apollo.QueryResult<GetPriceChangePercentageQuery, GetPriceChangePercentageQueryVariables>;
export const PriceHistoryUpdatedDocument = gql`
    subscription PriceHistoryUpdated($input: PriceHistoryInput!) {
  priceHistoryUpdated(input: $input) {
    contractAddress
    chainId
    timestamp
    open
    high
    low
    close
    volume
    currency
    assetType
    source
  }
}
    `;

/**
 * __usePriceHistoryUpdatedSubscription__
 *
 * To run a query within a React component, call `usePriceHistoryUpdatedSubscription` and pass it any options that fit your needs.
 * When your component renders, `usePriceHistoryUpdatedSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePriceHistoryUpdatedSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePriceHistoryUpdatedSubscription(baseOptions: Apollo.SubscriptionHookOptions<PriceHistoryUpdatedSubscription, PriceHistoryUpdatedSubscriptionVariables> & ({ variables: PriceHistoryUpdatedSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<PriceHistoryUpdatedSubscription, PriceHistoryUpdatedSubscriptionVariables>(PriceHistoryUpdatedDocument, options);
      }
export type PriceHistoryUpdatedSubscriptionHookResult = ReturnType<typeof usePriceHistoryUpdatedSubscription>;
export type PriceHistoryUpdatedSubscriptionResult = Apollo.SubscriptionResult<PriceHistoryUpdatedSubscription>;
export const PriceChangePercentageUpdatedDocument = gql`
    subscription PriceChangePercentageUpdated($input: PriceHistoryInput!) {
  priceChangePercentageUpdated(input: $input) {
    percentage
    currency
  }
}
    `;

/**
 * __usePriceChangePercentageUpdatedSubscription__
 *
 * To run a query within a React component, call `usePriceChangePercentageUpdatedSubscription` and pass it any options that fit your needs.
 * When your component renders, `usePriceChangePercentageUpdatedSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePriceChangePercentageUpdatedSubscription({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePriceChangePercentageUpdatedSubscription(baseOptions: Apollo.SubscriptionHookOptions<PriceChangePercentageUpdatedSubscription, PriceChangePercentageUpdatedSubscriptionVariables> & ({ variables: PriceChangePercentageUpdatedSubscriptionVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<PriceChangePercentageUpdatedSubscription, PriceChangePercentageUpdatedSubscriptionVariables>(PriceChangePercentageUpdatedDocument, options);
      }
export type PriceChangePercentageUpdatedSubscriptionHookResult = ReturnType<typeof usePriceChangePercentageUpdatedSubscription>;
export type PriceChangePercentageUpdatedSubscriptionResult = Apollo.SubscriptionResult<PriceChangePercentageUpdatedSubscription>;