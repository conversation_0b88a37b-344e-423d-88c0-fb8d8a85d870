"use client";

import { useState, useEffect } from "react";
import { useAccount, useSwitch<PERSON>hain, useConnect } from "wagmi";
import { injected } from "@wagmi/connectors";
import { toast } from "sonner";
import {
  ActionType,
  CreateCollectionInput,
  Currency,
  useCollectionModifiedPublicRealtimeSubscription,
  useCreateCollectionMutation,
} from "@/lib/api/graphql/generated";
import { StepStatus, FormData } from "@/types/collection.type";
import { getSupportedChains } from "@/lib/constant/chains";
import client from "@/lib/api/apolloClient";
import { isAddress } from "ethers";

// Import our custom hooks
import { useCollectionForm } from "./useCollectionForm";
import { useFileUpload } from "./useFileUpload";
import { useContractDeployment } from "./useContractDeployment";
import { useCollectionValidation } from "./useCollectionValidation";

interface ModalState {
  isModalOpen: boolean;
  step1Status: StepStatus;
  step2Status: StepStatus;
}

export function useCreateCollectionOrchestrator() {
  const { address, chain, isConnected, chainId: chainIdAccount } = useAccount();
  const { connectAsync } = useConnect();
  const { switchChainAsync } = useSwitchChain();
  const [createCollectionMutation] = useCreateCollectionMutation();

  // Modal and loading state
  const [isLoading, setIsLoading] = useState(false);
  const [modalState, setModalState] = useState<ModalState>({
    isModalOpen: false,
    step1Status: "pending",
    step2Status: "pending",
  });

  // Initialize hooks
  const formHook = useCollectionForm();
  const fileHook = useFileUpload({ selectedArtType: formHook.selectedArtType });
  const deploymentHook = useContractDeployment();
  const validationHook = useCollectionValidation();

  // Update form validity when files change
  useEffect(() => {
    const hasRequiredFiles =
      fileHook.collectionImageFile !== null &&
      (formHook.selectedArtType === "SAME"
        ? fileHook.artworkFile !== null
        : fileHook.metadataUrl.trim() !== "");
    
    formHook.updateFormValidity(hasRequiredFiles);
  }, [
    formHook.form.formState.isValid,
    fileHook.collectionImageFile,
    formHook.selectedArtType,
    fileHook.artworkFile,
    fileHook.metadataUrl,
    formHook.updateFormValidity,
  ]);

  // Subscription for collection creation
  const { data: collectionCreatedData } = useCollectionModifiedPublicRealtimeSubscription({
    variables: {
      input: {
        chainId: chainIdAccount?.toString() || "",
      },
    },
    onData: (data) => {
      console.log("Collection creation data:", data);
    },
    onError: (error) => {
      console.error("Subscription error:", error);
      toast.error("Failed to subscribe to collection creation", {
        description: error.message,
      });
    },
    skip: !deploymentHook.deployedAddress || !isAddress(deploymentHook.deployedAddress),
  });

  // Handle collection creation success
  useEffect(() => {
    if (
      collectionCreatedData?.collectionModifiedPublicRealtime.action === ActionType.Create
    ) {
      const { contractAddress, name, id } =
        collectionCreatedData.collectionModifiedPublicRealtime.data;
      
      setModalState(prev => ({ ...prev, step2Status: "completed" }));
      setModalState(prev => ({ ...prev, isModalOpen: false }));
      
      toast.success("Collection created", {
        description: `${name} - Collection ID: ${id} - Contract Address: ${contractAddress}`,
      });
      
      handleClearForm();
    }
  }, [collectionCreatedData]);

  const getButtonText = () =>
    !isConnected
      ? "Connect Wallet"
      : isLoading
      ? "Processing..."
      : chain?.name === formHook.selectedChain
      ? `Create Collection on ${chain.name}`
      : `Switch to ${formHook.selectedChain}`;

  const handleClearForm = () => {
    formHook.resetForm();
    fileHook.resetFiles();
    deploymentHook.resetDeployment();
    setIsLoading(false);
    setModalState({
      isModalOpen: false,
      step1Status: "pending",
      step2Status: "pending",
    });
    toast.success("Form cleared successfully");
  };

  const createBaseInput = (
    values: FormData,
    tokenUri: string,
    collectionImageUrl: string
  ): CreateCollectionInput => ({
    chain: values.chain,
    chainId: values.chainId,
    name: values.name,
    description: values.description,
    artType: values.artType,
    uri: tokenUri,
    image: collectionImageUrl,
    tokenStandard: values.tokenStandard,
    mintPrice: String(values.mintPrice),
    royaltyFee: String(values.royaltyFee),
    maxSupply: String(values.maxSupply),
    mintLimit: String(values.mintLimit),
    mintStartDate: String(values.mintStartDate),
    currency: chain?.nativeCurrency.symbol as Currency,
    allowlistStages: formHook.allowlistStages.map((stage) => ({
      mintPrice: stage.mintPrice,
      startDate: stage.startDate,
      durationDays: stage.durationDays,
      durationHours: stage.durationHours,
      wallets: stage.wallets,
    })),
    publicMint: {
      mintPrice: values.publicMint.mintPrice,
      startDate: values.publicMint.startDate,
      durationDays: values.publicMint.durationDays,
      durationHours: values.publicMint.durationHours,
    },
  });

  const onSubmit = async (values: FormData) => {
    // Step 1: Connect wallet if needed
    if (!isConnected) {
      try {
        await connectAsync({ connector: injected() });
        return;
      } catch (error) {
        toast.error("Failed to connect wallet", {
          description: (error as Error).message,
        });
        return;
      }
    }

    // Step 2: Validate allowlist stages
    const overlapError = validationHook.validateAllowlistStages(formHook.allowlistStages);
    if (overlapError) {
      toast.error("Validation error", { description: overlapError });
      return;
    }

    // Step 3: Validate form
    const formValidation = validationHook.validateForm(values);
    if (!formValidation.isValid) {
      return;
    }

    // Step 4: Switch chain if needed
    if (chain?.name !== formHook.selectedChain) {
      const chainId = getSupportedChains().find(
        (c) => c.name === formHook.selectedChain
      )?.id;
      if (chainId) {
        try {
          await switchChainAsync({ chainId });
          return;
        } catch (error) {
          toast.error("Failed to switch chain", { description: (error as Error).message });
          return;
        }
      }
    }

    // Step 5: Validate files
    if (!fileHook.validateFiles()) {
      return;
    }

    setIsLoading(true);
    setModalState({
      isModalOpen: true,
      step1Status: "processing",
      step2Status: "pending",
    });

    try {
      await client.cache.reset();

      // Step 6: Upload files
      const collectionImageUrl = await fileHook.uploadCollectionImage();
      const tokenUri = await fileHook.uploadMetadata(values);

      // Step 7: Create collection input
      const baseInput = createBaseInput(values, tokenUri, collectionImageUrl);

      // Step 8: Create collection (Mutation 1)
      const { data: mutation1Data } = await createCollectionMutation({
        variables: { input: baseInput },
      });

      if (!mutation1Data?.createCollection?.success) {
        throw new Error(
          mutation1Data?.createCollection?.message || "Failed to create collection"
        );
      }

      const initializeData = mutation1Data.createCollection.data;
      if (!initializeData) {
        throw new Error("No initialize data received from server");
      }

      setModalState(prev => ({ ...prev, step1Status: "completed" }));

      // Step 9: Deploy contract
      const contractAddress = await deploymentHook.deployContract(initializeData);

      // Step 10: Update collection with contract address (Mutation 2)
      setModalState(prev => ({ ...prev, step2Status: "processing" }));

      const { data: mutation2Data } = await createCollectionMutation({
        variables: {
          input: {
            ...baseInput,
            contractAddress,
          },
        },
      });

      if (!mutation2Data?.createCollection?.success) {
        throw new Error(
          mutation2Data?.createCollection?.message || "Failed to update collection"
        );
      }

      console.log("Collection creation completed successfully");
    } catch (error) {
      console.error("Collection creation failed:", error);
      
      let description = (error as Error).message;
      if (description.includes("JSON")) {
        description = "Invalid data received from server. Please try again or contact support.";
      } else if (description.includes("deploy")) {
        description = "Contract deployment failed. Check gas or network.";
      } else if (description.includes("reverted")) {
        description = "Transaction reverted. Check contract logic or inputs.";
      } else if (description.includes("network")) {
        description = "Network error. Please check your connection.";
      }
      
      toast.error("Failed to create collection", { description });
      setModalState({
        isModalOpen: false,
        step1Status: "pending",
        step2Status: "pending",
      });
      setIsLoading(false);
    }
  };

  return {
    // Form
    form: formHook.form,
    
    // State
    isLoading,
    selectedChain: formHook.selectedChain,
    selectedArtType: formHook.selectedArtType,
    allowlistStages: formHook.allowlistStages,
    publicMint: formHook.publicMint,
    isFormValid: formHook.isFormValid,
    
    // Files
    collectionImageFile: fileHook.collectionImageFile,
    artworkFile: fileHook.artworkFile,
    metadataUrl: fileHook.metadataUrl,
    
    // Modal
    isModalOpen: modalState.isModalOpen,
    step1Status: modalState.step1Status,
    step2Status: modalState.step2Status,
    
    // Actions
    setSelectedChain: formHook.setSelectedChain,
    setSelectedArtType: formHook.setSelectedArtType,
    setAllowlistStages: formHook.setAllowlistStages,
    setPublicMint: formHook.setPublicMint,
    setCollectionImageFile: fileHook.setCollectionImageFile,
    setArtworkFile: fileHook.setArtworkFile,
    setMetadataUrl: fileHook.setMetadataUrl,
    setIsModalOpen: (open: boolean) => setModalState(prev => ({ ...prev, isModalOpen: open })),
    
    getButtonText,
    handleClearForm,
    onSubmit,
  };
}
