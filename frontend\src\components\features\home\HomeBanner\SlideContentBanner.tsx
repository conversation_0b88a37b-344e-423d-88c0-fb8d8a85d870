import { SlideCollection } from "@/types/collection.type";
import { motion } from "framer-motion";

export const SlideContentBanner = ({
  slide,
  isActive,
}: {
  slide: SlideCollection;
  isActive: boolean;
}) => {
  return (
    <motion.div
      className={`${isActive ? "block" : "hidden"}`}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: isActive ? 1 : 0, x: isActive ? 0 : -20 }}
      transition={{ duration: 0.5 }}
    >
      <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
        <span
          className="bg-clip-text text-transparent"
          style={{
            backgroundImage: `linear-gradient(to right, ${slide.color1}, ${slide.color2})`,
          }}
        >
          {slide.title}
        </span>
      </h1>
      <p className="text-lg md:text-xl mb-8 text-gray-700 dark:text-gray-300 max-w-lg">
        {slide.description}
      </p>
    </motion.div>
  );
};
