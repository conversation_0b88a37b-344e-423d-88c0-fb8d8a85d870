"use client";

import { useState, useEffect, useCallback } from "react";
import { mockNFTs } from "@/data/mockData";

// This custom hook encapsulates data fetching and state management logic
// for the AuctionsList component. This promotes:
// - Separation of Concerns: Keeps data logic out of the UI component.
// - Reusability: Logic can be shared or adapted elsewhere.
// - Testability: Hook can be tested in isolation.
// - Maintainability: Easier to find and update data-related code.
export function useAuctionsListData() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [auctions, setAuctions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [likedAuctions, setLikedAuctions] = useState<Record<string, boolean>>(
    {}
  );

  useEffect(() => {
    // Simulate API call to get auctions
    const timer = setTimeout(() => {
      const mockAuctions = mockNFTs.slice(0, 8).map((nft, index) => {
        const hoursToAdd = Math.floor(Math.random() * 72) + 1;
        const endTime = new Date();
        endTime.setHours(endTime.getHours() + hoursToAdd);
        const startingBid =
          Number.parseFloat(nft.price) * (Math.random() * 0.5 + 0.8);
        const bidCount = Math.floor(Math.random() * 20);
        return {
          ...nft,
          id: `auction-${index}`,
          endTime,
          startingBid,
          currentBid: startingBid + bidCount * 0.1,
          bidCount,
          highestBidder: `User${Math.floor(Math.random() * 1000)}`,
        };
      });
      setAuctions(mockAuctions);
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handleLikeToggle = useCallback((id: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setLikedAuctions((prev) => ({ ...prev, [id]: !prev[id] }));
  }, []);

  const formatTimeRemaining = useCallback((endTime: Date) => {
    const now = new Date();
    const diff = endTime.getTime() - now.getTime();
    if (diff <= 0) return "Ended";
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    if (hours < 24) return `${hours}h ${minutes}m`;
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h`;
  }, []);

  return {
    auctions,
    loading,
    likedAuctions,
    handleLikeToggle,
    formatTimeRemaining,
  };
}
