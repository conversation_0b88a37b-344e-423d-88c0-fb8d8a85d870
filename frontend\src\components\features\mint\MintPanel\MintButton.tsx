"use client";

import type React from "react";
import { useSelector } from "react-redux";
import { useAccount } from "wagmi";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { RootState } from "@/store/store";
import { AttributeInput } from "@/lib/api/graphql/generated";
import { useMintValidation } from "@/hooks/useMintValidation";
import { useMintButton } from "@/hooks/useMintButton";
import { NftMetadataInput, MintCost } from "@/types/mint.types";

interface MintButtonProps {
  agreedToTerms: boolean;
  imageFile?: string | null;
  name: string;
  description: string;
  attributes: AttributeInput[];
  amount: number;
  royalty: number;
  gasPrice?: string;
  gasLimit?: string;
  signature?: string;
  nonce?: string;
  batchMetadata: NftMetadataInput[];
  onMintSuccess: () => void;
  onMintError: (error: string) => void;
  onConfirm: (isBatch: boolean) => void;
  isSameArtType: boolean;
  isAllowlistMint: boolean;
  lastMintCost?: MintCost;
  isCalculating?: boolean;
}

const MintButton: React.FC<MintButtonProps> = ({
  agreedToTerms,
  imageFile,
  name,
  description,
  attributes,
  amount,
  royalty,
  gasPrice,
  gasLimit,
  signature,
  nonce,
  batchMetadata,
  onConfirm,
  isSameArtType,
  isAllowlistMint,
  onMintSuccess,
  onMintError,
  lastMintCost = {
    mintPrice: "0",
    estimatedGas: "0",
    totalPrice: "0",
  },
  isCalculating = false,
}) => {
  const collection = useSelector((state: RootState) => state.collection.data);
  const { address } = useAccount();

  const { validations } = useMintValidation({
    address,
    collection,
    isSameArtType,
    isAllowlistMint,
    imageFile,
    amount,
    attributes,
    name,
    description,
    batchMetadata,
    signature,
    nonce,
  });

  const { buttonConfigs } = useMintButton({
    collection,
    validations,
    agreedToTerms,
    lastMintCost,
    isLoadingMintCost: isCalculating,
    isAllowlistMint,
    isSameArtType,
    batchMetadata,
    amount,
    onConfirm,
  });

  return (
    <div className="flex flex-col gap-2">
      {buttonConfigs.map((config, index) => (
        <Button
          key={index}
          id={config.id}
          variant={config.variant || "default"}
          disabled={config.disabled}
          onClick={config.onClick}
          className={cn(
            "h-10 text-sm font-medium",
            config.variant === "default" &&
              "bg-pink-500 hover:bg-pink-600 text-white dark:bg-pink-600 dark:hover:bg-pink-700",
            config.variant === "destructive" &&
              "bg-red-500 hover:bg-red-600 text-white dark:bg-red-600 dark:hover:bg-red-700",
            config.variant === "secondary" &&
              "bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300",
            config.disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          {config.icon}
          {config.text}
        </Button>
      ))}
    </div>
  );
};

export default MintButton;
