/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import type React from "react";
import { useState, useMemo, useEffect } from "react";
import { useSelector } from "react-redux";
import { useAccount, useConnect, useBalance } from "wagmi";
import { metaMask } from "wagmi/connectors";
import { ethers } from "ethers";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  AlertTriangle,
  Wallet,
  Sparkles,
  Package,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { RootState } from "@/store/store";
import {
  NftMetadataInput,
  AttributeInput,
  useGetMintCostQuery,
  useGetActiveStageQuery,
} from "@/lib/api/graphql/generated";
import { toast } from "sonner";

interface MintButtonProps {
  agreedToTerms: boolean;
  imageFile?: string | null;
  name: string;
  description: string;
  attributes: AttributeInput[];
  amount: number;
  royalty: number;
  gasPrice?: string;
  gasLimit?: string;
  signature?: string;
  nonce?: string;
  batchMetadata: NftMetadataInput[];
  onMintSuccess: () => void;
  onMintError: (error: string) => void;
  onConfirm: (isBatch: boolean) => void;
  isSameArtType: boolean;
  isAllowlistMint: boolean;
  lastMintCost?: {
    mintPrice: string;
    estimatedGas: string;
    totalPrice: string;
  };
  isCalculating?: boolean;
}

interface ButtonConfig {
  text: string;
  disabled: boolean;
  onClick?: () => void;
  id?: string;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  icon?: React.ReactNode;
}

interface ValidationState {
  isValidImage: boolean;
  hasSufficientBalance: boolean;
  isValidAmount: boolean;
  isValidAttributes: boolean;
  isValidBatch: boolean;
  isValidSignature: boolean;
  isValidNonce: boolean;
}

const MintButton: React.FC<MintButtonProps> = ({
  agreedToTerms,
  imageFile,
  name,
  description,
  attributes,
  amount,
  royalty,
  gasPrice,
  gasLimit,
  signature,
  nonce,
  batchMetadata,
  onConfirm,
  isSameArtType,
  isAllowlistMint,
  onMintSuccess,
  onMintError,
  lastMintCost = {
    mintPrice: "0",
    estimatedGas: "0",
    totalPrice: "0",
  },
  isCalculating = false,
}) => {
  const [isEstimating, setIsEstimating] = useState(false);
  const [currentAmount, setCurrentAmount] = useState(amount);
  const collection = useSelector((state: RootState) => state.collection.data);
  const { address, isConnected } = useAccount();
  const { connect } = useConnect();
  const { data: balance } = useBalance({ address });

  useEffect(() => {
    const newAmount = batchMetadata.length
      ? batchMetadata.reduce((sum, meta) => sum + Number(meta.amount), 0)
      : amount;

    if (newAmount !== currentAmount) {
      setCurrentAmount(newAmount);
    }
  }, [amount, batchMetadata]);

  const { data: activeStageData } = useGetActiveStageQuery({
    variables: {
      input: {
        chainId: collection?.chainId ?? "",
        contractAddress: collection?.contractAddress ?? "",
        wallet: address ?? "",
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress || !address,
  });

  const {
    data: mintCostData,
    error: mintCostError,
    loading: mintCostLoading,
  } = useGetMintCostQuery({
    variables: {
      input: {
        chainId: collection?.chainId ?? "",
        contractAddress: collection?.contractAddress ?? "",
        amount: currentAmount.toString(),
        wallet: address ?? "",
        stageId: activeStageData?.getActiveStage?.stageId,
      },
    },
    skip: !collection?.chainId || !collection?.contractAddress || isCalculating,
  });

  const validations: ValidationState = useMemo(
    () => ({
      isValidImage: isSameArtType
        ? true
        : Boolean(
            (imageFile && /^(ipfs|https):\/\//.test(imageFile)) ||
              batchMetadata.length > 0
          ),
      hasSufficientBalance: Boolean(
        balance &&
          Number(ethers.formatEther(balance.value)) >=
            Number(mintCostData?.getMintCost.totalPrice ?? 0) +
              Number(mintCostData?.getMintCost.estimatedGas ?? 0)
      ),
      isValidAmount: Boolean(
        amount >= 1 &&
          amount <=
            Math.min(
              collection?.mintLimit ? Number(collection.mintLimit) : 100,
              Number(collection?.maxSupply) - Number(collection?.totalMinted)
            ) &&
          (isSameArtType ||
            batchMetadata.length === 0 ||
            batchMetadata.reduce(
              (sum, meta) => sum + Number(meta.amount),
              0
            ) === amount)
      ),
      isValidAttributes: Boolean(
        attributes.length <= 10 &&
          attributes.every(
            (attr) => attr.trait_type.trim() && attr.value.trim()
          ) &&
          new Set(attributes.map((attr) => attr.trait_type.trim())).size ===
            attributes.length
      ),
      isValidBatch: Boolean(
        !isAllowlistMint &&
          batchMetadata.length <= 50 &&
          batchMetadata.every(
            (meta) =>
              Number(meta.amount) >= 1 &&
              Number(meta.amount) <=
                Math.min(
                  collection?.mintLimit ? Number(collection.mintLimit) : 100,
                  Number(collection?.maxSupply) -
                    Number(collection?.totalMinted)
                ) &&
              meta.name.trim() &&
              meta.description.trim() &&
              /^(ipfs|https):\/\//.test(meta.image || "")
          )
      ),
      isValidSignature: Boolean(
        !isAllowlistMint ||
          (signature && /^0x[0-9a-fA-F]{130}$/.test(signature))
      ),
      isValidNonce: Boolean(
        !isAllowlistMint ||
          (nonce && Number.isInteger(Number(nonce)) && Number(nonce) >= 0)
      ),
    }),
    [
      isSameArtType,
      imageFile,
      balance,
      mintCostData,
      amount,
      collection,
      attributes,
      name,
      description,
      batchMetadata,
      isAllowlistMint,
      signature,
      nonce,
    ]
  );

  const getButtonConfig = (): ButtonConfig[] => {
    if (!collection) {
      return [
        {
          text: "Loading...",
          disabled: true,
          variant: "secondary",
          icon: <Loader2 className="mr-2 h-4 w-4 animate-spin" />,
        },
      ];
    }

    if (!isConnected) {
      return [
        {
          text: "Kết nối ví",
          disabled: false,
          variant: "default",
          icon: <Wallet className="mr-2 h-4 w-4" />,
          onClick: () => connect({ connector: metaMask() }),
        },
      ];
    }

    if (Number(collection.totalMinted) >= Number(collection.maxSupply)) {
      return [
        {
          text: "Đã bán hết",
          disabled: true,
          variant: "destructive",
          icon: <AlertTriangle className="mr-2 h-4 w-4" />,
        },
      ];
    }

    if (!mintCostData?.getMintCost.success) {
      return [
        {
          text: `Mint ${currentAmount} NFT${
            currentAmount > 1 ? "s" : ""
          } (Gas: ${Number(lastMintCost.estimatedGas).toFixed(6)} ETH)`,
          disabled: true,
          variant: "secondary",
        },
      ];
    }

    if (!validations.isValidImage) {
      return [
        {
          text: isSameArtType
            ? "Collection image not available"
            : "Upload a valid image or use batch upload",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!validations.isValidAttributes) {
      return [
        {
          text: "Complete required attribute fields",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!validations.isValidAmount) {
      return [
        {
          text: `Invalid amount (max ${Math.min(
            collection?.mintLimit ? Number(collection.mintLimit) : 100,
            Number(collection?.maxSupply) - Number(collection?.totalMinted)
          )}, batch must match amount)`,
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (
      isAllowlistMint &&
      (!validations.isValidSignature || !validations.isValidNonce)
    ) {
      return [
        {
          text: "Provide valid signature and nonce",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (!agreedToTerms) {
      return [
        {
          text: "Accept terms of service",
          disabled: true,
          variant: "secondary",
        },
      ];
    }

    if (
      !validations.hasSufficientBalance &&
      mintCostData?.getMintCost.success
    ) {
      return [
        {
          text: `Insufficient balance (${currentAmount} NFT${
            currentAmount > 1 ? "s" : ""
          } - Gas: ${Number(mintCostData.getMintCost.estimatedGas).toFixed(
            6
          )} ETH)`,
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    if (isAllowlistMint && batchMetadata.length > 0) {
      return [
        {
          text: "Batch mint not supported for allowlist",
          disabled: true,
          variant: "destructive",
        },
      ];
    }

    const buttons: ButtonConfig[] = [
      {
        text: `Mint ${currentAmount} NFT${currentAmount > 1 ? "s" : ""} (Gas: ${
          mintCostData?.getMintCost.estimatedGas
            ? Number(mintCostData.getMintCost.estimatedGas).toFixed(6)
            : Number(lastMintCost.estimatedGas).toFixed(6)
        } ETH)`,
        disabled: isEstimating || mintCostLoading || isCalculating,
        variant: "default",
        icon: <Sparkles className="mr-2 h-4 w-4" />,
        onClick: () => {
          setIsEstimating(true);
          onConfirm(false);
          setIsEstimating(false);
        },
      },
    ];

    if (
      !isSameArtType &&
      batchMetadata.length > 0 &&
      validations.isValidBatch
    ) {
      buttons.push({
        text: `Batch Mint ${currentAmount} NFT${
          currentAmount > 1 ? "s" : ""
        } (Gas: ${
          mintCostData?.getMintCost.estimatedGas
            ? Number(mintCostData.getMintCost.estimatedGas).toFixed(6)
            : Number(lastMintCost.estimatedGas).toFixed(6)
        } ETH)`,
        disabled: isEstimating || mintCostLoading || isCalculating,
        variant: "default",
        icon: <Package className="mr-2 h-4 w-4" />,
        onClick: () => {
          setIsEstimating(true);
          onConfirm(true);
          setIsEstimating(false);
        },
      });
    }

    return buttons;
  };

  return (
    <div className="flex flex-col gap-2">
      {getButtonConfig().map((config, index) => (
        <Button
          key={index}
          id={config.id}
          variant={config.variant || "default"}
          disabled={config.disabled}
          onClick={config.onClick}
          className={cn(
            "h-10 text-sm font-medium",
            config.variant === "default" &&
              "bg-pink-500 hover:bg-pink-600 text-white dark:bg-pink-600 dark:hover:bg-pink-700",
            config.variant === "destructive" &&
              "bg-red-500 hover:bg-red-600 text-white dark:bg-red-600 dark:hover:bg-red-700",
            config.variant === "secondary" &&
              "bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300",
            config.disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          {config.icon}
          {config.text}
        </Button>
      ))}
    </div>
  );
};

export default MintButton;
