import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Nft } from "@/lib/api/graphql/generated";

interface MyItemsState {
  nfts: Nft[];
  selectedNFTs: string[];
  isLoading: boolean;
  error: string | null;
}

const initialState: MyItemsState = {
  nfts: [],
  selectedNFTs: [],
  isLoading: false,
  error: null,
};

const myItemsSlice = createSlice({
  name: "myItems",
  initialState,
  reducers: {
    setNFTs: (state, action: PayloadAction<Nft[]>) => {
      state.nfts = action.payload;
    },
    addNFT: (state, action: PayloadAction<Nft>) => {
      const existingIndex = state.nfts.findIndex(
        (nft) => nft.id === action.payload.id
      );
      if (existingIndex === -1) {
        state.nfts = [action.payload, ...state.nfts];
      }
    },
    removeNFT: (state, action: PayloadAction<string>) => {
      state.nfts = state.nfts.filter((nft) => nft.id !== action.payload);
      state.selectedNFTs = state.selectedNFTs.filter(
        (id) => id !== action.payload
      );
    },
    updateNFT: (state, action: PayloadAction<Nft>) => {
      state.nfts = state.nfts.map((nft) =>
        nft.id === action.payload.id ? action.payload : nft
      );
    },
    toggleNFTSelection: (state, action: PayloadAction<string>) => {
      const index = state.selectedNFTs.indexOf(action.payload);
      if (index === -1) {
        state.selectedNFTs = [...state.selectedNFTs, action.payload];
      } else {
        state.selectedNFTs = state.selectedNFTs.filter(
          (id) => id !== action.payload
        );
      }
    },
    clearSelectedNFTs: (state) => {
      state.selectedNFTs = [];
    },
    setSelectedNFTs: (state, action: PayloadAction<string[]>) => {
      state.selectedNFTs = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    addMultipleNFTs: (state, action: PayloadAction<Nft[]>) => {
      const existingIds = new Set(state.nfts.map((nft) => nft.id));
      const newNFTs = action.payload.filter((nft) => !existingIds.has(nft.id));
      state.nfts = [...newNFTs, ...state.nfts];
    },
    updateMultipleNFTs: (state, action: PayloadAction<Nft[]>) => {
      const updatedMap = new Map(action.payload.map((nft) => [nft.id, nft]));
      state.nfts = state.nfts.map((nft) =>
        updatedMap.has(nft.id) ? updatedMap.get(nft.id)! : nft
      );
    },
    removeMultipleNFTs: (state, action: PayloadAction<string[]>) => {
      const idsToRemove = new Set(action.payload);
      state.nfts = state.nfts.filter((nft) => !idsToRemove.has(nft.id));
      state.selectedNFTs = state.selectedNFTs.filter(
        (id) => !idsToRemove.has(id)
      );
    },
  },
});

export const {
  setNFTs,
  addNFT,
  removeNFT,
  updateNFT,
  toggleNFTSelection,
  clearSelectedNFTs,
  setSelectedNFTs,
  setLoading,
  setError,
  addMultipleNFTs,
  updateMultipleNFTs,
  removeMultipleNFTs,
} = myItemsSlice.actions;

export default myItemsSlice.reducer;
