import { Collection, Stats } from "@/lib/api/graphql/generated";
import { Chain } from "@/lib/constant/chains";
import { z } from "zod";

export const formSchema = z
  .object({
    chain: z.string().min(1, "Chain is required"),
    chainId: z.string().min(1, "Chain ID is required"),
    name: z.string().min(1, "Name is required"),
    description: z.string().min(1, "Description is required"),
    artType: z.enum(["SAME", "UNIQUE"]).optional(),
    uri: z.string().optional(),
    collectionImageUrl: z.string().optional(),
    tokenStandard: z.enum(["ERC721", "ERC1155"]).optional(),
    mintPrice: z.string().optional(),
    royaltyFee: z.string().optional(),
    maxSupply: z.string().optional(),
    mintLimit: z.string().optional(),
    mintStartDate: z.string(),
    allowlistStages: z
      .array(
        z.object({
          mintPrice: z
            .string()
            .regex(/^\d+(\.\d+)?$/, "Mint price must be a valid number")
            .refine(
              (val) => parseFloat(val) >= 0,
              "Mint price cannot be negative"
            ),
          startDate: z.string(),
          durationDays: z
            .string()
            .regex(/^\d+$/, "Duration days must be a number")
            .refine(
              (val) => parseInt(val) >= 0,
              "Duration days cannot be negative"
            ),
          durationHours: z
            .string()
            .regex(/^\d+$/, "Duration hours must be a number")
            .refine(
              (val) => parseInt(val) >= 0,
              "Duration hours cannot be negative"
            ),
          wallets: z
            .array(
              z.string().length(42, "Wallet address must be 42 characters")
            )
            .min(1, "At least one wallet is required"),
        })
      )
      .optional(),
    publicMint: z.object({
      mintPrice: z
        .string()
        .regex(/^\d+(\.\d+)?$/, "Mint price must be a valid number")
        .refine((val) => parseFloat(val) >= 0, "Mint price cannot be negative"),
      durationDays: z
        .string()
        .regex(/^\d+$/, "Duration days must be a number")
        .refine(
          (val) => parseInt(val) >= 0,
          "Duration days cannot be negative"
        ),
      durationHours: z
        .string()
        .regex(/^\d+$/, "Duration hours must be a number")
        .refine(
          (val) => parseInt(val) >= 0,
          "Duration hours cannot be negative"
        ),
      startDate: z.string(),
    }),
  })
  .superRefine((data, ctx) => {
    const mintStartDate = new Date(data.mintStartDate);

    // Check publicMint startDate
    if (new Date(data.publicMint.startDate) < mintStartDate) {
      ctx.addIssue({
        path: ["publicMint", "startDate"],
        code: z.ZodIssueCode.custom,
        message: "Start date must be after or equal to mint start date",
      });
    }

    // Check each allowlistStage startDate
    data.allowlistStages?.forEach((stage, index) => {
      if (new Date(stage.startDate) < mintStartDate) {
        ctx.addIssue({
          path: ["allowlistStages", index, "startDate"],
          code: z.ZodIssueCode.custom,
          message: "Start date must be after or equal to mint start date",
        });
      }
    });
  });

export type FormData = z.infer<typeof formSchema>;

export type StepStatus = "pending" | "processing" | "completed";

export interface PublishCollectionModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  step1Status: StepStatus;
  step2Status: StepStatus;
}

export interface HomeState {
  collections: Collection[];
  stats: Stats;
  selectedChain: Chain | null;
  isLoading: boolean;
  error: string | null;
}

export interface SlideCollection {
  title: string;
  description: string;
  image: string;
  color1: string;
  color2: string;
}
