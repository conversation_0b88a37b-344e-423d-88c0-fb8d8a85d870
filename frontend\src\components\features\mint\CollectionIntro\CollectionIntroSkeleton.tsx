import { Skeleton } from "@/components/ui/skeleton";

export function CollectionIntroSkeleton() {
  return (
    <div className="w-full">
      <div className="border-b border-gray-200 dark:border-gray-800/30">
        <div className="bg-transparent h-auto p-0">
          <Skeleton className="h-8 w-24" />
        </div>
      </div>

      <div className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Skeleton className="h-7 w-48" />
            <div className="flex space-x-3">
              <Skeleton className="h-4 w-64" />
            </div>
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>

          <div className="space-y-4">
            <Skeleton className="h-7 w-24" />
            <div className="space-y-3">
              <div>
                <Skeleton className="h-4 w-48 mb-1" />
                <Skeleton className="h-3 w-32" />
              </div>
              <div>
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-3 w-64" />
              </div>
              <div>
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-3 w-64" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
