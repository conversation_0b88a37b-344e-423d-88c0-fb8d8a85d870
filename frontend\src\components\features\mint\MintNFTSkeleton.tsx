import { CollectionIntroSkeleton } from "@/components/features/mint/CollectionIntro/CollectionIntroSkeleton";
import { CollectionMediaShowcaseSkeleton } from "@/components/features/mint/CollectionMediaShowcase/CollectionMediaShowcaseSkeleton";
import { MintPanelSkeleton } from "@/components/features/mint/MintPanel/MintPanelSkeleton";
import { ExploreButtonSkeleton } from "@/components/features/mint/ExploreButton/ExploreButtonSkeleton";
export function MintNFTSkeleton() {
  return (
    <>
      <div className="flex xl:flex-row items-center justify-center gap-10 xl:gap-50 flex-col mb-20">
        <CollectionMediaShowcaseSkeleton />
        <div className="flex flex-col gap-5">
          <MintPanelSkeleton />
          <ExploreButtonSkeleton />
        </div>
      </div>
      <CollectionIntroSkeleton />
    </>
  );
}
