export const isMintActive = (
  startDate: string,
  durationDays: string
): boolean => {
  if (!startDate || !durationDays) return false;
  const start = new Date(startDate);
  const end = new Date(
    start.getTime() + parseInt(durationDays) * 24 * 60 * 60 * 1000
  ); // Tính end bằng milliseconds
  const now = new Date();
  return now >= start && now <= end;
};

export const getTimeUntilMint = (startDate: string): string | undefined => {
  if (!startDate) return undefined;
  const now = new Date();
  const start = new Date(startDate);
  if (now < start) {
    const diff = start.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    return `${days}d ${hours}h`;
  }
  return undefined;
};
