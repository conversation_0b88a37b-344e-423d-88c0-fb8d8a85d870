import { Suspense } from "react";
import { HomeContent } from "../components/features/home";
import { ChainTabs } from "../components/features/home/<USER>";
import { ChainTabsSkeleton } from "@/components/features/home/<USER>/ChainTabsSkeleton";

export const metadata = {
  title: "NFT Marketplace | Explore Collections Across Multiple Chains",
  description:
    "Discover, collect, and sell extraordinary NFTs across multiple blockchains",
};

export default function Home() {
  return (
    <div className="space-y-8">
      {/* Wrap ChainTabs in a Suspense boundary */}
      <Suspense fallback={<ChainTabsSkeleton />}>
        <ChainTabs />
      </Suspense>
      <HomeContent />
    </div>
  );
}
