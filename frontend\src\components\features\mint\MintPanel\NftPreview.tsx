import type {
  AttributeInput,
  Collection,
  NftMetadataInput,
} from "@/lib/api/graphql/generated";
import { Sparkles } from "lucide-react";
import Image from "next/image";

interface NftPreviewProps {
  imageFile?: string | null;
  name: string;
  description: string;
  attributes: AttributeInput[];
  isSameArtType: boolean;
  collection: Collection;
  batchMetadata?: NftMetadataInput[];
  currentGalleryImage?: string;
}

export function NftPreview({
  imageFile,
  name,
  description,
  attributes,
  isSameArtType,
  collection,
  batchMetadata = [],
  currentGalleryImage,
}: NftPreviewProps) {
  if (!collection) return null;

  const previewImage = isSameArtType
    ? currentGalleryImage || collection.image
    : imageFile;
  const previewName = isSameArtType
    ? name.trim() ||
      `${collection.name} #${Number(collection.totalMinted || 0) + 1}`
    : name.trim() || "NFT Name";
  const previewDescription = isSameArtType
    ? description.trim() || collection.description || "No description"
    : description.trim() || "No description";
  const previewAttributes = attributes.filter(
    (attr) => attr.trait_type.trim() && attr.value.trim()
  );

  return (
    <div className="bg-white border-gray-200 dark:bg-[#1a1625] rounded-lg overflow-hidden border dark:border-gray-800/50">
      <div className="p-4 bg-gray-50 dark:bg-[#0f0a19] flex justify-between items-center">
        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
          NFT Preview
        </h3>
        <div
          className={`px-2 py-1 text-xs rounded-full ${
            isSameArtType
              ? "bg-blue-100 text-blue-600 border border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800/50"
              : "bg-purple-100 text-purple-600 border border-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-800/50"
          }`}
        >
          {isSameArtType ? "Same Art" : "Unique Art"}
        </div>
      </div>

      <div className="p-4">
        {batchMetadata.length > 0 && !isSameArtType ? (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              Batch Preview ({batchMetadata.length} NFTs)
            </h4>
            <div className="grid grid-cols-2 gap-3">
              {batchMetadata.map((meta, index) => (
                <div key={index} className="space-y-2">
                  <div className="relative w-full h-32 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800/50">
                    <Image
                      src={meta.image || "/placeholder.svg"}
                      alt={meta.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                      {meta.name || `NFT #${index + 1}`}
                    </h5>
                    <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                      {meta.description || "No description"}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <>
            {previewImage ? (
              <div className="relative w-full h-64 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800/50 mb-4">
                <Image
                  src={previewImage || "/placeholder.svg"}
                  alt="NFT preview"
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            ) : (
              <div className="w-full h-64 rounded-lg border border-gray-200 dark:border-gray-800/50 mb-4 flex items-center justify-center bg-gray-50 dark:bg-[#0f0a19]">
                <p className="text-gray-500">No image available</p>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  {previewName}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-3">
                  {previewDescription}
                </p>
              </div>

              {previewAttributes.length > 0 ? (
                <div>
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Attributes
                  </h5>
                  <div className="grid grid-cols-2 gap-2">
                    {previewAttributes.map((attr, index) => (
                      <div
                        key={index}
                        className="bg-gray-50 dark:bg-[#0f0a19] rounded-md p-2 border border-gray-200/70 dark:border-gray-800/30"
                      >
                        <span className="text-gray-600 dark:text-gray-400 text-xs block">
                          {attr.trait_type}
                        </span>
                        <span className="text-gray-900 dark:text-white text-xs font-medium">
                          {attr.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="bg-gray-50 dark:bg-[#0f0a19] rounded-md p-3 border border-gray-200/70 dark:border-gray-800/30">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                    <p className="text-xs text-gray-700 dark:text-gray-300">
                      No attributes added yet
                    </p>
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        <div
          className={`p-3 rounded-md ${
            isSameArtType
              ? "bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800/50"
              : "bg-purple-50 border border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50"
          }`}
        >
          <p className="text-xs text-gray-700 dark:text-gray-300">
            <span className="font-medium">
              {collection.name} #{Number(collection.totalMinted || 0) + 1}
            </span>
            <br />
            {isSameArtType
              ? "Same Art Type: All NFTs share the same image from the collection. You can customize name, description, and attributes (defaults to collection metadata)."
              : "Unique Art Type: Each NFT requires a unique image and metadata. Use batch upload for multiple NFTs."}
          </p>
        </div>
      </div>
    </div>
  );
}
